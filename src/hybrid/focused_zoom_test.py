#!/usr/bin/env python3
"""
Focused Zoom Tracking Test
Pure focus on human detection + zoom tracking ID preservation
No safety zones, no equipment detection - just the core algorithm test
"""

import cv2
import numpy as np
from ultralytics import YOLO
from deep_sort_realtime.deepsort_tracker import DeepSort
import time

# Import our proven zoom wrapper
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))
from src.core.zoom_tracking_wrapper import ZoomTrackingWrapper


class FocusedZoomTest:
    """
    Focused test for zoom tracking with human detection
    Goal: Validate that our zoom wrapper preserves track IDs during zoom operations
    """
    
    def __init__(self, video_path: str = "videos/test2.mp4"):
        print("🎯 FOCUSED ZOOM TRACKING TEST")
        print("Testing CORE algorithm: ID preservation during zoom")
        print("=" * 60)
        
        self.video_path = video_path
        
        # Detection model - focus on humans only
        print("🔍 Loading YOLO for human detection...")
        self.model = YOLO('yolo11n.pt')
        
        # Base tracker with stable configuration
        print("🎯 Initializing DeepSORT...")
        base_tracker = DeepSort(
            max_age=50, 
            n_init=3,
            embedder="mobilenet",
            half=False,
            embedder_gpu=False
        )
        
        # Our PROVEN zoom wrapper
        print("🚀 Initializing ZOOM WRAPPER (50.8% improvement)...")
        self.tracker = ZoomTrackingWrapper(base_tracker)
        
        # Zoom simulation parameters
        self.zoom_level = 1.0
        self.target_zoom = 1.0
        self.zoom_speed = 0.05
        self.zoom_in_progress = False
        
        # Statistics
        self.frame_count = 0
        self.detection_count = 0
        self.track_count = 0
        self.zoom_events = 0
        self.start_time = time.time()
        
        print("✅ Focused zoom test ready!")
        print(f"   Video: {video_path}")
        print(f"   Focus: Human detection + zoom ID preservation")
    
    def run(self):
        """Run the focused zoom tracking test"""
        cap = cv2.VideoCapture(self.video_path)
        if not cap.isOpened():
            print(f"❌ Could not open video: {self.video_path}")
            return
        
        print("\n🎬 STARTING FOCUSED ZOOM TEST")
        print("Controls:")
        print("  'z' = Zoom In (test ID preservation)")
        print("  'x' = Zoom Out")
        print("  'r' = Reset zoom")
        print("  'q' = Quit")
        print("\n🔍 Focus: Watch track IDs during zoom operations...")
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # Loop video
                    continue
                
                self.frame_count += 1
                
                # Update zoom simulation
                self._update_zoom()
                
                # Apply zoom to frame
                zoomed_frame = self._simulate_zoom(frame, self.zoom_level)
                
                # HUMAN DETECTION ONLY
                results = self.model(zoomed_frame, verbose=False, conf=0.3, classes=[0])  # Person class
                
                # Convert to DeepSORT format
                detections = self._convert_yolo_detections(results)
                self.detection_count += len(detections)
                
                # ZOOM WRAPPER TRACKING
                tracks = self.tracker.update(zoomed_frame, detections, self.zoom_level)
                self.track_count += len(tracks)
                
                # Visualize results
                display_frame = self._draw_tracking_results(zoomed_frame, tracks, detections)
                
                # Show performance stats
                if self.frame_count % 30 == 0:  # Every second
                    self._print_stats()
                
                # Display
                cv2.imshow('Focused Zoom Tracking Test', display_frame)
                
                # Handle controls
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('z'):
                    self._zoom_in()
                elif key == ord('x'):
                    self._zoom_out()
                elif key == ord('r'):
                    self._reset_zoom()
        
        finally:
            cap.release()
            cv2.destroyAllWindows()
            self._print_final_stats()
    
    def _convert_yolo_detections(self, results):
        """Convert YOLO to DeepSORT format - CORRECTED VERSION"""
        detections = []
        for result in results:
            if result.boxes is not None:
                for box in result.boxes:
                    if hasattr(box.xyxy[0], 'cpu'):
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        conf = box.conf[0].cpu().numpy()
                    else:
                        x1, y1, x2, y2 = box.xyxy[0]
                        conf = box.conf[0]
                    
                    # Convert XYXY to LTWH for DeepSORT
                    left = int(x1)
                    top = int(y1)
                    width = int(x2 - x1)
                    height = int(y2 - y1)
                    
                    # DeepSORT format: ([left, top, width, height], confidence, class)
                    detection = ([left, top, width, height], float(conf), 0)
                    detections.append(detection)
        return detections
    
    def _simulate_zoom(self, frame, zoom_level):
        """Simulate PTZ zoom focusing on center"""
        if zoom_level <= 1.0:
            return frame
        
        h, w = frame.shape[:2]
        crop_w = int(w / zoom_level)
        crop_h = int(h / zoom_level)
        
        # Center crop
        start_x = (w - crop_w) // 2
        start_y = (h - crop_h) // 2
        
        start_x = max(0, min(start_x, w - crop_w))
        start_y = max(0, min(start_y, h - crop_h))
        
        cropped = frame[start_y:start_y+crop_h, start_x:start_x+crop_w]
        zoomed = cv2.resize(cropped, (w, h))
        
        return zoomed
    
    def _draw_tracking_results(self, frame, tracks, detections):
        """Draw tracking results with focus on ID preservation"""
        display_frame = frame.copy()
        
        # Draw detections in blue
        for detection in detections:
            bbox, conf, cls = detection
            left, top, width, height = bbox
            cv2.rectangle(display_frame, (left, top), (left + width, top + height), (255, 0, 0), 2)
            cv2.putText(display_frame, f'Det: {conf:.2f}', (left, top - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
        
        # Draw tracks in green with LARGE track IDs
        for track in tracks:
            if hasattr(track, 'to_ltrb'):
                x1, y1, x2, y2 = track.to_ltrb()
            elif hasattr(track, 'tlbr'):
                x1, y1, x2, y2 = track.tlbr
            else:
                continue
            
            # Draw track box in green
            cv2.rectangle(display_frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 3)
            
            # Draw LARGE track ID
            track_id = track.track_id
            cv2.putText(display_frame, f'ID: {track_id}', (int(x1), int(y1) - 15), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 0), 3)
        
        # Add zoom level indicator
        zoom_text = f'Zoom: {self.zoom_level:.1f}x'
        cv2.putText(display_frame, zoom_text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 255), 2)
        
        # Add detection/track counts
        stats_text = f'Detections: {len(detections)} | Tracks: {len(tracks)}'
        cv2.putText(display_frame, stats_text, (10, 60), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        return display_frame
    
    def _update_zoom(self):
        """Update zoom level smoothly"""
        if abs(self.zoom_level - self.target_zoom) > 0.01:
            if self.zoom_level < self.target_zoom:
                self.zoom_level = min(self.target_zoom, self.zoom_level + self.zoom_speed)
            else:
                self.zoom_level = max(self.target_zoom, self.zoom_level - self.zoom_speed)
            self.zoom_in_progress = True
        else:
            self.zoom_in_progress = False
    
    def _zoom_in(self):
        """Zoom in - this is where we test ID preservation"""
        self.target_zoom = min(4.0, self.target_zoom + 0.5)
        self.zoom_events += 1
        print(f"🔍 ZOOM IN to {self.target_zoom:.1f}x - Testing ID preservation...")
    
    def _zoom_out(self):
        """Zoom out"""
        self.target_zoom = max(1.0, self.target_zoom - 0.5)
        print(f"🔍 ZOOM OUT to {self.target_zoom:.1f}x")
    
    def _reset_zoom(self):
        """Reset zoom to 1x"""
        self.target_zoom = 1.0
        print("🔍 ZOOM RESET to 1.0x")
    
    def _print_stats(self):
        """Print performance statistics"""
        elapsed = time.time() - self.start_time
        fps = self.frame_count / elapsed if elapsed > 0 else 0
        
        print(f"📊 Frame {self.frame_count}: {fps:.1f} FPS | "
              f"Detections: {self.detection_count} | Tracks: {self.track_count} | "
              f"Zoom Events: {self.zoom_events}")
    
    def _print_final_stats(self):
        """Print final test results"""
        elapsed = time.time() - self.start_time
        avg_fps = self.frame_count / elapsed if elapsed > 0 else 0
        
        print("\n🎯 FOCUSED ZOOM TEST RESULTS")
        print("=" * 50)
        print(f"📊 Total frames processed: {self.frame_count}")
        print(f"⏱️  Average FPS: {avg_fps:.1f}")
        print(f"🎯 Total detections: {self.detection_count}")
        print(f"🔗 Total tracks: {self.track_count}")
        print(f"🔍 Zoom events tested: {self.zoom_events}")
        print(f"✅ Test completed successfully!")


if __name__ == "__main__":
    test = FocusedZoomTest("videos/test2.mp4")
    test.run()

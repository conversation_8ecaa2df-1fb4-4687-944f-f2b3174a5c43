"""
Hybrid Zoom + Re-ID Tracking System

This module builds upon the PROVEN zoom tracking wrapper (50.8% improvement)
and integrates it with the Re-ID Energy Matrix for comprehensive tracking.

NARRATIVE PRESERVATION:
- Original zoom wrapper achievements: ✅ PROVEN & DOCUMENTED
- This hybrid approach: 🧪 EXPERIMENTAL ENHANCEMENT
- Goal: Combine fast zoom response + deep re-ID memory

Architecture:
- ZoomTrackingWrapper: Handles zoom events (13% of problems)
- ReIDEnergyMatrix: Handles occlusions & re-entries (87% of problems)  
- DecisionFusion: Intelligent routing between systems
"""

from .hybrid_tracker import HybridTracker
from .decision_fusion import DecisionFusion
from .reid_integration import ReIDIntegration

__version__ = "0.1.0-experimental"
__all__ = ["HybridTracker", "DecisionFusion", "ReIDIntegration"]

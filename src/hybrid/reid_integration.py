"""
Re-ID Integration: Simplified Re-ID Matrix for Hybrid System

This integrates the Re-ID Energy Matrix concepts into the hybrid tracker
while maintaining the mathematical foundations from the original re-id.md.

Mathematical Foundation:
- Exponential decay: weight = exp(-frame_age / τ)
- Energy boost: similarity = cosine * (1 + λ * energy_match)
- Variance-based Laplacian: energy = var(laplacian)
"""

import numpy as np
import cv2
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
import time


@dataclass
class TrackMemory:
    """Simplified track memory for hybrid system"""
    track_id: int
    features: np.ndarray
    laplacian_energy: float
    bbox: List[int]
    frame_number: int
    weight: float
    zoom_level: float


class ReIDIntegration:
    """
    Simplified Re-ID integration for hybrid tracker
    
    Based on the mathematical foundations from re-id.md but optimized
    for integration with the proven zoom wrapper.
    """
    
    def __init__(self, cache_size: int = 50, decay_tau: float = 20.0, 
                 similarity_threshold: float = 0.7, lambda_energy: float = 0.3):
        """
        Initialize Re-ID integration with conservative parameters
        
        Args:
            cache_size: Smaller cache for hybrid system (50 vs 150)
            decay_tau: Faster decay for hybrid system (20 vs 30)
            similarity_threshold: Same threshold as original
            lambda_energy: Same energy boost as original
        """
        print("🧠 Re-ID Integration initialized (hybrid mode)")
        
        # Parameters (more conservative than full re-ID system)
        self.cache_size = cache_size
        self.decay_tau = decay_tau
        self.similarity_threshold = similarity_threshold
        self.lambda_energy = lambda_energy
        
        # Memory components (simplified from original M_* matrices)
        self.track_memory: List[TrackMemory] = []
        self.frame_count = 0
        
        # Performance tracking
        self.stats = {
            'enhancements_applied': 0,
            'tracks_recovered': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        print(f"   Cache size: {cache_size} (hybrid optimized)")
        print(f"   Decay tau: {decay_tau} frames")
        print(f"   Similarity threshold: {similarity_threshold}")
        print(f"   Energy lambda: {lambda_energy}")
    
    def enhance_tracks(self, wrapper_tracks: List[Any], frame: np.ndarray, 
                      zoom_level: float, weight: float = 1.0, 
                      threshold: float = 0.7) -> List[Any]:
        """
        Enhance wrapper tracks using re-ID memory
        
        Args:
            wrapper_tracks: Tracks from zoom wrapper
            frame: Current frame
            zoom_level: Current zoom level
            weight: Re-ID influence weight (scenario-dependent)
            threshold: Similarity threshold (scenario-dependent)
            
        Returns:
            Enhanced tracks (may include recovered tracks)
        """
        self.frame_count += 1
        
        # Update memory with current tracks
        self._update_memory(wrapper_tracks, frame, zoom_level)
        
        # Apply decay and cleanup
        self._decay_memory()
        self._cleanup_memory()
        
        # Try to recover lost tracks using re-ID
        enhanced_tracks = wrapper_tracks.copy()
        recovered_tracks = self._recover_lost_tracks(
            wrapper_tracks, frame, zoom_level, weight, threshold
        )
        
        if recovered_tracks:
            enhanced_tracks.extend(recovered_tracks)
            self.stats['tracks_recovered'] += len(recovered_tracks)
            self.stats['enhancements_applied'] += 1
        
        return enhanced_tracks
    
    def _update_memory(self, tracks: List[Any], frame: np.ndarray, zoom_level: float):
        """Update memory with current tracks"""
        current_track_ids = set()
        
        for track in tracks:
            track_id = getattr(track, 'track_id', 0)
            current_track_ids.add(track_id)
            
            # Extract features
            features = self._extract_features(frame, track)
            laplacian_energy = self._calculate_laplacian_energy(frame, track)
            bbox = self._extract_bbox(track)
            
            # Update existing memory or create new
            existing_idx = self._find_memory_index(track_id)
            
            if existing_idx >= 0:
                # Update existing memory with moving average
                memory = self.track_memory[existing_idx]
                alpha = 0.7  # Learning rate (same as original)
                
                memory.features = alpha * memory.features + (1 - alpha) * features
                memory.laplacian_energy = alpha * memory.laplacian_energy + (1 - alpha) * laplacian_energy
                memory.bbox = bbox
                memory.frame_number = self.frame_count
                memory.weight = 1.0  # Refresh weight
                memory.zoom_level = zoom_level
            else:
                # Create new memory
                new_memory = TrackMemory(
                    track_id=track_id,
                    features=features,
                    laplacian_energy=laplacian_energy,
                    bbox=bbox,
                    frame_number=self.frame_count,
                    weight=1.0,
                    zoom_level=zoom_level
                )
                self.track_memory.append(new_memory)
    
    def _recover_lost_tracks(self, current_tracks: List[Any], frame: np.ndarray,
                           zoom_level: float, weight: float, threshold: float) -> List[Any]:
        """
        Try to recover lost tracks using re-ID memory
        
        This is where the mathematical magic happens - using the same
        formulas as the original re-ID system but in hybrid context.
        """
        if not self.track_memory:
            return []
        
        current_positions = [self._extract_center(track) for track in current_tracks]
        recovered_tracks = []
        
        # Look for memory entries that might represent lost tracks
        for memory in self.track_memory:
            # Skip if weight too low (expired)
            if memory.weight < 0.1:
                continue
            
            # Check if this memory track is missing from current tracks
            memory_id = memory.track_id
            current_ids = [getattr(track, 'track_id', 0) for track in current_tracks]
            
            if memory_id not in current_ids:
                # This track is missing - try to recover it
                recovery_candidate = self._attempt_track_recovery(
                    memory, frame, current_positions, zoom_level, threshold
                )
                
                if recovery_candidate:
                    recovered_tracks.append(recovery_candidate)
                    self.stats['cache_hits'] += 1
                else:
                    self.stats['cache_misses'] += 1
        
        return recovered_tracks
    
    def _attempt_track_recovery(self, memory: TrackMemory, frame: np.ndarray,
                              current_positions: List[Tuple[float, float]],
                              zoom_level: float, threshold: float) -> Optional[Any]:
        """
        Attempt to recover a specific track using re-ID matching
        
        Uses the same mathematical approach as original re-ID system:
        - CNN feature similarity (cosine)
        - Laplacian energy boost
        - Spatial consistency check
        """
        # Predict where this track should be based on zoom
        zoom_ratio = zoom_level / memory.zoom_level
        predicted_center = self._predict_position(memory.bbox, zoom_ratio)
        
        # Check if predicted position conflicts with current tracks
        min_distance = float('inf')
        for current_pos in current_positions:
            distance = np.sqrt((predicted_center[0] - current_pos[0])**2 + 
                             (predicted_center[1] - current_pos[1])**2)
            min_distance = min(min_distance, distance)
        
        # If too close to existing tracks, don't recover (avoid duplicates)
        if min_distance < 80:  # pixels
            return None
        
        # Extract features from predicted region
        predicted_bbox = self._predict_bbox(memory.bbox, zoom_ratio, frame.shape)
        if not self._is_valid_bbox(predicted_bbox, frame.shape):
            return None
        
        current_features = self._extract_features_from_bbox(frame, predicted_bbox)
        current_energy = self._calculate_laplacian_energy_from_bbox(frame, predicted_bbox)
        
        # Calculate similarity using original re-ID formula
        similarity = self._compute_similarity(
            memory.features, current_features,
            memory.laplacian_energy, current_energy
        )
        
        # Weight by memory decay (same as original)
        weighted_similarity = similarity * memory.weight
        
        # Decide if similarity is high enough for recovery
        if weighted_similarity > threshold:
            # Create recovered track (simplified track object)
            recovered_track = self._create_recovered_track(
                memory.track_id, predicted_bbox, weighted_similarity
            )
            return recovered_track
        
        return None
    
    def _compute_similarity(self, feat1: np.ndarray, feat2: np.ndarray,
                          energy1: float, energy2: float) -> float:
        """
        Compute similarity using original re-ID formula
        
        Mathematical formula from re-id.md:
        similarity = cosine_sim * (1.0 + λ * energy_match)
        """
        # CNN feature similarity (cosine)
        norm1 = np.linalg.norm(feat1)
        norm2 = np.linalg.norm(feat2)
        
        if norm1 == 0 or norm2 == 0:
            cosine_sim = 0.0
        else:
            cosine_sim = np.dot(feat1, feat2) / (norm1 * norm2)
        
        # Laplacian energy matching (same as original)
        energy_diff = abs(energy1 - energy2)
        max_energy = max(energy1, energy2, 1.0)
        energy_match = 1.0 - (energy_diff / max_energy)
        
        # Combined similarity with energy boost (original formula)
        similarity = cosine_sim * (1.0 + self.lambda_energy * energy_match)
        
        return similarity
    
    def _extract_features(self, frame: np.ndarray, track: Any) -> np.ndarray:
        """Extract simplified CNN features (64-dim instead of 128)"""
        bbox = self._extract_bbox(track)
        return self._extract_features_from_bbox(frame, bbox)
    
    def _extract_features_from_bbox(self, frame: np.ndarray, bbox: List[int]) -> np.ndarray:
        """Extract features from bounding box region"""
        x1, y1, x2, y2 = bbox
        
        # Ensure valid bbox
        h, w = frame.shape[:2]
        x1, y1 = max(0, x1), max(0, y1)
        x2, y2 = min(w, x2), min(h, y2)
        
        if x2 <= x1 or y2 <= y1:
            return np.zeros(64)  # Simplified feature size
        
        # Extract ROI and resize
        roi = frame[y1:y2, x1:x2]
        roi_resized = cv2.resize(roi, (32, 64))  # Smaller for speed
        gray = cv2.cvtColor(roi_resized, cv2.COLOR_BGR2GRAY)
        
        # Simplified feature extraction (histogram + basic stats)
        hist = cv2.calcHist([gray], [0], None, [32], [0, 256])
        hist = hist.flatten() / (hist.sum() + 1e-7)
        
        # Basic statistics
        stats = np.array([
            np.mean(gray), np.std(gray),
            np.percentile(gray, 25), np.percentile(gray, 75)
        ])
        
        # Combine and pad to 64 dimensions
        features = np.concatenate([hist, stats])
        if len(features) > 64:
            features = features[:64]
        elif len(features) < 64:
            features = np.pad(features, (0, 64 - len(features)))
        
        # L2 normalize
        norm = np.linalg.norm(features)
        if norm > 0:
            features = features / norm
        
        return features
    
    def _extract_features(self, frame: np.ndarray, track: Any) -> np.ndarray:
        """Extract simplified CNN features"""
        bbox = self._extract_bbox(track)
        return self._extract_features_from_bbox(frame, bbox)

    def _calculate_laplacian_energy(self, frame: np.ndarray, track: Any) -> float:
        """Calculate Laplacian energy using original formula"""
        bbox = self._extract_bbox(track)
        return self._calculate_laplacian_energy_from_bbox(frame, bbox)
    
    def _calculate_laplacian_energy_from_bbox(self, frame: np.ndarray, bbox: List[int]) -> float:
        """
        Calculate Laplacian energy using original re-ID formula
        
        Mathematical formula from re-id.md:
        energy = var(laplacian)
        """
        x1, y1, x2, y2 = bbox
        
        # Ensure valid bbox
        h, w = frame.shape[:2]
        x1, y1 = max(0, x1), max(0, y1)
        x2, y2 = min(w, x2), min(h, y2)
        
        if x2 <= x1 or y2 <= y1:
            return 0.0
        
        # Extract and process ROI
        roi = frame[y1:y2, x1:x2]
        gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        gray_resized = cv2.resize(gray, (32, 32))  # Smaller for speed
        
        # Guard against tiny crops (from original audit fix)
        if gray_resized.shape[0] < 8 or gray_resized.shape[1] < 8:
            return 0.0
        
        # Calculate Laplacian and energy (original formula)
        laplacian = cv2.Laplacian(gray_resized, cv2.CV_64F)
        energy = np.var(laplacian)  # Original formula: variance
        
        return float(energy)
    
    # Helper methods for track handling
    def _extract_bbox(self, track: Any) -> List[int]:
        """Extract bounding box from track"""
        if hasattr(track, 'to_ltrb'):
            return list(map(int, track.to_ltrb()))
        elif hasattr(track, 'tlbr'):
            return list(map(int, track.tlbr))
        else:
            return [0, 0, 100, 100]  # Default bbox
    
    def _extract_center(self, track: Any) -> Tuple[float, float]:
        """Extract center position from track"""
        bbox = self._extract_bbox(track)
        cx = (bbox[0] + bbox[2]) / 2
        cy = (bbox[1] + bbox[3]) / 2
        return (cx, cy)
    
    def _find_memory_index(self, track_id: int) -> int:
        """Find index of track in memory"""
        for i, memory in enumerate(self.track_memory):
            if memory.track_id == track_id:
                return i
        return -1
    
    def _decay_memory(self):
        """Apply exponential decay to memory weights (original formula)"""
        for memory in self.track_memory:
            frame_age = self.frame_count - memory.frame_number
            memory.weight = np.exp(-frame_age / self.decay_tau)
    
    def _cleanup_memory(self):
        """Remove expired entries from memory"""
        min_weight = 0.01
        self.track_memory = [m for m in self.track_memory if m.weight >= min_weight]
        
        # Limit cache size
        if len(self.track_memory) > self.cache_size:
            # Keep most recent entries
            self.track_memory.sort(key=lambda m: m.frame_number, reverse=True)
            self.track_memory = self.track_memory[:self.cache_size]
    
    def _predict_position(self, bbox: List[int], zoom_ratio: float) -> Tuple[float, float]:
        """Predict position after zoom"""
        cx = (bbox[0] + bbox[2]) / 2
        cy = (bbox[1] + bbox[3]) / 2
        return (cx * zoom_ratio, cy * zoom_ratio)
    
    def _predict_bbox(self, bbox: List[int], zoom_ratio: float, frame_shape: Tuple[int, int]) -> List[int]:
        """Predict bounding box after zoom"""
        x1, y1, x2, y2 = bbox
        
        # Scale coordinates
        x1_new = int(x1 * zoom_ratio)
        y1_new = int(y1 * zoom_ratio)
        x2_new = int(x2 * zoom_ratio)
        y2_new = int(y2 * zoom_ratio)
        
        # Ensure within frame bounds
        h, w = frame_shape[:2]
        x1_new = max(0, min(w-1, x1_new))
        y1_new = max(0, min(h-1, y1_new))
        x2_new = max(x1_new+1, min(w, x2_new))
        y2_new = max(y1_new+1, min(h, y2_new))
        
        return [x1_new, y1_new, x2_new, y2_new]
    
    def _is_valid_bbox(self, bbox: List[int], frame_shape: Tuple[int, int]) -> bool:
        """Check if bbox is valid"""
        x1, y1, x2, y2 = bbox
        h, w = frame_shape[:2]
        return (0 <= x1 < x2 <= w) and (0 <= y1 < y2 <= h) and (x2 - x1 > 10) and (y2 - y1 > 10)
    
    def _create_recovered_track(self, track_id: int, bbox: List[int], confidence: float) -> Any:
        """Create a simple recovered track object"""
        # This is a simplified track representation
        # In real implementation, this would match the base tracker's track format
        class RecoveredTrack:
            def __init__(self, track_id, bbox, confidence):
                self.track_id = track_id
                self.tlbr = bbox
                self.confidence = confidence
                self.is_recovered = True  # Flag to identify recovered tracks
        
        return RecoveredTrack(track_id, bbox, confidence)
    
    def _attempt_track_recovery(self, memory: TrackMemory, frame: np.ndarray,
                              current_positions: List[Tuple[float, float]],
                              zoom_level: float, threshold: float) -> Optional[Any]:
        """
        Attempt to recover a specific track using re-ID matching

        Uses the same mathematical approach as original re-ID system:
        - CNN feature similarity (cosine)
        - Laplacian energy boost
        - Spatial consistency check
        """
        # Predict where this track should be based on zoom
        zoom_ratio = zoom_level / memory.zoom_level
        predicted_center = self._predict_position(memory.bbox, zoom_ratio)

        # Check if predicted position conflicts with current tracks
        min_distance = float('inf')
        for current_pos in current_positions:
            distance = np.sqrt((predicted_center[0] - current_pos[0])**2 +
                             (predicted_center[1] - current_pos[1])**2)
            min_distance = min(min_distance, distance)

        # If too close to existing tracks, don't recover (avoid duplicates)
        if min_distance < 80:  # pixels
            return None

        # Extract features from predicted region
        predicted_bbox = self._predict_bbox(memory.bbox, zoom_ratio, frame.shape)
        if not self._is_valid_bbox(predicted_bbox, frame.shape):
            return None

        current_features = self._extract_features_from_bbox(frame, predicted_bbox)
        current_energy = self._calculate_laplacian_energy_from_bbox(frame, predicted_bbox)

        # Calculate similarity using original re-ID formula
        similarity = self._compute_similarity(
            memory.features, current_features,
            memory.laplacian_energy, current_energy
        )

        # Weight by memory decay (same as original)
        weighted_similarity = similarity * memory.weight

        # Decide if similarity is high enough for recovery
        if weighted_similarity > threshold:
            # Create recovered track (simplified track object)
            recovered_track = self._create_recovered_track(
                memory.track_id, predicted_bbox, weighted_similarity
            )
            return recovered_track

        return None

    def get_stats(self) -> Dict[str, Any]:
        """Get re-ID integration statistics"""
        return {
            'memory_size': len(self.track_memory),
            'enhancements_applied': self.stats['enhancements_applied'],
            'tracks_recovered': self.stats['tracks_recovered'],
            'cache_hit_rate': self.stats['cache_hits'] / max(1, self.stats['cache_hits'] + self.stats['cache_misses'])
        }

#!/usr/bin/env python3
"""
Test Detection Format Conversion
Simple validation that our YOLO->DeepSORT format conversion works correctly
"""

import numpy as np
from ultralytics import YOLO
import cv2

def test_yolo_to_deepsort_conversion():
    """Test that YOLO XYXY format converts correctly to DeepSORT LTWH format"""
    print("🧪 TESTING YOLO->DeepSORT FORMAT CONVERSION")
    print("=" * 50)
    
    # Load YOLO model
    print("🔍 Loading YOLO model...")
    model = YOLO('yolo11n.pt')
    
    # Create test frame
    test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # Run YOLO detection
    print("🎯 Running YOLO detection...")
    results = model(test_frame, verbose=False, conf=0.1, classes=[0])  # Person class only, low confidence
    
    # Convert using our function
    print("🔄 Converting YOLO format to DeepSORT format...")
    detections = convert_yolo_detections(results)
    
    print(f"✅ Converted {len(detections)} detections")
    
    # Validate format
    for i, detection in enumerate(detections):
        print(f"   Detection {i+1}: {detection}")
        
        # Validate structure: ([left, top, width, height], confidence, class)
        assert len(detection) == 3, f"Detection should have 3 elements, got {len(detection)}"
        bbox, conf, cls = detection
        
        assert len(bbox) == 4, f"Bbox should have 4 elements, got {len(bbox)}"
        assert isinstance(conf, float), f"Confidence should be float, got {type(conf)}"
        assert isinstance(cls, int), f"Class should be int, got {type(cls)}"
        
        # Validate bbox values are reasonable
        left, top, width, height = bbox
        assert width > 0, f"Width should be positive, got {width}"
        assert height > 0, f"Height should be positive, got {height}"
        assert left >= 0, f"Left should be non-negative, got {left}"
        assert top >= 0, f"Top should be non-negative, got {top}"
        
        print(f"      ✅ LTWH: [{left}, {top}, {width}, {height}], conf: {conf:.3f}, class: {cls}")
    
    print("🎉 FORMAT CONVERSION TEST PASSED!")
    return True

def convert_yolo_detections(results):
    """
    Convert YOLO results to DeepSORT compatible format
    
    CRITICAL: DeepSORT expects ([left, top, width, height], confidence, detection_class)
    YOLO provides: [x1, y1, x2, y2] (XYXY format)
    Conversion: width = x2 - x1, height = y2 - y1
    """
    detections = []

    for result in results:
        if result.boxes is not None:
            for box in result.boxes:
                # Handle both tensor and numpy array formats
                if hasattr(box.xyxy[0], 'cpu'):
                    # Tensor format
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    conf = box.conf[0].cpu().numpy()
                    cls = box.cls[0].cpu().numpy() if box.cls is not None else 0
                else:
                    # Already numpy array
                    x1, y1, x2, y2 = box.xyxy[0]
                    conf = box.conf[0]
                    cls = box.cls[0] if box.cls is not None else 0

                # Convert XYXY to LTWH format for DeepSORT
                left = int(x1)
                top = int(y1)
                width = int(x2 - x1)
                height = int(y2 - y1)
                
                # Create detection in CORRECT format expected by DeepSORT
                # Format: ([left, top, width, height], confidence, detection_class)
                detection = ([left, top, width, height], float(conf), int(cls))
                detections.append(detection)

    return detections

def convert_yolo_detections_all_classes(results):
    """Convert YOLO results for all classes (for debugging)"""
    detections = []
    for result in results:
        if result.boxes is not None:
            for box in result.boxes:
                if hasattr(box.xyxy[0], 'cpu'):
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    conf = box.conf[0].cpu().numpy()
                    cls = box.cls[0].cpu().numpy() if box.cls is not None else 0
                else:
                    x1, y1, x2, y2 = box.xyxy[0]
                    conf = box.conf[0]
                    cls = box.cls[0] if box.cls is not None else 0

                left = int(x1)
                top = int(y1)
                width = int(x2 - x1)
                height = int(y2 - y1)

                detection = ([left, top, width, height], float(conf), int(cls))
                detections.append(detection)
    return detections

def test_with_real_video():
    """Test format conversion with real oil rig video"""
    print("\n🛢️ TESTING WITH REAL OIL RIG VIDEO")
    print("=" * 50)
    
    video_path = "videos/oil_rig.mp4"
    
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print(f"❌ Could not open video: {video_path}")
            return False
        
        # Load YOLO model
        model = YOLO('yolo11n.pt')
        
        # Try multiple frames to find workers
        frame_count = 0
        max_frames_to_try = 10
        best_frame = None
        best_detections = []

        while frame_count < max_frames_to_try:
            ret, frame = cap.read()
            if not ret:
                break

            frame_count += 1
            print(f"📹 Trying frame {frame_count}: {frame.shape}")

            # Save first frame for inspection
            if frame_count == 1:
                cv2.imwrite("debug_frame.jpg", frame)
                print("💾 Saved debug_frame.jpg for inspection")

            # Run detection with very low confidence
            all_results = model(frame, verbose=False, conf=0.1)
            all_detections = convert_yolo_detections_all_classes(all_results)

            if len(all_detections) > len(best_detections):
                best_frame = frame.copy()
                best_detections = all_detections
                print(f"🎯 Frame {frame_count}: Found {len(all_detections)} detections!")

                # Show some detections with class names
                class_names = model.names  # Get YOLO class names
                for i, detection in enumerate(all_detections[:3]):
                    bbox, conf, cls = detection
                    left, top, width, height = bbox
                    class_name = class_names.get(cls, f"class_{cls}")
                    print(f"   Detection {i+1}: LTWH=[{left}, {top}, {width}, {height}], conf={conf:.3f}, class={cls} ({class_name})")

        print(f"📊 Best frame had {len(best_detections)} total detections")

        # Now test person-specific detection on best frame
        if best_frame is not None:
            person_results = model(best_frame, verbose=False, conf=0.2, classes=[0])
            detections = convert_yolo_detections(person_results)
            print(f"🎯 Found {len(detections)} person detections in best frame")
        else:
            detections = []
            print("❌ No good frame found")
        
        for i, detection in enumerate(detections):
            bbox, conf, cls = detection
            left, top, width, height = bbox
            print(f"   Worker {i+1}: LTWH=[{left}, {top}, {width}, {height}], conf={conf:.3f}")
        
        cap.release()
        print("✅ REAL VIDEO TEST PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing with real video: {e}")
        return False

if __name__ == "__main__":
    print("🚀 DETECTION FORMAT CONVERSION VALIDATION")
    print("Testing our YOLO->DeepSORT format fix")
    print("=" * 60)
    
    # Test 1: Basic format conversion
    success1 = test_yolo_to_deepsort_conversion()
    
    # Test 2: Real video test
    success2 = test_with_real_video()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Detection format conversion is working correctly")
        print("✅ Ready for hybrid system integration")
    else:
        print("\n❌ SOME TESTS FAILED")
        print("Need to fix format conversion issues")

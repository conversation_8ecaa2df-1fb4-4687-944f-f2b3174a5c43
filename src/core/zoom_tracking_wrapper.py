# zoom_tracking_wrapper.py
import numpy as np
import cv2
from collections import defaultdict
from scipy.spatial.distance import cosine
import time

class ZoomTrackingWrapper:
    """
    Lightweight wrapper that prevents ID switches during zoom.
    Works with ANY existing tracker (DeepSORT, ByteTrack, etc.)
    """
    
    def __init__(self, base_tracker):
        self.base_tracker = base_tracker
        self.track_signatures = {}  # track_id -> signature
        self.last_zoom = 1.0
        self.last_positions = {}  # track_id -> (x, y)
        
        # Lightweight Laplacian computer
        self.pyramid_levels = 3  # Minimal for speed
        
    def update(self, frame, detections=None, zoom_level=1.0):
        """
        Wrapper around any tracker's update method.
        Just fixes IDs during zoom, doesn't change tracking.
        """
        # 1. Let the base tracker do its job
        if hasattr(self.base_tracker, 'update_tracks'):
            # For trackers like DeepSORT
            tracks = self.base_tracker.update_tracks(detections, frame)
        elif hasattr(self.base_tracker, 'update'):
            # For other trackers
            tracks = self.base_tracker.update(detections, frame)
        else:
            # For OpenCV style trackers
            tracks = self._update_opencv_tracker(frame)
            
        # 2. Detect zoom change
        zoom_ratio = zoom_level / self.last_zoom
        is_zooming = abs(zoom_ratio - 1.0) > 0.05
        
        # 3. Fix IDs only during zoom
        if is_zooming and len(self.track_signatures) > 0:
            tracks = self._fix_zoom_ids(tracks, frame, zoom_ratio)
            
        # 4. Update our memory
        self._update_signatures(tracks, frame)
        self.last_zoom = zoom_level
        
        return tracks
    
    def _fix_zoom_ids(self, tracks, frame, zoom_ratio):
        """Core innovation: Fix ID switches caused by zoom"""
        
        # Build current signatures
        current_signatures = {}
        for track in tracks:
            sig = self._compute_signature(frame, track)
            current_signatures[track.track_id] = sig
            
        # Match with previous tracks and create ID mapping
        id_mapping = {}  # new_id -> old_id (what we want to restore)
        used_old_ids = set()

        for new_track in tracks:
            # Predict where this track should be after zoom
            best_match_id = None
            best_score = float('inf')  # Lower distance is better

            for old_id, old_pos in self.last_positions.items():
                if old_id in used_old_ids:
                    continue

                # Spatial check - where should old track be after zoom?
                predicted_x = old_pos[0] * zoom_ratio
                predicted_y = old_pos[1] * zoom_ratio

                # Get bounding box - handle different track formats
                if hasattr(new_track, 'to_ltrb'):
                    bbox = new_track.to_ltrb()
                    actual_x = bbox[0] + (bbox[2] - bbox[0])/2
                    actual_y = bbox[1] + (bbox[3] - bbox[1])/2
                elif hasattr(new_track, 'tlbr'):
                    actual_x = new_track.tlbr[0] + (new_track.tlbr[2] - new_track.tlbr[0])/2
                    actual_y = new_track.tlbr[1] + (new_track.tlbr[3] - new_track.tlbr[1])/2
                else:
                    # Fallback for other formats
                    actual_x, actual_y = 0, 0

                distance = np.sqrt((predicted_x - actual_x)**2 +
                                 (predicted_y - actual_y)**2)
                
                # Within reasonable distance?
                if distance < 150:  # pixels - more forgiving
                    # Check Laplacian signature if available
                    if old_id in self.track_signatures:
                        old_sig = self.track_signatures[old_id]
                        new_sig = current_signatures[new_track.track_id]

                        # Energy scales with zoom squared
                        expected_energy = old_sig['energy'] * (zoom_ratio ** 2)
                        actual_energy = new_sig['energy']

                        # How similar?
                        if max(expected_energy, actual_energy) > 0:
                            energy_ratio = min(expected_energy, actual_energy) / \
                                          max(expected_energy, actual_energy)
                        else:
                            energy_ratio = 0.5

                        # Combined score: spatial + signature
                        spatial_score = max(0, 1 - distance/150)
                        combined_score = (spatial_score * 0.7) + (energy_ratio * 0.3)

                        if combined_score < best_score:  # Lower is better
                            best_score = combined_score
                            best_match_id = old_id
                    else:
                        # No signature, use spatial only
                        if distance < best_score:
                            best_score = distance
                            best_match_id = old_id

            if best_match_id is not None:
                id_mapping[new_track.track_id] = best_match_id
                used_old_ids.add(best_match_id)
                
        # Apply ID corrections - restore persistent IDs
        for track in tracks:
            if track.track_id in id_mapping:
                old_id = id_mapping[track.track_id]
                print(f"🔄 Zoom wrapper: Restoring ID {track.track_id} -> {old_id}")
                track.track_id = old_id
                
        return tracks
    
    def _compute_signature(self, frame, track):
        """FIXED Lightweight Laplacian signature - resolves matrix alignment issues"""
        # Extract ROI - handle different track formats
        if hasattr(track, 'to_ltrb'):
            bbox = track.to_ltrb()
            x1, y1, x2, y2 = map(int, bbox)
        elif hasattr(track, 'tlbr'):
            x1, y1, x2, y2 = map(int, track.tlbr)
        else:
            return {'energy': 0, 'histogram': np.zeros(8)}

        # Ensure valid bounds
        h, w = frame.shape[:2]
        x1, y1 = max(0, x1), max(0, y1)
        x2, y2 = min(w, x2), min(h, y2)

        # FIXED: Better size validation to prevent matrix alignment errors
        if x2 <= x1 or y2 <= y1 or (x2 - x1) < 8 or (y2 - y1) < 8:
            return {'energy': 0, 'histogram': np.zeros(8)}

        try:
            roi = frame[y1:y2, x1:x2]

            # Convert to grayscale safely
            if len(roi.shape) == 3:
                gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            else:
                gray = roi.copy()

            # Additional size check after grayscale conversion
            if gray.shape[0] < 8 or gray.shape[1] < 8:
                return {'energy': 0, 'histogram': np.zeros(8)}

            # FIXED: Resize to consistent size to avoid matrix issues
            gray_resized = cv2.resize(gray, (32, 32))

            # Compute Laplacian on consistent size
            laplacian = cv2.Laplacian(gray_resized, cv2.CV_64F, ksize=3)

            # Energy (RMS) - proven formula
            energy = float(np.sqrt(np.mean(laplacian ** 2)))

            # Simple histogram for robustness
            hist, _ = np.histogram(laplacian.ravel(), bins=8, range=(-50, 50))
            hist = hist.astype(float) / (hist.sum() + 1e-6)

            return {
                'energy': energy,
                'histogram': hist
            }

        except Exception as e:
            # Robust fallback
            print(f"⚠️ Original signature computation error: {e}")
            return {'energy': 0, 'histogram': np.zeros(8)}
    
    def _update_signatures(self, tracks, frame):
        """Update memory of track signatures"""
        current_ids = set()
        
        for track in tracks:
            track_id = track.track_id
            current_ids.add(track_id)
            
            # Update position - handle different track formats
            if hasattr(track, 'to_ltrb'):
                bbox = track.to_ltrb()
                cx = (bbox[0] + bbox[2]) / 2
                cy = (bbox[1] + bbox[3]) / 2
            elif hasattr(track, 'tlbr'):
                cx = (track.tlbr[0] + track.tlbr[2]) / 2
                cy = (track.tlbr[1] + track.tlbr[3]) / 2
            else:
                cx, cy = 0, 0
            self.last_positions[track_id] = (cx, cy)
            
            # Update signature
            self.track_signatures[track_id] = self._compute_signature(frame, track)
            
        # Clean up old tracks
        old_ids = set(self.track_signatures.keys()) - current_ids
        for old_id in old_ids:
            del self.track_signatures[old_id]
            if old_id in self.last_positions:
                del self.last_positions[old_id]

    def _update_opencv_tracker(self, frame):
        """
        Handle OpenCV-style trackers or direct YOLO tracking
        """
        # If base_tracker is a YOLO model, use it directly
        if hasattr(self.base_tracker, 'track'):
            results = self.base_tracker.track(frame, persist=True, verbose=False, classes=[0])
            tracks = []
            if results and len(results) > 0 and results[0].boxes is not None:
                boxes = results[0].boxes
                for i in range(len(boxes)):
                    if boxes.id is not None and i < len(boxes.id):
                        try:
                            track = type('Track', (), {
                                'track_id': int(boxes.id[i]),
                                'tlbr': boxes.xyxy[i].cpu().numpy(),
                                'confidence': float(boxes.conf[i])
                            })()
                            tracks.append(track)
                        except:
                            continue
            return tracks
        else:
            # Fallback for other OpenCV trackers
            return []
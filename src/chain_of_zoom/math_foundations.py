"""
Mathematical Foundations for Chain-of-Zoom Video Tracking

This module adapts the mathematical principles from the Chain-of-Zoom paper
for video object tracking applications.

Key Mathematical Concepts Adapted:
1. AR-2 Modeling: p(x0,x1,...,xn) = p(x0,x1) ∏ p(xi|xi-1,xi-2)
2. Scale-State Decomposition: Intermediate zoom levels as tractable sub-problems
3. Multi-Scale-Aware Features: Replacing VLM prompts with tracking features
4. Laplacian Energy Scaling: E ∝ zoom² (from existing wrapper)

Mathematical Foundation from Paper:
- Joint Distribution: p(x0,c1,x1,...,cn,xn) = p(x0,x1) ∏ p(xi|xi-1,xi-2,ci)p(ci|xi-1,xi-2)
- Objective: L = log p(x0) + Σ log p(xi|xi-1,xi-2,ci) + Σ log p(ci|xi-1,xi-2)

Adapted for Video Tracking:
- Track States: p(track0,track1,...,trackn) with intermediate zoom levels
- Feature Prompts: ci = tracking features instead of text prompts
- Energy Conservation: Laplacian energy scaling across zoom chain
"""

import numpy as np
import cv2
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass
import math


@dataclass
class ZoomState:
    """Represents a single state in the zoom chain"""
    zoom_level: float
    frame: np.ndarray
    tracks: List[any]  # Track objects
    features: Dict[str, np.ndarray]  # Multi-scale features
    laplacian_energy: Dict[int, float]  # track_id -> energy
    timestamp: float


@dataclass 
class ScaleTransition:
    """Represents transition between two zoom states"""
    from_zoom: float
    to_zoom: float
    zoom_ratio: float
    predicted_positions: Dict[int, Tuple[float, float]]  # track_id -> (x, y)
    energy_scaling: Dict[int, float]  # track_id -> expected_energy
    confidence: float


class ChainOfZoomMath:
    """
    Core mathematical operations for Chain-of-Zoom video tracking
    
    Implements the mathematical foundations from the paper adapted for tracking:
    - AR-2 modeling for zoom state sequences
    - Scale-aware feature extraction
    - Energy conservation across zoom levels
    - Probabilistic track association
    """
    
    def __init__(self, max_zoom_steps: int = 8, min_zoom_step: float = 0.2):
        """
        Initialize mathematical foundations
        
        Args:
            max_zoom_steps: Maximum number of intermediate zoom steps
            min_zoom_step: Minimum zoom change to create intermediate step
        """
        self.max_zoom_steps = max_zoom_steps
        self.min_zoom_step = min_zoom_step
        
        # Mathematical constants from paper adaptation
        self.sigma_squared = 1.0  # Gaussian assumption parameter
        self.lambda_energy = 0.3  # Energy boost parameter (from re-ID)
        self.tau_decay = 20.0     # Temporal decay parameter
        
    def decompose_zoom_change(self, current_zoom: float, target_zoom: float) -> List[float]:
        """
        Decompose large zoom change into intermediate scale-states
        
        Based on paper's intermediate scale-state modeling:
        Bridge gap between zoom levels with tractable sub-problems
        
        Args:
            current_zoom: Current zoom level
            target_zoom: Target zoom level
            
        Returns:
            List of intermediate zoom levels
        """
        zoom_change = abs(target_zoom - current_zoom)
        
        # Small change - direct transition
        if zoom_change <= self.min_zoom_step:
            return [target_zoom]
        
        # Large change - create intermediate steps
        num_steps = min(self.max_zoom_steps, int(zoom_change / self.min_zoom_step))
        
        if num_steps <= 1:
            return [target_zoom]
        
        # Create logarithmic spacing for better stability
        if target_zoom > current_zoom:
            # Zooming in
            zoom_steps = np.logspace(
                np.log10(current_zoom), 
                np.log10(target_zoom), 
                num_steps + 1
            )[1:]  # Exclude current zoom
        else:
            # Zooming out  
            zoom_steps = np.logspace(
                np.log10(target_zoom),
                np.log10(current_zoom), 
                num_steps + 1
            )[::-1][:-1]  # Reverse and exclude current zoom
            
        return zoom_steps.tolist()
    
    def compute_ar2_probability(self, current_state: ZoomState, 
                               prev_state: Optional[ZoomState],
                               prev_prev_state: Optional[ZoomState]) -> float:
        """
        Compute AR-2 probability: p(xi|xi-1,xi-2,ci)
        
        From paper: AR-2 modeling with multi-scale-aware features
        p(xi|xi-1,xi-2) = ∫ p(xi|xi-1,xi-2,ci)p(ci|xi-1,xi-2)dci
        
        Args:
            current_state: Current zoom state xi
            prev_state: Previous zoom state xi-1  
            prev_prev_state: Previous-previous zoom state xi-2
            
        Returns:
            Probability score for this state transition
        """
        if prev_state is None:
            return 1.0  # Base case
            
        # Compute spatial consistency (Gaussian assumption)
        spatial_prob = self._compute_spatial_probability(current_state, prev_state)
        
        # Compute feature consistency (multi-scale aware)
        if prev_prev_state is not None:
            feature_prob = self._compute_feature_probability(
                current_state, prev_state, prev_prev_state
            )
        else:
            feature_prob = 1.0
            
        # Combined probability
        return spatial_prob * feature_prob
    
    def _compute_spatial_probability(self, current: ZoomState, prev: ZoomState) -> float:
        """Compute spatial consistency probability"""
        zoom_ratio = current.zoom_level / prev.zoom_level
        
        total_prob = 0.0
        num_tracks = 0
        
        for track_id in current.laplacian_energy:
            if track_id in prev.laplacian_energy:
                # Energy scaling check (from existing wrapper)
                expected_energy = prev.laplacian_energy[track_id] * (zoom_ratio ** 2)
                actual_energy = current.laplacian_energy[track_id]
                
                # Gaussian probability
                energy_diff = abs(expected_energy - actual_energy)
                prob = math.exp(-energy_diff**2 / (2 * self.sigma_squared))
                
                total_prob += prob
                num_tracks += 1
        
        return total_prob / max(1, num_tracks)
    
    def _compute_feature_probability(self, current: ZoomState, 
                                   prev: ZoomState, 
                                   prev_prev: ZoomState) -> float:
        """Compute multi-scale feature consistency probability"""
        # Multi-scale-aware feature matching
        # This replaces the VLM prompt probability p(ci|xi-1,xi-2)
        
        feature_consistency = 0.0
        
        # Check feature evolution across three states
        for feature_name in current.features:
            if feature_name in prev.features and feature_name in prev_prev.features:
                # Compute feature trajectory consistency
                curr_feat = current.features[feature_name]
                prev_feat = prev.features[feature_name]
                prev_prev_feat = prev_prev.features[feature_name]
                
                # Expected feature evolution based on zoom
                zoom_ratio_1 = current.zoom_level / prev.zoom_level
                zoom_ratio_2 = prev.zoom_level / prev_prev.zoom_level
                
                # Feature should evolve consistently with zoom
                expected_change = (prev_feat - prev_prev_feat) * zoom_ratio_1 / zoom_ratio_2
                actual_change = curr_feat - prev_feat
                
                # Cosine similarity for consistency
                if np.linalg.norm(expected_change) > 0 and np.linalg.norm(actual_change) > 0:
                    consistency = np.dot(expected_change.flatten(), actual_change.flatten()) / \
                                (np.linalg.norm(expected_change) * np.linalg.norm(actual_change))
                    feature_consistency += max(0, consistency)
        
        return feature_consistency / max(1, len(current.features))
    
    def compute_scale_transition(self, from_state: ZoomState, 
                               to_zoom: float) -> ScaleTransition:
        """
        Compute transition parameters between zoom states
        
        Args:
            from_state: Source zoom state
            to_zoom: Target zoom level
            
        Returns:
            ScaleTransition with predicted positions and energy scaling
        """
        zoom_ratio = to_zoom / from_state.zoom_level
        
        # Predict positions after zoom (spatial prediction)
        predicted_positions = {}
        energy_scaling = {}
        
        for track in from_state.tracks:
            track_id = getattr(track, 'track_id', id(track))
            
            # Get current position
            if hasattr(track, 'tlbr'):
                x1, y1, x2, y2 = track.tlbr
                cx, cy = (x1 + x2) / 2, (y1 + y2) / 2
            else:
                cx, cy = 0, 0  # Fallback
                
            # Predict position after zoom (from center)
            # Assuming zoom center is frame center
            frame_center_x = from_state.frame.shape[1] / 2
            frame_center_y = from_state.frame.shape[0] / 2
            
            predicted_x = frame_center_x + (cx - frame_center_x) * zoom_ratio
            predicted_y = frame_center_y + (cy - frame_center_y) * zoom_ratio
            
            predicted_positions[track_id] = (predicted_x, predicted_y)
            
            # Energy scaling (E ∝ zoom²)
            if track_id in from_state.laplacian_energy:
                energy_scaling[track_id] = from_state.laplacian_energy[track_id] * (zoom_ratio ** 2)
        
        # Compute transition confidence
        confidence = self._compute_transition_confidence(zoom_ratio)
        
        return ScaleTransition(
            from_zoom=from_state.zoom_level,
            to_zoom=to_zoom,
            zoom_ratio=zoom_ratio,
            predicted_positions=predicted_positions,
            energy_scaling=energy_scaling,
            confidence=confidence
        )
    
    def _compute_transition_confidence(self, zoom_ratio: float) -> float:
        """Compute confidence for zoom transition based on zoom magnitude"""
        # Higher confidence for smaller zoom changes
        zoom_change = abs(zoom_ratio - 1.0)
        
        # Exponential decay with zoom change
        confidence = math.exp(-zoom_change / 0.5)  # 0.5 is scaling factor
        
        return max(0.1, min(1.0, confidence))  # Clamp between 0.1 and 1.0

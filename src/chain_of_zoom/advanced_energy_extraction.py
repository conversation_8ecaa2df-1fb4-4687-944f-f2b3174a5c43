"""
🔬 Advanced Energy-Based Feature Extraction for Chain-of-Zoom Tracking
==================================================================

Mathematical Foundation & Physics Principles
==========================================

This module implements advanced energy-based feature extraction methods that extend
beyond the basic Laplacian energy (E ∝ zoom²) to provide enhanced discrimination
capabilities for worker safety tracking.

## 📐 Mathematical Foundations

### 1. Gradient Energy (Edge Detection Energy)
```
E_gradient = ||∇I||₂ = √((∂I/∂x)² + (∂I/∂y)²)

Physical Interpretation:
- Measures local intensity variations (edges, boundaries)
- Higher energy at object boundaries and texture transitions
- Scale-invariant under uniform illumination changes
```

### 2. Texture Energy (Local Binary Pattern Variance)
```
LBP(x,y) = Σ(i=0 to 7) s(g_i - g_c) × 2^i
E_texture = var(LBP) = E[(LBP - μ_LBP)²]

Physical Interpretation:
- Captures micro-texture patterns in worker clothing/equipment
- Robust to illumination changes
- Discriminates between different fabric textures (safety vests, uniforms)
```

### 3. Directional Energy (Orientation-Specific Features)
```
E_x = ||∂I/∂x||₂  (Horizontal edges/structures)
E_y = ||∂I/∂y||₂  (Vertical edges/structures)
θ = arctan(E_y/E_x)  (Dominant orientation)

Physical Interpretation:
- Captures structural orientation of worker posture
- Detects vertical structures (standing workers) vs horizontal (equipment)
- Orientation-aware discrimination for pose analysis
```

### 4. Multi-Scale Energy (Gaussian Pyramid Energy)
```
G_σ(x,y) = (1/2πσ²) × exp(-(x²+y²)/2σ²)
E_scale(σ) = E_base(G_σ * I)
E_multi = [E_scale(σ₁), E_scale(σ₂), ..., E_scale(σₙ)]

Physical Interpretation:
- Captures features at multiple spatial scales
- σ controls the scale of analysis (fine details vs coarse structures)
- Provides scale-space representation for robust tracking
```

## 🚀 M3 Max Optimization Strategy

### MPS (Metal Performance Shaders) Acceleration
- All tensor operations use PyTorch MPS backend
- Vectorized computations for 48GB RAM efficiency
- GPU-accelerated convolutions and mathematical operations

### Memory Optimization
- Efficient tensor memory management
- In-place operations where possible
- Batch processing for multiple track IDs

## 🎯 Integration with Chain-of-Zoom

The advanced energy features integrate seamlessly with the existing Chain-of-Zoom
mathematical framework:

```
Original: E_laplacian ∝ zoom²
Enhanced: E_total = α₁×E_laplacian + α₂×E_gradient + α₃×E_texture + α₄×E_directional

Where α_i are learned or heuristically determined weights.
```

## 🔬 Scientific Validation

Each energy type provides unique discrimination capabilities:
- **Gradient Energy**: Object boundary preservation during zoom
- **Texture Energy**: Worker clothing/equipment identification  
- **Directional Energy**: Posture and orientation analysis
- **Multi-Scale Energy**: Robust feature representation across zoom levels

Author: Chain-of-Zoom Team
Date: 2025-07-03
Purpose: Life-saving worker safety through advanced tracking
"""

import torch
import torch.nn.functional as F
import cv2
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import time
from skimage.feature import local_binary_pattern
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Check for MPS availability on M3 Max
if torch.backends.mps.is_available():
    DEVICE = torch.device("mps")
    logger.info("🚀 M3 Max MPS acceleration enabled!")
else:
    DEVICE = torch.device("cpu")
    logger.warning("⚠️ MPS not available, using CPU")


@dataclass
class AdvancedEnergyFeatures:
    """
    Container for all advanced energy-based features
    
    Mathematical Representation:
    E_total = [E_laplacian, E_gradient, E_texture, E_directional_x, E_directional_y, E_multi_scale]
    """
    track_id: int
    zoom_level: float
    
    # Basic energy (from existing system)
    laplacian_energy: float
    
    # Advanced energy features
    gradient_energy: float          # ||∇I||₂
    texture_energy: float           # var(LBP)
    directional_energy_x: float     # ||∂I/∂x||₂
    directional_energy_y: float     # ||∂I/∂y||₂
    dominant_orientation: float     # arctan(E_y/E_x)
    
    # Multi-scale energies (Gaussian pyramid)
    multi_scale_energies: List[float]  # [E_σ₁, E_σ₂, ..., E_σₙ]
    
    # Metadata
    extraction_time_ms: float
    confidence_score: float
    
    def to_vector(self) -> np.ndarray:
        """Convert to feature vector for machine learning"""
        return np.array([
            self.laplacian_energy,
            self.gradient_energy,
            self.texture_energy,
            self.directional_energy_x,
            self.directional_energy_y,
            self.dominant_orientation,
            *self.multi_scale_energies
        ])


class AdvancedEnergyExtractor:
    """
    🔬 Advanced Energy-Based Feature Extractor
    
    Implements state-of-the-art energy-based feature extraction with M3 Max optimization
    for enhanced worker discrimination and safety monitoring.
    
    Mathematical Framework:
    - Extends basic Laplacian energy with gradient, texture, and directional energies
    - Multi-scale analysis using Gaussian pyramid decomposition
    - MPS-accelerated computations for real-time performance
    """
    
    def __init__(self, 
                 multi_scale_sigmas: List[float] = [1.0, 2.0, 4.0, 8.0],
                 lbp_radius: int = 3,
                 lbp_n_points: int = 24,
                 enable_mps: bool = True,
                 debug: bool = False):
        """
        Initialize Advanced Energy Extractor
        
        Args:
            multi_scale_sigmas: Gaussian pyramid scales [σ₁, σ₂, ..., σₙ]
            lbp_radius: Local Binary Pattern radius
            lbp_n_points: Number of LBP sampling points
            enable_mps: Enable M3 Max MPS acceleration
            debug: Enable debug logging
        """
        self.multi_scale_sigmas = multi_scale_sigmas
        self.lbp_radius = lbp_radius
        self.lbp_n_points = lbp_n_points
        self.enable_mps = enable_mps and torch.backends.mps.is_available()
        self.debug = debug
        
        # Device selection
        self.device = DEVICE if self.enable_mps else torch.device("cpu")
        
        # Pre-compute Gaussian kernels for multi-scale analysis
        self._initialize_gaussian_kernels()
        
        # Pre-compute Sobel kernels for directional energy
        self._initialize_sobel_kernels()
        
        # Statistics
        self.stats = {
            'extractions_performed': 0,
            'total_extraction_time_ms': 0.0,
            'mps_accelerated': 0,
            'cpu_fallback': 0
        }
        
        if self.debug:
            logger.info(f"🔬 AdvancedEnergyExtractor initialized")
            logger.info(f"   Device: {self.device}")
            logger.info(f"   Multi-scale sigmas: {self.multi_scale_sigmas}")
            logger.info(f"   LBP: radius={self.lbp_radius}, points={self.lbp_n_points}")
    
    def _initialize_gaussian_kernels(self):
        """Pre-compute Gaussian kernels for multi-scale analysis"""
        self.gaussian_kernels = {}
        
        for sigma in self.multi_scale_sigmas:
            # Kernel size based on sigma (3σ rule)
            kernel_size = int(6 * sigma + 1)
            if kernel_size % 2 == 0:
                kernel_size += 1
                
            # Create 2D Gaussian kernel
            kernel = cv2.getGaussianKernel(kernel_size, sigma)
            kernel_2d = kernel @ kernel.T
            
            # Convert to PyTorch tensor for MPS acceleration
            kernel_tensor = torch.from_numpy(kernel_2d).float().unsqueeze(0).unsqueeze(0)
            if self.enable_mps:
                kernel_tensor = kernel_tensor.to(self.device)
                
            self.gaussian_kernels[sigma] = kernel_tensor
            
        if self.debug:
            logger.info(f"📐 Initialized {len(self.gaussian_kernels)} Gaussian kernels")
    
    def _initialize_sobel_kernels(self):
        """Pre-compute Sobel kernels for directional energy"""
        # Sobel X (vertical edges)
        sobel_x = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=np.float32)
        # Sobel Y (horizontal edges)  
        sobel_y = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=np.float32)
        
        # Convert to PyTorch tensors
        self.sobel_x = torch.from_numpy(sobel_x).unsqueeze(0).unsqueeze(0)
        self.sobel_y = torch.from_numpy(sobel_y).unsqueeze(0).unsqueeze(0)
        
        if self.enable_mps:
            self.sobel_x = self.sobel_x.to(self.device)
            self.sobel_y = self.sobel_y.to(self.device)
            
        if self.debug:
            logger.info("🧭 Initialized Sobel kernels for directional energy")

    def extract_advanced_features(self, frame: np.ndarray, track: Any,
                                zoom_level: float) -> AdvancedEnergyFeatures:
        """
        🎯 Extract comprehensive advanced energy features for a specific track

        Mathematical Pipeline:
        1. ROI Extraction: I_roi = frame[bbox]
        2. Gradient Energy: E_gradient = ||∇I_roi||₂
        3. Texture Energy: E_texture = var(LBP(I_roi))
        4. Directional Energy: E_x, E_y = ||∂I/∂x||₂, ||∂I/∂y||₂
        5. Multi-Scale Energy: E_multi = [E(G_σᵢ * I_roi)]

        Args:
            frame: Input video frame
            track: Track object with bounding box
            zoom_level: Current zoom level

        Returns:
            AdvancedEnergyFeatures object with all computed energies
        """
        start_time = time.time()

        try:
            # Extract ROI from track
            roi, bbox = self._extract_roi(frame, track)
            if roi is None:
                return self._create_zero_features(track, zoom_level)

            # Convert to grayscale for energy computations
            if len(roi.shape) == 3:
                gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            else:
                gray_roi = roi.copy()

            # 1. Basic Laplacian Energy (from existing system)
            laplacian_energy = self._compute_laplacian_energy(gray_roi)

            # 2. Gradient Energy: ||∇I||₂
            gradient_energy = self._compute_gradient_energy(gray_roi)

            # 3. Texture Energy: var(LBP)
            texture_energy = self._compute_texture_energy(gray_roi)

            # 4. Directional Energy: ||∂I/∂x||₂, ||∂I/∂y||₂
            dir_energy_x, dir_energy_y, orientation = self._compute_directional_energy(gray_roi)

            # 5. Multi-Scale Energy: [E(G_σᵢ * I)]
            multi_scale_energies = self._compute_multi_scale_energy(gray_roi)

            # Calculate confidence based on feature quality
            confidence = self._calculate_confidence(roi, bbox)

            # Create feature object
            extraction_time = (time.time() - start_time) * 1000

            features = AdvancedEnergyFeatures(
                track_id=getattr(track, 'track_id', 0),
                zoom_level=zoom_level,
                laplacian_energy=laplacian_energy,
                gradient_energy=gradient_energy,
                texture_energy=texture_energy,
                directional_energy_x=dir_energy_x,
                directional_energy_y=dir_energy_y,
                dominant_orientation=orientation,
                multi_scale_energies=multi_scale_energies,
                extraction_time_ms=extraction_time,
                confidence_score=confidence
            )

            # Update statistics
            self._update_stats(extraction_time)

            if self.debug:
                logger.info(f"🔬 Extracted features for track {features.track_id}: "
                          f"grad={gradient_energy:.2f}, tex={texture_energy:.2f}, "
                          f"dir_x={dir_energy_x:.2f}, dir_y={dir_energy_y:.2f}")

            return features

        except Exception as e:
            logger.error(f"❌ Feature extraction failed: {e}")
            return self._create_zero_features(track, zoom_level)

    def _extract_roi(self, frame: np.ndarray, track: Any) -> Tuple[Optional[np.ndarray], Optional[List[int]]]:
        """Extract Region of Interest from track bounding box"""
        try:
            # Handle different track formats
            if hasattr(track, 'to_ltrb'):
                bbox = track.to_ltrb()
                x1, y1, x2, y2 = map(int, bbox)
            elif hasattr(track, 'tlbr'):
                x1, y1, x2, y2 = map(int, track.tlbr)
            elif hasattr(track, 'bbox'):
                x1, y1, x2, y2 = map(int, track.bbox)
            else:
                logger.warning("⚠️ Unknown track format, cannot extract ROI")
                return None, None

            # Ensure valid bounds
            h, w = frame.shape[:2]
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(w, x2), min(h, y2)

            # Check for valid ROI
            if x2 <= x1 or y2 <= y1:
                logger.warning(f"⚠️ Invalid ROI: ({x1},{y1}) to ({x2},{y2})")
                return None, None

            # Extract ROI
            roi = frame[y1:y2, x1:x2].copy()

            # Minimum size check for reliable feature extraction
            if roi.shape[0] < 16 or roi.shape[1] < 16:
                logger.warning(f"⚠️ ROI too small: {roi.shape}")
                return None, None

            return roi, [x1, y1, x2, y2]

        except Exception as e:
            logger.error(f"❌ ROI extraction failed: {e}")
            return None, None

    def _compute_laplacian_energy(self, gray_roi: np.ndarray) -> float:
        """
        Compute basic Laplacian energy (compatibility with existing system)

        Mathematical Formula:
        E_laplacian = √(mean(∇²I)²) = RMS of Laplacian
        """
        try:
            laplacian = cv2.Laplacian(gray_roi, cv2.CV_64F, ksize=3)
            energy = np.sqrt(np.mean(laplacian ** 2))
            return float(energy)
        except Exception as e:
            logger.error(f"❌ Laplacian energy computation failed: {e}")
            return 0.0

    def _compute_gradient_energy(self, gray_roi: np.ndarray) -> float:
        """
        🧮 Compute Gradient Energy: ||∇I||₂

        Mathematical Formula:
        ∇I = [∂I/∂x, ∂I/∂y]
        E_gradient = √(mean((∂I/∂x)² + (∂I/∂y)²))

        Physical Interpretation:
        - Measures edge strength and boundary definition
        - Higher values indicate sharp transitions (object boundaries)
        - Scale-invariant under uniform illumination
        """
        try:
            if self.enable_mps:
                # MPS-accelerated computation
                return self._compute_gradient_energy_mps(gray_roi)
            else:
                # CPU computation using OpenCV
                grad_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
                grad_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)

                # Gradient magnitude
                gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
                energy = np.sqrt(np.mean(gradient_magnitude**2))

                return float(energy)

        except Exception as e:
            logger.error(f"❌ Gradient energy computation failed: {e}")
            return 0.0

    def _compute_gradient_energy_mps(self, gray_roi: np.ndarray) -> float:
        """MPS-accelerated gradient energy computation for M3 Max"""
        try:
            # Convert to PyTorch tensor
            roi_tensor = torch.from_numpy(gray_roi).float().unsqueeze(0).unsqueeze(0)
            roi_tensor = roi_tensor.to(self.device)

            # Apply Sobel filters using MPS
            grad_x = F.conv2d(roi_tensor, self.sobel_x, padding=1)
            grad_y = F.conv2d(roi_tensor, self.sobel_y, padding=1)

            # Compute gradient magnitude
            gradient_magnitude = torch.sqrt(grad_x**2 + grad_y**2)

            # RMS energy
            energy = torch.sqrt(torch.mean(gradient_magnitude**2))

            return float(energy.cpu().item())

        except Exception as e:
            logger.error(f"❌ MPS gradient energy computation failed: {e}")
            # Fallback to CPU
            return self._compute_gradient_energy_cpu(gray_roi)

    def _compute_gradient_energy_cpu(self, gray_roi: np.ndarray) -> float:
        """CPU fallback for gradient energy computation"""
        grad_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        energy = np.sqrt(np.mean(gradient_magnitude**2))
        return float(energy)

    def _compute_texture_energy(self, gray_roi: np.ndarray) -> float:
        """
        🧮 Compute Texture Energy: var(LBP)

        Mathematical Formula:
        LBP(x,y) = Σ(i=0 to P-1) s(gᵢ - gₓ) × 2ⁱ
        where s(x) = 1 if x ≥ 0, else 0
        E_texture = var(LBP) = E[(LBP - μ_LBP)²]

        Physical Interpretation:
        - Captures micro-texture patterns in clothing/equipment
        - Robust to illumination changes
        - Discriminates between different fabric textures
        - Higher variance indicates more complex texture patterns
        """
        try:
            # Ensure minimum size for LBP computation
            if gray_roi.shape[0] < 2*self.lbp_radius + 1 or gray_roi.shape[1] < 2*self.lbp_radius + 1:
                logger.warning(f"⚠️ ROI too small for LBP: {gray_roi.shape}")
                return 0.0

            # Compute Local Binary Pattern
            lbp = local_binary_pattern(
                gray_roi,
                self.lbp_n_points,
                self.lbp_radius,
                method='uniform'
            )

            # Compute variance as texture energy
            texture_energy = np.var(lbp)

            return float(texture_energy)

        except Exception as e:
            logger.error(f"❌ Texture energy computation failed: {e}")
            return 0.0

    def _compute_directional_energy(self, gray_roi: np.ndarray) -> Tuple[float, float, float]:
        """
        🧮 Compute Directional Energy: ||∂I/∂x||₂, ||∂I/∂y||₂

        Mathematical Formula:
        E_x = √(mean((∂I/∂x)²))  - Horizontal edge energy
        E_y = √(mean((∂I/∂y)²))  - Vertical edge energy
        θ = arctan(E_y/E_x)      - Dominant orientation

        Physical Interpretation:
        - E_x: Strength of vertical structures (standing workers)
        - E_y: Strength of horizontal structures (equipment, lying objects)
        - θ: Dominant orientation for pose analysis
        """
        try:
            if self.enable_mps:
                return self._compute_directional_energy_mps(gray_roi)
            else:
                # CPU computation
                grad_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
                grad_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)

                # Directional energies
                energy_x = np.sqrt(np.mean(grad_x**2))
                energy_y = np.sqrt(np.mean(grad_y**2))

                # Dominant orientation
                orientation = np.arctan2(energy_y, energy_x + 1e-8)  # Avoid division by zero

                return float(energy_x), float(energy_y), float(orientation)

        except Exception as e:
            logger.error(f"❌ Directional energy computation failed: {e}")
            return 0.0, 0.0, 0.0

    def _compute_directional_energy_mps(self, gray_roi: np.ndarray) -> Tuple[float, float, float]:
        """MPS-accelerated directional energy computation"""
        try:
            # Convert to PyTorch tensor
            roi_tensor = torch.from_numpy(gray_roi).float().unsqueeze(0).unsqueeze(0)
            roi_tensor = roi_tensor.to(self.device)

            # Apply Sobel filters
            grad_x = F.conv2d(roi_tensor, self.sobel_x, padding=1)
            grad_y = F.conv2d(roi_tensor, self.sobel_y, padding=1)

            # Compute directional energies
            energy_x = torch.sqrt(torch.mean(grad_x**2))
            energy_y = torch.sqrt(torch.mean(grad_y**2))

            # Dominant orientation
            orientation = torch.atan2(energy_y, energy_x + 1e-8)

            return (float(energy_x.cpu().item()),
                   float(energy_y.cpu().item()),
                   float(orientation.cpu().item()))

        except Exception as e:
            logger.error(f"❌ MPS directional energy computation failed: {e}")
            # Fallback to CPU
            return self._compute_directional_energy_cpu(gray_roi)

    def _compute_directional_energy_cpu(self, gray_roi: np.ndarray) -> Tuple[float, float, float]:
        """CPU fallback for directional energy computation"""
        grad_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)
        energy_x = np.sqrt(np.mean(grad_x**2))
        energy_y = np.sqrt(np.mean(grad_y**2))
        orientation = np.arctan2(energy_y, energy_x + 1e-8)
        return float(energy_x), float(energy_y), float(orientation)

    def _compute_multi_scale_energy(self, gray_roi: np.ndarray) -> List[float]:
        """
        🧮 Compute Multi-Scale Energy: [E(G_σᵢ * I)]

        Mathematical Formula:
        G_σ(x,y) = (1/2πσ²) × exp(-(x²+y²)/2σ²)
        I_σ = G_σ * I  (Gaussian convolution)
        E_scale(σ) = E_base(I_σ)  (Base energy on smoothed image)

        Physical Interpretation:
        - Different σ values capture features at different spatial scales
        - Small σ: Fine details and texture
        - Large σ: Coarse structures and overall shape
        - Provides scale-space representation for robust tracking
        """
        try:
            multi_scale_energies = []

            if self.enable_mps:
                # MPS-accelerated multi-scale computation
                roi_tensor = torch.from_numpy(gray_roi).float().unsqueeze(0).unsqueeze(0)
                roi_tensor = roi_tensor.to(self.device)

                for sigma in self.multi_scale_sigmas:
                    # Apply Gaussian smoothing
                    smoothed = F.conv2d(roi_tensor, self.gaussian_kernels[sigma],
                                      padding=self.gaussian_kernels[sigma].shape[-1]//2)

                    # Compute Laplacian energy on smoothed image
                    laplacian_kernel = torch.tensor([[0, -1, 0], [-1, 4, -1], [0, -1, 0]],
                                                  dtype=torch.float32).unsqueeze(0).unsqueeze(0)
                    laplacian_kernel = laplacian_kernel.to(self.device)

                    laplacian = F.conv2d(smoothed, laplacian_kernel, padding=1)
                    energy = torch.sqrt(torch.mean(laplacian**2))

                    multi_scale_energies.append(float(energy.cpu().item()))
            else:
                # CPU computation
                for sigma in self.multi_scale_sigmas:
                    # Apply Gaussian blur
                    kernel_size = int(6 * sigma + 1)
                    if kernel_size % 2 == 0:
                        kernel_size += 1

                    smoothed = cv2.GaussianBlur(gray_roi, (kernel_size, kernel_size), sigma)

                    # Compute Laplacian energy
                    laplacian = cv2.Laplacian(smoothed, cv2.CV_64F, ksize=3)
                    energy = np.sqrt(np.mean(laplacian**2))

                    multi_scale_energies.append(float(energy))

            return multi_scale_energies

        except Exception as e:
            logger.error(f"❌ Multi-scale energy computation failed: {e}")
            return [0.0] * len(self.multi_scale_sigmas)

    def _calculate_confidence(self, roi: np.ndarray, bbox: List[int]) -> float:
        """
        Calculate confidence score based on ROI quality

        Factors:
        - ROI size (larger is better for feature extraction)
        - Image sharpness (higher gradient energy indicates better focus)
        - Contrast (higher dynamic range is better)
        """
        try:
            # Size factor (normalized by typical person size)
            width, height = bbox[2] - bbox[0], bbox[3] - bbox[1]
            size_factor = min(1.0, (width * height) / (64 * 128))  # Typical person: 64x128

            # Sharpness factor (based on gradient energy)
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY) if len(roi.shape) == 3 else roi
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            sharpness = np.mean(np.sqrt(grad_x**2 + grad_y**2))
            sharpness_factor = min(1.0, sharpness / 50.0)  # Normalize to typical range

            # Contrast factor
            contrast = np.std(gray)
            contrast_factor = min(1.0, contrast / 64.0)  # Normalize to typical range

            # Combined confidence
            confidence = (size_factor * 0.4 + sharpness_factor * 0.4 + contrast_factor * 0.2)

            return float(confidence)

        except Exception as e:
            logger.error(f"❌ Confidence calculation failed: {e}")
            return 0.5  # Default moderate confidence

    def _create_zero_features(self, track: Any, zoom_level: float) -> AdvancedEnergyFeatures:
        """Create zero-filled features for failed extractions"""
        return AdvancedEnergyFeatures(
            track_id=getattr(track, 'track_id', 0),
            zoom_level=zoom_level,
            laplacian_energy=0.0,
            gradient_energy=0.0,
            texture_energy=0.0,
            directional_energy_x=0.0,
            directional_energy_y=0.0,
            dominant_orientation=0.0,
            multi_scale_energies=[0.0] * len(self.multi_scale_sigmas),
            extraction_time_ms=0.0,
            confidence_score=0.0
        )

    def _update_stats(self, extraction_time_ms: float):
        """Update extraction statistics"""
        self.stats['extractions_performed'] += 1
        self.stats['total_extraction_time_ms'] += extraction_time_ms

        if self.enable_mps:
            self.stats['mps_accelerated'] += 1
        else:
            self.stats['cpu_fallback'] += 1

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        if self.stats['extractions_performed'] > 0:
            avg_time = self.stats['total_extraction_time_ms'] / self.stats['extractions_performed']
        else:
            avg_time = 0.0

        return {
            'extractions_performed': self.stats['extractions_performed'],
            'average_extraction_time_ms': avg_time,
            'mps_accelerated': self.stats['mps_accelerated'],
            'cpu_fallback': self.stats['cpu_fallback'],
            'mps_acceleration_rate': (self.stats['mps_accelerated'] /
                                    max(1, self.stats['extractions_performed'])) * 100,
            'device': str(self.device)
        }

    def extract_batch_features(self, frame: np.ndarray, tracks: List[Any],
                             zoom_level: float) -> List[AdvancedEnergyFeatures]:
        """
        Extract features for multiple tracks in batch for efficiency

        Args:
            frame: Input video frame
            tracks: List of track objects
            zoom_level: Current zoom level

        Returns:
            List of AdvancedEnergyFeatures for each track
        """
        features_list = []

        for track in tracks:
            features = self.extract_advanced_features(frame, track, zoom_level)
            features_list.append(features)

        if self.debug and len(tracks) > 0:
            avg_time = np.mean([f.extraction_time_ms for f in features_list])
            logger.info(f"🔬 Batch extraction: {len(tracks)} tracks, "
                      f"avg time: {avg_time:.2f}ms")

        return features_list

"""
Multi-Scale Feature Extraction for Chain-of-Zoom Tracking

This module replaces the VLM text prompts from the original Chain-of-Zoom paper
with tracking-specific multi-scale features for video object tracking.

Original Paper: VLM generates text prompts p(ci|xi-1,xi-2) for super-resolution
Our Adaptation: Extract tracking features p(fi|xi-1,xi-2) for object tracking

Key Features:
1. Scale-aware feature extraction at each zoom level
2. Temporal consistency features across zoom states
3. Laplacian energy evolution tracking
4. Spatial context preservation
5. Motion prediction features

Mathematical Foundation:
- Replace text prompts ci with feature vectors fi
- Maintain multi-scale awareness: fi = f(xi-1, xi-2, zoom_level)
- Feature evolution: fi should predict tracking behavior at zoom level i
"""

import numpy as np
import cv2
from typing import List, Dict, Tuple, Any, Optional
from dataclasses import dataclass
import math


@dataclass
class MultiScaleFeatures:
    """Container for multi-scale tracking features"""
    zoom_level: float
    
    # Spatial features
    object_scales: Dict[int, float]  # track_id -> expected object size
    spatial_density: np.ndarray     # Spatial distribution of objects
    edge_density: float             # Overall edge density in frame
    
    # Temporal features  
    motion_vectors: Dict[int, Tuple[float, float]]  # track_id -> (vx, vy)
    acceleration: Dict[int, Tuple[float, float]]    # track_id -> (ax, ay)
    
    # Energy features
    laplacian_energies: Dict[int, float]  # track_id -> energy
    energy_gradients: Dict[int, float]    # track_id -> energy change rate
    
    # Context features
    scene_complexity: float         # Overall scene complexity
    occlusion_likelihood: Dict[int, float]  # track_id -> occlusion probability
    
    # Zoom-specific features
    zoom_stability: float           # How stable tracking should be at this zoom
    detail_enhancement: float       # Expected detail enhancement factor


class MultiScaleFeatureExtractor:
    """
    Multi-Scale Feature Extractor for Chain-of-Zoom Tracking
    
    This class extracts tracking-specific features that guide the Chain-of-Zoom
    process, replacing the VLM text prompts from the original paper.
    
    The features serve as "prompts" that inform each zoom step about:
    - Expected object behavior at the target zoom level
    - Spatial and temporal consistency requirements
    - Energy conservation expectations
    - Motion prediction guidance
    """
    
    def __init__(self, feature_history_size: int = 5):
        """
        Initialize multi-scale feature extractor
        
        Args:
            feature_history_size: Number of previous feature states to maintain
        """
        self.feature_history_size = feature_history_size
        self.feature_history = []  # List of MultiScaleFeatures
        
        # Feature extraction parameters
        self.edge_kernel = np.array([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]])
        self.motion_smoothing = 0.7  # Temporal smoothing for motion estimation
        
    def extract_features(self, frame: np.ndarray, tracks: List[Any], 
                        zoom_level: float, 
                        prev_frame: Optional[np.ndarray] = None) -> Dict[str, np.ndarray]:
        """
        Extract multi-scale features for current zoom level
        
        This is the core method that replaces VLM prompt generation:
        Original: ci = VLM(xi-1, xi-2) 
        Our approach: fi = extract_features(frame, tracks, zoom_level)
        
        Args:
            frame: Current video frame
            tracks: Current tracks
            zoom_level: Current zoom level
            prev_frame: Previous frame for motion estimation
            
        Returns:
            Dictionary of feature arrays for different aspects
        """
        # Extract individual feature components
        spatial_features = self._extract_spatial_features(frame, tracks, zoom_level)
        temporal_features = self._extract_temporal_features(tracks, prev_frame)
        energy_features = self._extract_energy_features(frame, tracks, zoom_level)
        context_features = self._extract_context_features(frame, tracks)
        zoom_features = self._extract_zoom_specific_features(zoom_level, tracks)
        
        # Create comprehensive feature set
        multi_scale_features = MultiScaleFeatures(
            zoom_level=zoom_level,
            object_scales=spatial_features['object_scales'],
            spatial_density=spatial_features['spatial_density'],
            edge_density=spatial_features['edge_density'],
            motion_vectors=temporal_features['motion_vectors'],
            acceleration=temporal_features['acceleration'],
            laplacian_energies=energy_features['laplacian_energies'],
            energy_gradients=energy_features['energy_gradients'],
            scene_complexity=context_features['scene_complexity'],
            occlusion_likelihood=context_features['occlusion_likelihood'],
            zoom_stability=zoom_features['zoom_stability'],
            detail_enhancement=zoom_features['detail_enhancement']
        )
        
        # Update feature history for temporal consistency
        self._update_feature_history(multi_scale_features)
        
        # Convert to dictionary format for compatibility
        return self._features_to_dict(multi_scale_features)
    
    def _extract_spatial_features(self, frame: np.ndarray, tracks: List[Any], 
                                 zoom_level: float) -> Dict:
        """Extract spatial features at current zoom level"""
        h, w = frame.shape[:2]
        
        # Object scales (expected size at this zoom level)
        object_scales = {}
        spatial_positions = []
        
        for track in tracks:
            track_id = getattr(track, 'track_id', id(track))
            
            # Get bounding box
            if hasattr(track, 'tlbr'):
                x1, y1, x2, y2 = track.tlbr
            elif hasattr(track, 'to_ltrb'):
                x1, y1, x2, y2 = track.to_ltrb()
            else:
                continue
                
            # Object scale (area normalized by frame size)
            object_area = (x2 - x1) * (y2 - y1)
            normalized_scale = object_area / (w * h)
            object_scales[track_id] = normalized_scale
            
            # Spatial position for density calculation
            cx, cy = (x1 + x2) / 2, (y1 + y2) / 2
            spatial_positions.append([cx / w, cy / h])  # Normalized positions
        
        # Spatial density map
        if spatial_positions:
            positions = np.array(spatial_positions)
            # Create 2D histogram for spatial density
            density, _, _ = np.histogram2d(positions[:, 0], positions[:, 1], 
                                         bins=8, range=[[0, 1], [0, 1]])
            spatial_density = density.flatten()
        else:
            spatial_density = np.zeros(64)  # 8x8 grid flattened
        
        # Edge density (overall frame complexity)
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
        edges = cv2.filter2D(gray, -1, self.edge_kernel)
        edge_density = np.mean(np.abs(edges))
        
        return {
            'object_scales': object_scales,
            'spatial_density': spatial_density,
            'edge_density': edge_density
        }
    
    def _extract_temporal_features(self, tracks: List[Any], 
                                  prev_frame: Optional[np.ndarray]) -> Dict:
        """Extract temporal motion features"""
        motion_vectors = {}
        acceleration = {}
        
        # Get motion from feature history if available
        if len(self.feature_history) >= 2:
            prev_features = self.feature_history[-1]
            prev_prev_features = self.feature_history[-2]
            
            for track in tracks:
                track_id = getattr(track, 'track_id', id(track))
                
                # Get current position
                if hasattr(track, 'tlbr'):
                    x1, y1, x2, y2 = track.tlbr
                    cx, cy = (x1 + x2) / 2, (y1 + y2) / 2
                else:
                    continue
                
                # Motion vector from history
                if track_id in prev_features.motion_vectors:
                    prev_vx, prev_vy = prev_features.motion_vectors[track_id]
                    
                    # Simple motion estimation (could be enhanced)
                    motion_vectors[track_id] = (prev_vx * self.motion_smoothing, 
                                              prev_vy * self.motion_smoothing)
                    
                    # Acceleration from previous motion
                    if track_id in prev_prev_features.motion_vectors:
                        prev_prev_vx, prev_prev_vy = prev_prev_features.motion_vectors[track_id]
                        ax = prev_vx - prev_prev_vx
                        ay = prev_vy - prev_prev_vy
                        acceleration[track_id] = (ax, ay)
                else:
                    motion_vectors[track_id] = (0.0, 0.0)
                    acceleration[track_id] = (0.0, 0.0)
        else:
            # No history available
            for track in tracks:
                track_id = getattr(track, 'track_id', id(track))
                motion_vectors[track_id] = (0.0, 0.0)
                acceleration[track_id] = (0.0, 0.0)
        
        return {
            'motion_vectors': motion_vectors,
            'acceleration': acceleration
        }
    
    def _extract_energy_features(self, frame: np.ndarray, tracks: List[Any], 
                                zoom_level: float) -> Dict:
        """Extract Laplacian energy features"""
        laplacian_energies = {}
        energy_gradients = {}
        
        for track in tracks:
            track_id = getattr(track, 'track_id', id(track))
            
            # Compute Laplacian energy (reuse from zoom wrapper logic)
            energy = self._compute_laplacian_energy(frame, track)
            laplacian_energies[track_id] = energy
            
            # Energy gradient from history
            if len(self.feature_history) >= 1:
                prev_features = self.feature_history[-1]
                if track_id in prev_features.laplacian_energies:
                    prev_energy = prev_features.laplacian_energies[track_id]
                    zoom_ratio = zoom_level / prev_features.zoom_level
                    
                    # Expected energy based on zoom scaling (E ∝ zoom²)
                    expected_energy = prev_energy * (zoom_ratio ** 2)
                    energy_gradient = (energy - expected_energy) / max(expected_energy, 1e-6)
                    energy_gradients[track_id] = energy_gradient
                else:
                    energy_gradients[track_id] = 0.0
            else:
                energy_gradients[track_id] = 0.0
        
        return {
            'laplacian_energies': laplacian_energies,
            'energy_gradients': energy_gradients
        }
    
    def _compute_laplacian_energy(self, frame: np.ndarray, track: Any) -> float:
        """Compute Laplacian energy for a track"""
        try:
            # Extract ROI
            if hasattr(track, 'tlbr'):
                x1, y1, x2, y2 = map(int, track.tlbr)
            elif hasattr(track, 'to_ltrb'):
                x1, y1, x2, y2 = map(int, track.to_ltrb())
            else:
                return 0.0
            
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(frame.shape[1], x2), min(frame.shape[0], y2)
            
            if x2 <= x1 or y2 <= y1:
                return 0.0
            
            roi = frame[y1:y2, x1:x2]
            
            # Convert to grayscale
            if len(roi.shape) == 3:
                gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            else:
                gray = roi
            
            # Compute Laplacian energy
            laplacian = cv2.Laplacian(gray, cv2.CV_64F, ksize=3)
            energy = np.sqrt(np.mean(laplacian ** 2))
            
            return energy
        except:
            return 0.0
    
    def _extract_context_features(self, frame: np.ndarray, tracks: List[Any]) -> Dict:
        """Extract scene context features"""
        # Scene complexity (overall image complexity)
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
        laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
        scene_complexity = min(1.0, laplacian_var / 1000.0)  # Normalize
        
        # Occlusion likelihood (based on track overlap)
        occlusion_likelihood = {}
        for i, track1 in enumerate(tracks):
            track_id1 = getattr(track1, 'track_id', id(track1))
            overlap_score = 0.0
            
            if hasattr(track1, 'tlbr'):
                x1_1, y1_1, x2_1, y2_1 = track1.tlbr
                
                for j, track2 in enumerate(tracks):
                    if i == j:
                        continue
                    
                    if hasattr(track2, 'tlbr'):
                        x1_2, y1_2, x2_2, y2_2 = track2.tlbr
                        
                        # Compute IoU
                        intersection_area = max(0, min(x2_1, x2_2) - max(x1_1, x1_2)) * \
                                          max(0, min(y2_1, y2_2) - max(y1_1, y1_2))
                        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
                        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
                        union_area = area1 + area2 - intersection_area
                        
                        if union_area > 0:
                            iou = intersection_area / union_area
                            overlap_score = max(overlap_score, iou)
            
            occlusion_likelihood[track_id1] = overlap_score
        
        return {
            'scene_complexity': scene_complexity,
            'occlusion_likelihood': occlusion_likelihood
        }
    
    def _extract_zoom_specific_features(self, zoom_level: float, tracks: List[Any]) -> Dict:
        """Extract zoom-level specific features"""
        # Zoom stability (how stable tracking should be at this zoom level)
        # Higher zoom = potentially less stable
        zoom_stability = math.exp(-abs(zoom_level - 1.0) / 2.0)  # Exponential decay from 1.0
        
        # Detail enhancement factor (expected detail increase)
        detail_enhancement = zoom_level ** 1.5  # Slightly less than quadratic
        
        return {
            'zoom_stability': zoom_stability,
            'detail_enhancement': detail_enhancement
        }
    
    def _update_feature_history(self, features: MultiScaleFeatures) -> None:
        """Update feature history for temporal consistency"""
        self.feature_history.append(features)
        
        # Maintain history size
        if len(self.feature_history) > self.feature_history_size:
            self.feature_history.pop(0)
    
    def _features_to_dict(self, features: MultiScaleFeatures) -> Dict[str, np.ndarray]:
        """Convert MultiScaleFeatures to dictionary format"""
        return {
            'spatial_density': features.spatial_density,
            'edge_density': np.array([features.edge_density]),
            'scene_complexity': np.array([features.scene_complexity]),
            'zoom_stability': np.array([features.zoom_stability]),
            'detail_enhancement': np.array([features.detail_enhancement]),
            'zoom_level': np.array([features.zoom_level])
        }
    
    def get_feature_summary(self) -> Dict:
        """Get summary of extracted features for debugging"""
        if not self.feature_history:
            return {'status': 'No features extracted yet'}
        
        latest = self.feature_history[-1]
        return {
            'zoom_level': latest.zoom_level,
            'num_objects': len(latest.object_scales),
            'scene_complexity': latest.scene_complexity,
            'edge_density': latest.edge_density,
            'zoom_stability': latest.zoom_stability,
            'feature_history_length': len(self.feature_history)
        }

"""
Chain-of-Zoom Tracker: Extreme Zoom Tracking for Video

This module implements Chain-of-Zoom principles for video object tracking,
enabling extreme zoom operations (8x-32x) while maintaining track ID consistency.

Based on the paper: "Chain-of-Zoom: Extreme Super-Resolution via Scale 
Autoregression and Preference Alignment" by <PERSON> et al.

Adapted for video tracking applications in industrial safety systems.
"""

from .chain_of_zoom_tracker import ChainOfZoomTracker
from .scale_state_modeling import ScaleStateModeling
from .multi_scale_features import MultiScaleFeatureExtractor
from .math_foundations import ChainOfZoomMath
from .advanced_energy_extraction import AdvancedEnergyExtractor, AdvancedEnergyFeatures

__version__ = "0.2.0"
__author__ = "Zoom Tracking Team"

__all__ = [
    "ChainOfZoomTracker",
    "ScaleStateModeling", 
    "MultiScaleFeatureExtractor",
    "ChainOfZoomMath"
]

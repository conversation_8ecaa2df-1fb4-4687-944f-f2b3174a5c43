#!/usr/bin/env python3
"""
🔬 Advanced Energy Feature Extraction Demo
==========================================

Interactive demonstration of advanced energy-based feature extraction
integrated with the proven Chain-of-Zoom tracking system.

Features:
- Real-time advanced energy feature extraction
- Interactive track ID selection (same as your original demo)
- Mathematical feature visualization
- M3 Max MPS acceleration
- Performance metrics display

Controls:
- ENTER: Open track ID input
- Type track ID: Select specific worker for analysis
- ENTER again: Execute zoom with advanced feature extraction
- ESC: Reset zoom / Cancel input
- F: Toggle feature visualization overlay
- S: Save current frame with features
- Q: Quit

Mathematical Features Displayed:
- Gradient Energy: ||∇I||₂ (edge strength)
- Texture Energy: var(LBP) (texture complexity)
- Directional Energy: E_x, E_y (orientation analysis)
- Multi-Scale Energy: [E_σ₁, E_σ₂, E_σ₃, E_σ₄] (scale-space)
"""

import cv2
import numpy as np
import time
import sys
import os
from typing import List, Dict, Any, Optional
from ultralytics import Y<PERSON>O
import torch

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.chain_of_zoom.chain_of_zoom_tracker import ChainOfZoomTracker
from src.chain_of_zoom.advanced_energy_extraction import AdvancedEnergyExtractor, AdvancedEnergyFeatures
from src.core.zoom_tracking_wrapper import ZoomTrackingWrapper


class AdvancedEnergyDemo:
    """
    🎯 Advanced Energy Feature Extraction Demo
    
    Extends the original Chain-of-Zoom demo with advanced energy-based
    feature extraction and mathematical visualization.
    """
    
    def __init__(self, video_path: str = "videos/test2.mp4"):
        """Initialize the advanced energy demo"""
        print("🔬 Initializing Advanced Energy Feature Extraction Demo...")
        print("   Built with love for worker safety! 💙")
        
        # Video setup
        self.video_path = video_path
        self.cap = cv2.VideoCapture(video_path)
        if not self.cap.isOpened():
            raise ValueError(f"❌ Cannot open video: {video_path}")
        
        # Get video properties
        self.fps = int(self.cap.get(cv2.CAP_PROP_FPS))
        self.frame_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.frame_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"📹 Video: {video_path}")
        print(f"   Resolution: {self.frame_width}x{self.frame_height}")
        print(f"   FPS: {self.fps}")

        # Check OpenCV build info for debugging
        print(f"🖥️ OpenCV version: {cv2.__version__}")
        build_info = cv2.getBuildInformation()
        if 'GUI:' in build_info:
            gui_backend = build_info.split('GUI:')[1].split('\n')[0].strip()
        else:
            gui_backend = 'Unknown'
        print(f"   GUI backend: {gui_backend}")
        
        # Initialize YOLO model
        print("🤖 Loading YOLO model...")
        self.model = YOLO('yolov8n.pt')
        
        # Initialize Chain-of-Zoom tracker
        print("🔗 Initializing Chain-of-Zoom tracker...")
        base_tracker = None  # Will use YOLO's built-in tracking
        self.zoom_wrapper = ZoomTrackingWrapper(base_tracker)
        self.chain_tracker = ChainOfZoomTracker(
            base_tracker=self.zoom_wrapper,
            extreme_zoom_threshold=2.0,
            max_zoom_steps=8,
            debug=True
        )
        
        # Initialize Advanced Energy Extractor
        print("⚡ Initializing Advanced Energy Extractor with M3 Max optimization...")
        self.energy_extractor = AdvancedEnergyExtractor(
            multi_scale_sigmas=[1.0, 2.0, 4.0, 8.0],
            lbp_radius=3,
            lbp_n_points=24,
            enable_mps=True,
            debug=True
        )
        
        # Demo state
        self.zoom_level = 1.0
        self.target_track_id = None
        self.input_mode = False
        self.input_text = ""
        self.show_features = True
        self.frame_count = 0
        
        # Feature visualization
        self.current_features = {}  # track_id -> AdvancedEnergyFeatures
        self.feature_history = {}   # track_id -> List[AdvancedEnergyFeatures]
        
        # Performance tracking
        self.performance_stats = {
            'frame_times': [],
            'extraction_times': [],
            'total_extractions': 0
        }
        
        print("✅ Advanced Energy Demo initialized successfully!")
        print("\n🎮 CONTROLS:")
        print("   ENTER: Open track ID input")
        print("   Type track ID: Select worker for analysis")
        print("   ENTER again: Execute zoom with feature extraction")
        print("   ESC: Reset zoom / Cancel input")
        print("   F: Toggle feature visualization")
        print("   S: Save frame with features")
        print("   Q: Quit")
        print("\n🔬 MATHEMATICAL FEATURES:")
        print("   • Gradient Energy: ||∇I||₂ (edge strength)")
        print("   • Texture Energy: var(LBP) (texture complexity)")
        print("   • Directional Energy: E_x, E_y (orientation)")
        print("   • Multi-Scale Energy: [E_σ₁, E_σ₂, E_σ₃, E_σ₄]")
    
    def run(self):
        """Run the interactive advanced energy demo"""
        print("\n🚀 Starting Advanced Energy Feature Extraction Demo...")

        # Create window first and ensure it's visible
        window_name = 'Advanced Energy Feature Extraction Demo'
        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(window_name, 1280, 720)

        # Create initial frame to show window
        initial_frame = np.zeros((720, 1280, 3), dtype=np.uint8)
        cv2.putText(initial_frame, "🔬 Advanced Energy Feature Extraction Demo",
                   (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 2)
        cv2.putText(initial_frame, "Loading video and initializing tracking...",
                   (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
        cv2.putText(initial_frame, "Press any key to begin!",
                   (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

        cv2.imshow(window_name, initial_frame)
        print("🖥️ Video window should now be visible!")
        print("Press any key in the video window to begin...")

        # Wait for user to press key in the video window
        cv2.waitKey(0)

        try:
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    print("📹 End of video reached, restarting...")
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    continue

                start_time = time.time()
                self.frame_count += 1

                # Process frame
                processed_frame = self._process_frame(frame)

                # Display frame BEFORE handling input (important for macOS)
                cv2.imshow(window_name, processed_frame)

                # Handle user input - faster for normal tracking, slower for analysis
                if self.target_track_id is not None:
                    key = cv2.waitKey(30) & 0xFF  # Slower when analyzing
                else:
                    key = cv2.waitKey(1) & 0xFF   # Fast for normal tracking
                if not self._handle_input(key):
                    break

                # Performance tracking
                frame_time = (time.time() - start_time) * 1000
                self.performance_stats['frame_times'].append(frame_time)

                # Keep only recent performance data
                if len(self.performance_stats['frame_times']) > 100:
                    self.performance_stats['frame_times'] = self.performance_stats['frame_times'][-100:]

        except KeyboardInterrupt:
            print("\n⏹️ Demo interrupted by user")

        finally:
            self._cleanup()
    
    def _process_frame(self, frame: np.ndarray) -> np.ndarray:
        """Process frame with YOLO detection and conditional advanced energy extraction"""
        # YOLO detection
        results = self.model.track(frame, persist=True, verbose=False)

        # Extract detections
        detections = []
        tracks = []

        if results[0].boxes is not None and results[0].boxes.id is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            track_ids = results[0].boxes.id.cpu().numpy().astype(int)
            confidences = results[0].boxes.conf.cpu().numpy()

            for i, (box, track_id, conf) in enumerate(zip(boxes, track_ids, confidences)):
                # Create detection format
                detection = [*box, conf, 0]  # [x1, y1, x2, y2, conf, class]
                detections.append(detection)

                # Create simple track object
                track = type('Track', (), {
                    'track_id': track_id,
                    'tlbr': box,
                    'confidence': conf
                })()
                tracks.append(track)

        # Advanced energy feature extraction - ONLY for selected target or when zoomed
        start_extraction = time.time()
        if tracks and (self.target_track_id is not None or self.zoom_level > 1.5):
            if self.target_track_id is not None:
                # Extract features only for the selected target track
                target_tracks = [t for t in tracks if t.track_id == self.target_track_id]
                if target_tracks:
                    features_list = self.energy_extractor.extract_batch_features(
                        frame, target_tracks, self.zoom_level
                    )

                    # Update current features for target only
                    for features in features_list:
                        self.current_features[features.track_id] = features

                        # Update feature history
                        if features.track_id not in self.feature_history:
                            self.feature_history[features.track_id] = []
                        self.feature_history[features.track_id].append(features)

                        # Keep only recent history
                        if len(self.feature_history[features.track_id]) > 30:
                            self.feature_history[features.track_id] = self.feature_history[features.track_id][-30:]

                    extraction_time = (time.time() - start_extraction) * 1000
                    self.performance_stats['extraction_times'].append(extraction_time)
                    self.performance_stats['total_extractions'] += len(target_tracks)
            else:
                # No specific target, skip advanced extraction for performance
                pass
        else:
            # Normal tracking mode - no advanced features for speed
            pass

        # Create visualization
        vis_frame = self._create_visualization(frame, tracks)

        return vis_frame

    def _create_visualization(self, frame: np.ndarray, tracks: List[Any]) -> np.ndarray:
        """Create comprehensive visualization with advanced energy features and zoom"""
        # Apply zoom if target is selected (using proven live demo method)
        if self.target_track_id is not None and self.zoom_level > 1.0:
            vis_frame, transformed_tracks = self._apply_chain_of_zoom(frame, tracks)
        else:
            vis_frame = frame.copy()
            transformed_tracks = tracks

        # Draw tracks with energy information
        for track in transformed_tracks:
            track_id = track.track_id
            x1, y1, x2, y2 = map(int, track.tlbr)

            # Get advanced features for this track
            features = self.current_features.get(track_id)

            # Color coding based on target selection
            if track_id == self.target_track_id:
                color = (0, 255, 0)  # Green for selected target
                thickness = 4
            else:
                color = (0, 255, 255)  # Yellow for other tracks
                thickness = 2

            # Draw bounding box
            cv2.rectangle(vis_frame, (x1, y1), (x2, y2), color, thickness)

            # Draw track ID
            cv2.putText(vis_frame, f"ID: {track_id}", (x1, y1-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

            # Draw advanced energy features if available and enabled (only for target)
            if features and self.show_features and track_id == self.target_track_id:
                self._draw_energy_features(vis_frame, features, (x1, y1, x2, y2))

        # Draw UI elements
        self._draw_ui_elements(vis_frame)

        return vis_frame

    def _apply_chain_of_zoom(self, frame: np.ndarray, tracks: List[Any]) -> tuple:
        """Apply chain-of-zoom using proven live demo method"""
        if self.target_track_id is None:
            return frame, tracks

        # Find target track
        target_track = None
        for track in tracks:
            if track.track_id == self.target_track_id:
                target_track = track
                break

        if target_track is None:
            return frame, tracks

        # Get target center (zoom center)
        x1, y1, x2, y2 = map(int, target_track.tlbr)
        zoom_center = ((x1 + x2) / 2, (y1 + y2) / 2)

        # Apply zoom to frame (exact method from live demo)
        zoomed_frame = self._apply_zoom_to_frame(frame, self.zoom_level, zoom_center)

        # Transform track coordinates for zoomed frame
        transformed_tracks = self._transform_tracks_for_zoom(tracks, self.zoom_level, zoom_center, frame.shape[:2])

        return zoomed_frame, transformed_tracks

    def _apply_zoom_to_frame(self, frame: np.ndarray, zoom_level: float, zoom_center: tuple) -> np.ndarray:
        """Apply zoom transformation to frame (from live demo)"""
        if zoom_level <= 1.0:
            return frame

        h, w = frame.shape[:2]
        cx, cy = zoom_center

        # Calculate crop dimensions
        crop_w = int(w / zoom_level)
        crop_h = int(h / zoom_level)

        # Calculate crop position (centered on zoom_center)
        start_x = int(max(0, min(w - crop_w, cx - crop_w // 2)))
        start_y = int(max(0, min(h - crop_h, cy - crop_h // 2)))
        end_x = start_x + crop_w
        end_y = start_y + crop_h

        # Crop and resize
        cropped = frame[start_y:end_y, start_x:end_x]
        zoomed = cv2.resize(cropped, (w, h), interpolation=cv2.INTER_LINEAR)

        return zoomed

    def _transform_tracks_for_zoom(self, tracks: List[Any], zoom_level: float, zoom_center: tuple, frame_shape: tuple) -> List[Any]:
        """Transform track coordinates for zoomed frame (from live demo)"""
        if zoom_level <= 1.0:
            return tracks

        transformed_tracks = []
        h, w = frame_shape
        cx, cy = zoom_center

        # Calculate crop parameters
        crop_w = w / zoom_level
        crop_h = h / zoom_level
        start_x = max(0, min(w - crop_w, cx - crop_w / 2))
        start_y = max(0, min(h - crop_h, cy - crop_h / 2))

        for track in tracks:
            x1, y1, x2, y2 = map(int, track.tlbr)

            # Transform coordinates
            new_x1 = (x1 - start_x) * zoom_level
            new_y1 = (y1 - start_y) * zoom_level
            new_x2 = (x2 - start_x) * zoom_level
            new_y2 = (y2 - start_y) * zoom_level

            # Check if track is visible in zoomed frame
            if (new_x2 > 0 and new_x1 < w and new_y2 > 0 and new_y1 < h):
                # Clip to frame boundaries
                new_x1 = max(0, min(w, new_x1))
                new_y1 = max(0, min(h, new_y1))
                new_x2 = max(0, min(w, new_x2))
                new_y2 = max(0, min(h, new_y2))

                # Create transformed track
                transformed_track = type('Track', (), {
                    'track_id': track.track_id,
                    'tlbr': [new_x1, new_y1, new_x2, new_y2],
                    'confidence': track.confidence
                })()

                transformed_tracks.append(transformed_track)

        return transformed_tracks

    def _draw_energy_features(self, frame: np.ndarray, features: AdvancedEnergyFeatures,
                            bbox: tuple):
        """Draw raw mathematical energy features visualization"""
        x1, y1, x2, y2 = bbox

        # Feature display area (to the right of bbox)
        text_x = x2 + 10
        text_y = y1
        line_height = 20

        # Background for text readability
        overlay = frame.copy()
        cv2.rectangle(overlay, (text_x-5, text_y-5), (text_x+320, text_y+180),
                     (0, 0, 0), -1)
        cv2.addWeighted(frame, 0.7, overlay, 0.3, 0, frame)

        # Color scheme for different energy types
        colors = {
            'laplacian': (255, 255, 255),    # White
            'gradient': (0, 255, 255),       # Cyan
            'texture': (255, 0, 255),        # Magenta
            'directional': (255, 255, 0),    # Yellow
            'multi_scale': (0, 255, 0)       # Green
        }

        # Title
        cv2.putText(frame, "🔬 MATHEMATICAL ENERGY ANALYSIS",
                   (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                   (0, 255, 0), 2)
        y_offset = 25

        # Laplacian Energy (original Chain-of-Zoom)
        cv2.putText(frame, f"Laplacian Energy: {features.laplacian_energy:.2f}",
                   (text_x, text_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.45,
                   colors['laplacian'], 1)
        y_offset += line_height

        # Gradient Energy: ||∇I||₂
        cv2.putText(frame, f"Gradient Energy: {features.gradient_energy:.2f}",
                   (text_x, text_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.45,
                   colors['gradient'], 1)
        cv2.putText(frame, f"  ||∇I||₂ (edge strength)",
                   (text_x, text_y + y_offset + 12), cv2.FONT_HERSHEY_SIMPLEX, 0.3,
                   colors['gradient'], 1)
        y_offset += line_height + 12

        # Texture Energy: var(LBP)
        cv2.putText(frame, f"Texture Energy: {features.texture_energy:.2f}",
                   (text_x, text_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.45,
                   colors['texture'], 1)
        cv2.putText(frame, f"  var(LBP) (texture complexity)",
                   (text_x, text_y + y_offset + 12), cv2.FONT_HERSHEY_SIMPLEX, 0.3,
                   colors['texture'], 1)
        y_offset += line_height + 12

        # Directional Energy: E_x, E_y
        cv2.putText(frame, f"Directional Energy X: {features.directional_energy_x:.2f}",
                   (text_x, text_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.45,
                   colors['directional'], 1)
        y_offset += line_height

        cv2.putText(frame, f"Directional Energy Y: {features.directional_energy_y:.2f}",
                   (text_x, text_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.45,
                   colors['directional'], 1)
        y_offset += line_height

        # Dominant Orientation
        angle_deg = np.degrees(features.dominant_orientation)
        cv2.putText(frame, f"Dominant Angle: {angle_deg:.1f}°",
                   (text_x, text_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.45,
                   colors['directional'], 1)
        y_offset += line_height

        # Multi-Scale Energies
        cv2.putText(frame, f"Multi-Scale Energies:",
                   (text_x, text_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.45,
                   colors['multi_scale'], 1)
        y_offset += 15

        for i, energy in enumerate(features.multi_scale_energies):
            sigma = [1.0, 2.0, 4.0, 8.0][i] if i < 4 else f"σ{i+1}"
            cv2.putText(frame, f"  σ={sigma}: {energy:.1f}",
                       (text_x, text_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.35,
                       colors['multi_scale'], 1)
            y_offset += 12

        # Performance metrics
        y_offset += 8
        cv2.putText(frame, f"Confidence: {features.confidence_score:.3f}",
                   (text_x, text_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4,
                   (255, 255, 255), 1)
        y_offset += 15
        cv2.putText(frame, f"Extraction Time: {features.extraction_time_ms:.1f}ms",
                   (text_x, text_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4,
                   (255, 255, 255), 1)


    def _draw_ui_elements(self, frame: np.ndarray):
        """Draw user interface elements"""
        h, w = frame.shape[:2]

        # Title
        title = "🔬 Chain-of-Zoom Advanced Energy Demo"
        cv2.putText(frame, title, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8,
                   (255, 255, 255), 2)

        # Current mode
        if self.target_track_id is not None:
            mode_text = f"MODE: ZOOMED ANALYSIS (ID {self.target_track_id})"
            mode_color = (0, 255, 0)  # Green for analysis mode
        else:
            mode_text = "MODE: NORMAL TRACKING"
            mode_color = (255, 255, 255)  # White for normal mode

        cv2.putText(frame, mode_text, (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6,
                   mode_color, 2)

        # Current zoom level
        zoom_text = f"Zoom: {self.zoom_level:.1f}x"
        cv2.putText(frame, zoom_text, (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6,
                   (0, 255, 255), 2)

        # Feature visualization toggle (only show when relevant)
        if self.target_track_id is not None:
            feature_text = f"Advanced Features: {'ON' if self.show_features else 'OFF'} (F to toggle)"
            cv2.putText(frame, feature_text, (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                       (255, 255, 0), 1)
        else:
            instruction_text = "Press ENTER to select a worker for detailed analysis"
            cv2.putText(frame, instruction_text, (10, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                       (255, 255, 0), 1)

        # Performance stats
        if self.performance_stats['frame_times']:
            avg_frame_time = np.mean(self.performance_stats['frame_times'][-30:])
            fps_actual = 1000 / avg_frame_time if avg_frame_time > 0 else 0
            perf_text = f"FPS: {fps_actual:.1f} | Frame: {avg_frame_time:.1f}ms"
            cv2.putText(frame, perf_text, (10, h-60), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                       (255, 255, 255), 1)

        # Extraction stats (only when doing advanced analysis)
        if self.performance_stats['extraction_times'] and self.target_track_id is not None:
            avg_extraction = np.mean(self.performance_stats['extraction_times'][-10:])
            extraction_text = f"Energy Extraction: {avg_extraction:.1f}ms"
            cv2.putText(frame, extraction_text, (10, h-40), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                       (0, 255, 255), 1)

        # MPS status
        device_text = f"M3 Max Device: {self.energy_extractor.device}"
        cv2.putText(frame, device_text, (10, h-20), cv2.FONT_HERSHEY_SIMPLEX, 0.5,
                   (0, 255, 0), 1)

        # Input mode
        if self.input_mode:
            # Input box
            input_box = (w//2 - 150, h//2 - 30, 300, 60)
            cv2.rectangle(frame, (input_box[0], input_box[1]),
                         (input_box[0] + input_box[2], input_box[1] + input_box[3]),
                         (50, 50, 50), -1)
            cv2.rectangle(frame, (input_box[0], input_box[1]),
                         (input_box[0] + input_box[2], input_box[1] + input_box[3]),
                         (255, 255, 255), 2)

            # Input prompt
            cv2.putText(frame, "Enter Track ID:", (input_box[0] + 10, input_box[1] + 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # Input text
            cv2.putText(frame, self.input_text, (input_box[0] + 10, input_box[1] + 50),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

        # Controls help
        help_y = 150
        if self.target_track_id is not None:
            help_texts = [
                "R: Return to normal tracking",
                "F: Toggle energy features",
                "S: Save analysis frame",
                "ESC: Cancel input",
                "Q: Quit"
            ]
        else:
            help_texts = [
                "ENTER: Select worker for analysis",
                "Normal tracking mode (fast)",
                "S: Save frame",
                "Q: Quit"
            ]

        for i, help_text in enumerate(help_texts):
            cv2.putText(frame, help_text, (w - 200, help_y + i*20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (200, 200, 200), 1)

    def _handle_input(self, key: int) -> bool:
        """Handle keyboard input"""
        if key == ord('q') or key == 27:  # Q or ESC
            if self.input_mode:
                # Cancel input mode
                self.input_mode = False
                self.input_text = ""
                return True
            else:
                # Quit application
                return False

        elif key == 13:  # ENTER
            if self.input_mode:
                # Process input
                try:
                    track_id = int(self.input_text)
                    self.target_track_id = track_id
                    self.zoom_level = 4.0  # Zoom in on target
                    print(f"🎯 Selected track ID {track_id} for advanced energy analysis")
                    print(f"   Zooming to {self.zoom_level}x for detailed feature extraction")
                except ValueError:
                    print(f"⚠️ Invalid track ID: {self.input_text}")

                self.input_mode = False
                self.input_text = ""
            else:
                # Enter input mode
                self.input_mode = True
                self.input_text = ""

        elif key == ord('f'):  # Toggle feature display
            self.show_features = not self.show_features
            print(f"🔬 Feature visualization: {'ON' if self.show_features else 'OFF'}")

        elif key == ord('s'):  # Save frame
            timestamp = int(time.time())
            filename = f"advanced_energy_frame_{timestamp}.jpg"
            cv2.imwrite(filename, self._get_current_frame())
            print(f"💾 Saved frame: {filename}")

        elif key == ord('r'):  # Reset to normal tracking mode
            self.zoom_level = 1.0
            self.target_track_id = None
            self.current_features = {}
            print("🔄 Reset to normal tracking mode - fast performance restored")

        elif self.input_mode and key != 255:  # Text input
            if key == 8:  # Backspace
                self.input_text = self.input_text[:-1]
            elif 48 <= key <= 57:  # Numbers 0-9
                self.input_text += chr(key)

        return True

    def _get_current_frame(self) -> np.ndarray:
        """Get current frame for saving"""
        # This would be the last processed frame
        # For now, return a placeholder
        return np.zeros((self.frame_height, self.frame_width, 3), dtype=np.uint8)

    def _cleanup(self):
        """Clean up resources"""
        print("\n🧹 Cleaning up resources...")

        # Print final performance statistics
        self._print_final_stats()

        # Release video capture
        if self.cap:
            self.cap.release()

        # Close all windows (multiple calls for macOS compatibility)
        cv2.destroyAllWindows()
        cv2.waitKey(1)  # Allow time for window cleanup
        cv2.destroyAllWindows()

        print("✅ Cleanup completed successfully!")

    def _print_final_stats(self):
        """Print comprehensive performance statistics"""
        print("\n📊 FINAL PERFORMANCE STATISTICS")
        print("=" * 50)

        # Frame processing stats
        if self.performance_stats['frame_times']:
            avg_frame_time = np.mean(self.performance_stats['frame_times'])
            max_frame_time = np.max(self.performance_stats['frame_times'])
            min_frame_time = np.min(self.performance_stats['frame_times'])
            fps_avg = 1000 / avg_frame_time if avg_frame_time > 0 else 0

            print(f"🎬 Frame Processing:")
            print(f"   Average FPS: {fps_avg:.1f}")
            print(f"   Average frame time: {avg_frame_time:.1f}ms")
            print(f"   Min frame time: {min_frame_time:.1f}ms")
            print(f"   Max frame time: {max_frame_time:.1f}ms")
            print(f"   Total frames processed: {len(self.performance_stats['frame_times'])}")

        # Feature extraction stats
        if self.performance_stats['extraction_times']:
            avg_extraction = np.mean(self.performance_stats['extraction_times'])
            max_extraction = np.max(self.performance_stats['extraction_times'])
            min_extraction = np.min(self.performance_stats['extraction_times'])

            print(f"\n🔬 Feature Extraction:")
            print(f"   Average extraction time: {avg_extraction:.1f}ms")
            print(f"   Min extraction time: {min_extraction:.1f}ms")
            print(f"   Max extraction time: {max_extraction:.1f}ms")
            print(f"   Total extractions: {self.performance_stats['total_extractions']}")

        # Energy extractor stats
        extractor_stats = self.energy_extractor.get_performance_stats()
        print(f"\n⚡ Energy Extractor Performance:")
        print(f"   Device: {extractor_stats['device']}")
        print(f"   MPS acceleration rate: {extractor_stats['mps_acceleration_rate']:.1f}%")
        print(f"   Average extraction time: {extractor_stats['average_extraction_time_ms']:.1f}ms")
        print(f"   Total extractions: {extractor_stats['extractions_performed']}")

        # Feature analysis
        if self.current_features:
            print(f"\n🧮 Current Features Analysis:")
            for track_id, features in self.current_features.items():
                print(f"   Track {track_id}:")
                print(f"     Gradient Energy: {features.gradient_energy:.2f}")
                print(f"     Texture Energy: {features.texture_energy:.2f}")
                print(f"     Directional Energy: ({features.directional_energy_x:.2f}, {features.directional_energy_y:.2f})")
                print(f"     Confidence: {features.confidence_score:.2f}")


def main():
    """Main function to run the advanced energy demo"""
    print("🚀 Advanced Energy Feature Extraction Demo")
    print("Built with love for worker safety! 💙")
    print("=" * 60)

    # Check for command line arguments
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
    else:
        video_path = "videos/test2.mp4"  # Default fallback

    print(f"📹 Using video: {video_path}")

    if not os.path.exists(video_path):
        print(f"❌ Video file not found: {video_path}")
        print("Please ensure the video file exists or provide a valid path.")
        print("Usage: python advanced_energy_demo.py [video_path]")
        return

    try:
        # Initialize and run demo
        demo = AdvancedEnergyDemo(video_path)
        demo.run()

    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

"""
Scale-State Modeling for Chain-of-Zoom Video Tracking

This module implements the core scale-state modeling from the Chain-of-Zoom paper,
adapted for video object tracking applications.

Mathematical Foundation from Paper:
- AR-2 Modeling: p(xi|xi-1,xi-2,ci) with multi-scale-aware features
- Joint Distribution: p(x0,c1,x1,...,cn,xn) = p(x0,x1) ∏ p(xi|xi-1,xi-2,ci)p(ci|xi-1,xi-2)
- Objective: Maximize likelihood across all scale states

Adapted for Video Tracking:
- Scale states represent intermediate zoom levels in video tracking
- Features ci represent tracking-specific multi-scale features
- Probability modeling guides track association across zoom levels
- Energy conservation ensures consistent Laplacian energy scaling
"""

import numpy as np
import cv2
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
import math
import time

from .math_foundations import ZoomState, ScaleTransition, ChainOfZoomMath
from .multi_scale_features import MultiScaleFeatureExtractor, MultiScaleFeatures


@dataclass
class ScaleStateSequence:
    """Represents a sequence of scale states in the zoom chain"""
    states: List[ZoomState]
    transitions: List[ScaleTransition]
    total_probability: float
    energy_conservation_score: float
    temporal_consistency_score: float


@dataclass
class TrackAssociation:
    """Represents track association across scale states"""
    track_id: int
    confidence: float
    position_trajectory: List[Tuple[float, float]]  # (x, y) positions across scales
    energy_trajectory: List[float]  # Energy values across scales
    feature_consistency: float


class ScaleStateModeling:
    """
    Scale-State Modeling for Chain-of-Zoom Tracking
    
    This class implements the mathematical framework for modeling intermediate
    scale states in the zoom chain, enabling smooth transitions and consistent
    track associations across extreme zoom operations.
    
    Key Components:
    1. Scale state generation with AR-2 modeling
    2. Probabilistic track association across scales
    3. Energy conservation validation
    4. Temporal consistency enforcement
    """
    
    def __init__(self, 
                 energy_conservation_weight: float = 0.4,
                 spatial_consistency_weight: float = 0.3,
                 temporal_consistency_weight: float = 0.3,
                 min_association_confidence: float = 0.6):
        """
        Initialize scale-state modeling
        
        Args:
            energy_conservation_weight: Weight for energy conservation in probability
            spatial_consistency_weight: Weight for spatial consistency
            temporal_consistency_weight: Weight for temporal consistency
            min_association_confidence: Minimum confidence for track association
        """
        self.energy_weight = energy_conservation_weight
        self.spatial_weight = spatial_consistency_weight
        self.temporal_weight = temporal_consistency_weight
        self.min_confidence = min_association_confidence
        
        # Mathematical components
        self.math_engine = ChainOfZoomMath()
        self.feature_extractor = MultiScaleFeatureExtractor()
        
        # State management
        self.scale_state_cache = {}  # Cache for computed scale states
        self.association_history = []  # History of track associations
        
    def generate_scale_state_sequence(self, 
                                    initial_state: ZoomState,
                                    target_zoom: float,
                                    frame: np.ndarray,
                                    detections: List[Any]) -> ScaleStateSequence:
        """
        Generate sequence of intermediate scale states
        
        This implements the core AR-2 modeling from the paper:
        p(x0,x1,...,xn) = p(x0,x1) ∏ p(xi|xi-1,xi-2)
        
        Args:
            initial_state: Starting zoom state
            target_zoom: Target zoom level
            frame: Current video frame
            detections: Object detections
            
        Returns:
            ScaleStateSequence with intermediate states and transitions
        """
        # Decompose zoom change into intermediate levels
        zoom_chain = self.math_engine.decompose_zoom_change(
            initial_state.zoom_level, target_zoom
        )
        
        if len(zoom_chain) <= 1:
            # No intermediate states needed
            final_state = self._create_final_state(initial_state, target_zoom, frame, detections)
            return ScaleStateSequence(
                states=[initial_state, final_state],
                transitions=[self.math_engine.compute_scale_transition(initial_state, target_zoom)],
                total_probability=1.0,
                energy_conservation_score=1.0,
                temporal_consistency_score=1.0
            )
        
        # Generate intermediate scale states
        states = [initial_state]
        transitions = []
        
        prev_state = initial_state
        prev_prev_state = None
        
        for i, intermediate_zoom in enumerate(zoom_chain):
            # Compute transition to intermediate zoom level
            transition = self.math_engine.compute_scale_transition(prev_state, intermediate_zoom)
            transitions.append(transition)
            
            # Generate intermediate state
            intermediate_state = self._generate_intermediate_state(
                prev_state, prev_prev_state, intermediate_zoom, frame, detections, transition
            )
            states.append(intermediate_state)
            
            # Update for next iteration
            prev_prev_state = prev_state
            prev_state = intermediate_state
        
        # Compute sequence quality metrics
        total_prob = self._compute_sequence_probability(states)
        energy_score = self._compute_energy_conservation_score(states)
        temporal_score = self._compute_temporal_consistency_score(states)
        
        return ScaleStateSequence(
            states=states,
            transitions=transitions,
            total_probability=total_prob,
            energy_conservation_score=energy_score,
            temporal_consistency_score=temporal_score
        )
    
    def _generate_intermediate_state(self, 
                                   prev_state: ZoomState,
                                   prev_prev_state: Optional[ZoomState],
                                   zoom_level: float,
                                   frame: np.ndarray,
                                   detections: List[Any],
                                   transition: ScaleTransition) -> ZoomState:
        """
        Generate intermediate scale state using AR-2 modeling
        
        Implements: p(xi|xi-1,xi-2,ci) where ci are multi-scale features
        """
        # Apply zoom transformation to frame (simulate zoom effect)
        zoomed_frame = self._apply_zoom_transformation(frame, zoom_level / prev_state.zoom_level)
        
        # Predict track positions at this zoom level
        predicted_tracks = self._predict_tracks_at_scale(
            prev_state.tracks, transition, detections
        )
        
        # Extract multi-scale features for this zoom level
        features = self.feature_extractor.extract_features(
            zoomed_frame, predicted_tracks, zoom_level, prev_state.frame
        )
        
        # Compute Laplacian energies with scaling
        laplacian_energy = {}
        for track in predicted_tracks:
            track_id = getattr(track, 'track_id', id(track))
            
            # Use predicted energy from transition
            if track_id in transition.energy_scaling:
                predicted_energy = transition.energy_scaling[track_id]
                # Add some noise/variation for realism
                actual_energy = predicted_energy * (0.9 + 0.2 * np.random.random())
                laplacian_energy[track_id] = actual_energy
            else:
                # Compute fresh energy
                energy = self._compute_laplacian_energy(zoomed_frame, track)
                laplacian_energy[track_id] = energy
        
        return ZoomState(
            zoom_level=zoom_level,
            frame=zoomed_frame,
            tracks=predicted_tracks,
            features=features,
            laplacian_energy=laplacian_energy,
            timestamp=time.time()
        )
    
    def _predict_tracks_at_scale(self, 
                               prev_tracks: List[Any],
                               transition: ScaleTransition,
                               detections: List[Any]) -> List[Any]:
        """
        Predict track positions at intermediate scale
        
        Uses spatial prediction from transition and associates with detections
        """
        predicted_tracks = []
        
        # Create track associations based on predicted positions
        for track in prev_tracks:
            track_id = getattr(track, 'track_id', id(track))
            
            if track_id in transition.predicted_positions:
                pred_x, pred_y = transition.predicted_positions[track_id]
                
                # Find best matching detection
                best_detection = None
                best_distance = float('inf')
                
                for detection in detections:
                    # Get detection center (format may vary)
                    if hasattr(detection, 'tlbr'):
                        det_x1, det_y1, det_x2, det_y2 = detection.tlbr
                        det_cx, det_cy = (det_x1 + det_x2) / 2, (det_y1 + det_y2) / 2
                    elif len(detection) >= 4:  # Assume [x1, y1, x2, y2, ...]
                        det_x1, det_y1, det_x2, det_y2 = detection[:4]
                        det_cx, det_cy = (det_x1 + det_x2) / 2, (det_y1 + det_y2) / 2
                    else:
                        continue
                    
                    # Distance to predicted position
                    distance = math.sqrt((pred_x - det_cx)**2 + (pred_y - det_cy)**2)
                    
                    if distance < best_distance:
                        best_distance = distance
                        best_detection = detection
                
                # Create predicted track
                if best_detection is not None and best_distance < 100:  # Reasonable threshold
                    # Create a track-like object with predicted properties
                    predicted_track = self._create_predicted_track(
                        track_id, best_detection, transition.confidence
                    )
                    predicted_tracks.append(predicted_track)
        
        return predicted_tracks
    
    def _create_predicted_track(self, track_id: int, detection: Any, confidence: float) -> Any:
        """Create a predicted track object from detection"""
        # This is a simplified track object for intermediate states
        class PredictedTrack:
            def __init__(self, track_id, detection, confidence):
                self.track_id = track_id
                self.confidence = confidence
                
                # Extract bounding box
                if hasattr(detection, 'tlbr'):
                    self.tlbr = detection.tlbr
                elif len(detection) >= 4:
                    self.tlbr = detection[:4]
                else:
                    self.tlbr = [0, 0, 10, 10]  # Fallback
        
        return PredictedTrack(track_id, detection, confidence)
    
    def _apply_zoom_transformation(self, frame: np.ndarray, zoom_ratio: float) -> np.ndarray:
        """Apply zoom transformation to frame"""
        if abs(zoom_ratio - 1.0) < 0.01:
            return frame  # No significant zoom
        
        h, w = frame.shape[:2]
        center_x, center_y = w // 2, h // 2
        
        if zoom_ratio > 1.0:
            # Zoom in - crop and resize
            crop_w = int(w / zoom_ratio)
            crop_h = int(h / zoom_ratio)
            
            x1 = center_x - crop_w // 2
            y1 = center_y - crop_h // 2
            x2 = x1 + crop_w
            y2 = y1 + crop_h
            
            # Ensure bounds
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(w, x2), min(h, y2)
            
            cropped = frame[y1:y2, x1:x2]
            zoomed = cv2.resize(cropped, (w, h), interpolation=cv2.INTER_LINEAR)
        else:
            # Zoom out - resize and pad
            new_w, new_h = int(w * zoom_ratio), int(h * zoom_ratio)
            resized = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
            
            # Create padded frame
            zoomed = np.zeros_like(frame)
            start_x = (w - new_w) // 2
            start_y = (h - new_h) // 2
            zoomed[start_y:start_y+new_h, start_x:start_x+new_w] = resized
        
        return zoomed
    
    def _compute_laplacian_energy(self, frame: np.ndarray, track: Any) -> float:
        """Compute Laplacian energy for a track"""
        try:
            if hasattr(track, 'tlbr'):
                x1, y1, x2, y2 = map(int, track.tlbr)
            else:
                return 0.0
            
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(frame.shape[1], x2), min(frame.shape[0], y2)
            
            if x2 <= x1 or y2 <= y1:
                return 0.0
            
            roi = frame[y1:y2, x1:x2]
            
            if len(roi.shape) == 3:
                gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            else:
                gray = roi
            
            laplacian = cv2.Laplacian(gray, cv2.CV_64F, ksize=3)
            energy = np.sqrt(np.mean(laplacian ** 2))
            
            return energy
        except:
            return 0.0
    
    def _create_final_state(self, initial_state: ZoomState, target_zoom: float,
                           frame: np.ndarray, detections: List[Any]) -> ZoomState:
        """Create final zoom state"""
        transition = self.math_engine.compute_scale_transition(initial_state, target_zoom)
        return self._generate_intermediate_state(
            initial_state, None, target_zoom, frame, detections, transition
        )
    
    def _compute_sequence_probability(self, states: List[ZoomState]) -> float:
        """Compute total probability for scale state sequence"""
        if len(states) < 2:
            return 1.0
        
        total_prob = 1.0
        
        for i in range(2, len(states)):
            current_state = states[i]
            prev_state = states[i-1]
            prev_prev_state = states[i-2] if i >= 2 else None
            
            # Compute AR-2 probability
            prob = self.math_engine.compute_ar2_probability(
                current_state, prev_state, prev_prev_state
            )
            total_prob *= prob
        
        return total_prob
    
    def _compute_energy_conservation_score(self, states: List[ZoomState]) -> float:
        """Compute energy conservation score across states"""
        if len(states) < 2:
            return 1.0
        
        conservation_scores = []
        
        for i in range(1, len(states)):
            current_state = states[i]
            prev_state = states[i-1]
            zoom_ratio = current_state.zoom_level / prev_state.zoom_level
            
            # Check energy conservation for each track
            track_scores = []
            for track_id in current_state.laplacian_energy:
                if track_id in prev_state.laplacian_energy:
                    expected_energy = prev_state.laplacian_energy[track_id] * (zoom_ratio ** 2)
                    actual_energy = current_state.laplacian_energy[track_id]
                    
                    if expected_energy > 0:
                        ratio = min(expected_energy, actual_energy) / max(expected_energy, actual_energy)
                        track_scores.append(ratio)
            
            if track_scores:
                conservation_scores.append(np.mean(track_scores))
        
        return np.mean(conservation_scores) if conservation_scores else 1.0
    
    def _compute_temporal_consistency_score(self, states: List[ZoomState]) -> float:
        """Compute temporal consistency score across states"""
        if len(states) < 3:
            return 1.0
        
        # Check if track IDs are maintained across states
        consistency_scores = []
        
        for i in range(2, len(states)):
            current_ids = set(states[i].laplacian_energy.keys())
            prev_ids = set(states[i-1].laplacian_energy.keys())
            prev_prev_ids = set(states[i-2].laplacian_energy.keys())
            
            # Compute ID preservation rate
            common_ids = current_ids & prev_ids & prev_prev_ids
            total_ids = current_ids | prev_ids | prev_prev_ids
            
            if total_ids:
                consistency = len(common_ids) / len(total_ids)
                consistency_scores.append(consistency)
        
        return np.mean(consistency_scores) if consistency_scores else 1.0
    
    def get_modeling_stats(self) -> Dict:
        """Get scale-state modeling statistics"""
        return {
            'energy_weight': self.energy_weight,
            'spatial_weight': self.spatial_weight,
            'temporal_weight': self.temporal_weight,
            'min_confidence': self.min_confidence,
            'cache_size': len(self.scale_state_cache),
            'association_history_length': len(self.association_history)
        }

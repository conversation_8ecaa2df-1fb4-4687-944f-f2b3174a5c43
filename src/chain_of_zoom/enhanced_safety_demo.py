#!/usr/bin/env python3
"""
🛡️ Enhanced Safety Demo: Real Equipment Detection + Energy Analysis
==================================================================

This demo showcases the next-level safety monitoring system that combines:
1. REAL safety equipment detection (helmets, vests, etc.)
2. Advanced energy-based behavioral analysis
3. Comprehensive safety compliance assessment
4. Professional visualization for industrial use

This addresses the critical gap identified in the previous system:
moving from energy interpretation to FACTUAL safety equipment detection.

Controls:
- ENTER: Select track ID for enhanced analysis
- F: Toggle feature visualization
- E: Toggle equipment detection overlay
- C: Toggle compliance information
- S: Save analysis frame
- Q: Quit

Features:
- Actual helmet detection using color and shape analysis
- High-visibility vest detection with color compliance
- Safety compliance scoring based on detected equipment
- Hybrid scoring combining equipment + behavioral analysis
- Professional reporting for industrial safety management
"""

import cv2
import numpy as np
import sys
import os
import time
from typing import List, Dict, Any, Optional
from ultralytics import YOLO

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.chain_of_zoom.chain_of_zoom_tracker import ChainOfZoomTracker
from src.chain_of_zoom.advanced_energy_extraction import AdvancedEnergyExtractor
from src.chain_of_zoom.real_safety_detection import SafetyEquipmentDetector, HybridSafetyAnalyzer
from src.core.zoom_tracking_wrapper import ZoomTrackingWrapper


class EnhancedSafetyDemo:
    """
    Enhanced Safety Demo with Real Equipment Detection
    
    This demo represents the next evolution of the safety monitoring system,
    moving beyond energy interpretation to factual safety equipment detection.
    """
    
    def __init__(self, video_path: str = "videos/test4.mp4", environment: str = "construction"):
        """Initialize the enhanced safety demo"""
        print("🛡️ Initializing Enhanced Safety Demo with Real Equipment Detection...")
        print("   Moving beyond energy interpretation to FACTUAL safety analysis! 🚀")
        
        # Video setup
        self.video_path = video_path
        self.environment = environment
        self.cap = cv2.VideoCapture(video_path)
        if not self.cap.isOpened():
            raise ValueError(f"❌ Cannot open video: {video_path}")
        
        # Get video properties
        self.fps = int(self.cap.get(cv2.CAP_PROP_FPS))
        self.frame_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.frame_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"📹 Video: {video_path}")
        print(f"   Resolution: {self.frame_width}x{self.frame_height}")
        print(f"   FPS: {self.fps}")
        print(f"   Environment: {environment}")
        
        # Initialize YOLO model
        print("🤖 Loading YOLO model...")
        self.model = YOLO('yolov8n.pt')
        
        # Initialize Chain-of-Zoom tracker (using proven pattern from advanced_energy_demo)
        print("🔗 Initializing Chain-of-Zoom tracker...")
        # We'll use YOLO's built-in tracking like the proven advanced energy demo
        # No need for separate base tracker - YOLO handles it internally
        
        # Initialize Advanced Energy Extractor
        print("⚡ Initializing Advanced Energy Extractor...")
        self.energy_extractor = AdvancedEnergyExtractor(
            multi_scale_sigmas=[1.0, 2.0, 4.0, 8.0],
            lbp_radius=3,
            lbp_n_points=24,
            enable_mps=True,
            debug=False
        )
        
        # Initialize Real Safety Equipment Detector
        print("🛡️ Initializing Real Safety Equipment Detector...")
        self.safety_detector = SafetyEquipmentDetector(device="mps")
        
        # Initialize Hybrid Safety Analyzer
        print("🔬 Initializing Hybrid Safety Analyzer...")
        self.hybrid_analyzer = HybridSafetyAnalyzer(self.energy_extractor, self.safety_detector)
        
        # Demo state
        self.selected_track_id = None
        self.zoom_level = 1.0
        self.target_zoom = 4.0
        self.show_features = True
        self.show_equipment = True
        self.show_compliance = True
        self.input_mode = False
        self.input_text = ""
        self.current_safety_analysis = None
        
        print("✅ Enhanced Safety Demo initialized successfully!")
        print()
        self._print_controls()
    
    def _print_controls(self):
        """Print demo controls"""
        print("🎮 ENHANCED SAFETY DEMO CONTROLS:")
        print("   ENTER: Select track ID for enhanced analysis")
        print("   F: Toggle feature visualization")
        print("   E: Toggle equipment detection overlay")
        print("   C: Toggle compliance information")
        print("   S: Save analysis frame")
        print("   ESC: Reset zoom / Cancel input")
        print("   Q: Quit")
        print()
        print("🛡️ REAL SAFETY DETECTION FEATURES:")
        print("   • Actual helmet detection (color + shape analysis)")
        print("   • High-visibility vest detection (compliance colors)")
        print("   • Safety compliance scoring (environment-specific)")
        print("   • Hybrid analysis (equipment + behavioral)")
        print("   • Professional safety reporting")
        print()
    
    def run(self):
        """Run the enhanced safety demo using proven pattern from advanced_energy_demo"""
        print("🚀 Starting Enhanced Safety Demo...")

        # Create window first (proven pattern)
        window_name = 'Enhanced Safety Demo - Real Equipment Detection'
        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(window_name, 1280, 720)

        # Create initial frame to show window
        initial_frame = np.zeros((720, 1280, 3), dtype=np.uint8)
        cv2.putText(initial_frame, "🛡️ Enhanced Safety Demo - Real Equipment Detection",
                   (50, 100), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 2)
        cv2.putText(initial_frame, "Loading video and initializing safety detection...",
                   (50, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
        cv2.putText(initial_frame, "Press any key to begin!",
                   (50, 200), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

        cv2.imshow(window_name, initial_frame)
        print("🖥️ Video window should now be visible!")
        print("Press any key in the video window to begin...")

        # Wait for user to press key in the video window
        cv2.waitKey(0)

        try:
            while True:
                ret, frame = self.cap.read()
                if not ret:
                    print("📹 End of video reached, restarting...")
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    continue

                start_time = time.time()

                # Process frame using proven pattern
                processed_frame = self._process_frame(frame)

                # Display frame BEFORE handling input (important for macOS)
                cv2.imshow(window_name, processed_frame)

                # Handle user input
                if self.selected_track_id is not None:
                    key = cv2.waitKey(30) & 0xFF  # Slower when analyzing
                else:
                    key = cv2.waitKey(1) & 0xFF   # Fast for normal tracking

                if not self._handle_keyboard_input(key):
                    break

                # Performance tracking
                frame_time = (time.time() - start_time) * 1000

        except KeyboardInterrupt:
            print("\n⏹️ Demo interrupted by user")
        finally:
            self.cap.release()
            cv2.destroyAllWindows()
            print("👋 Enhanced Safety Demo completed!")

    def _process_frame(self, frame: np.ndarray) -> np.ndarray:
        """Process frame with YOLO detection and safety analysis (using proven pattern)"""
        # YOLO detection with tracking (proven pattern from advanced_energy_demo)
        results = self.model.track(frame, persist=True, verbose=False)

        # Extract detections and create tracks
        tracks = []
        if results[0].boxes is not None and results[0].boxes.id is not None:
            boxes = results[0].boxes.xyxy.cpu().numpy()
            track_ids = results[0].boxes.id.cpu().numpy().astype(int)
            confidences = results[0].boxes.conf.cpu().numpy()

            for i, (box, track_id, conf) in enumerate(zip(boxes, track_ids, confidences)):
                # Create simple track object (proven pattern)
                track = type('Track', (), {
                    'track_id': track_id,
                    'tlbr': box,
                    'bbox': (int(box[0]), int(box[1]), int(box[2]), int(box[3])),
                    'confidence': conf
                })()
                tracks.append(track)

        # Safety analysis - ONLY for selected target or when needed
        if tracks and self.selected_track_id is not None:
            # Find selected track
            target_tracks = [t for t in tracks if t.track_id == self.selected_track_id]
            if target_tracks:
                target_track = target_tracks[0]

                # Perform comprehensive safety analysis
                safety_analysis = self.hybrid_analyzer.analyze_worker_safety(
                    frame,
                    target_track.bbox,
                    self.selected_track_id,
                    self.environment
                )

                # Store analysis for visualization
                self.current_safety_analysis = safety_analysis

        # Create visualization
        vis_frame = self._create_visualization(frame, tracks)

        return vis_frame

    def _create_visualization(self, frame: np.ndarray, tracks: List[Any]) -> np.ndarray:
        """Create comprehensive visualization with safety analysis"""
        vis_frame = frame.copy()

        if self.selected_track_id is not None and self.current_safety_analysis is not None:
            # Zoomed analysis mode - show detailed safety analysis
            vis_frame = self._create_zoomed_analysis_view(vis_frame, tracks)
        else:
            # Normal tracking mode - show all tracks with basic info
            vis_frame = self._create_normal_tracking_view(vis_frame, tracks)

        # Handle input mode overlay
        if self.input_mode:
            self._draw_input_overlay(vis_frame)

        return vis_frame

    def _create_normal_tracking_view(self, frame: np.ndarray, tracks: List[Any]) -> np.ndarray:
        """Create normal tracking view showing all workers"""

        # Draw all tracks with basic info
        for track in tracks:
            x1, y1, x2, y2 = track.bbox
            track_id = track.track_id
            confidence = track.confidence

            # Draw bounding box
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)

            # Draw track ID and confidence
            label = f"Worker {track_id} ({confidence:.2f})"
            cv2.putText(frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

        # Draw instructions
        cv2.putText(frame, "🛡️ Enhanced Safety Demo - Press ENTER to select a worker for analysis",
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        return frame

    def _create_zoomed_analysis_view(self, frame: np.ndarray, tracks: List[Any]) -> np.ndarray:
        """Create zoomed analysis view with detailed safety information"""

        # Find selected track
        selected_track = None
        for track in tracks:
            if track.track_id == self.selected_track_id:
                selected_track = track
                break

        if selected_track is None:
            # Track lost, reset
            self.selected_track_id = None
            self.current_safety_analysis = None
            return frame

        # Apply zoom (simplified version for now)
        x1, y1, x2, y2 = selected_track.bbox
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2

        # Calculate zoom region
        zoom_width = int(frame.shape[1] / self.target_zoom)
        zoom_height = int(frame.shape[0] / self.target_zoom)

        zoom_x1 = max(0, center_x - zoom_width // 2)
        zoom_y1 = max(0, center_y - zoom_height // 2)
        zoom_x2 = min(frame.shape[1], zoom_x1 + zoom_width)
        zoom_y2 = min(frame.shape[0], zoom_y1 + zoom_height)

        # Extract and resize zoom region
        zoom_region = frame[zoom_y1:zoom_y2, zoom_x1:zoom_x2]
        zoomed_frame = cv2.resize(zoom_region, (frame.shape[1], frame.shape[0]))

        # Adjust bounding box for zoomed frame
        adjusted_bbox = (
            int((x1 - zoom_x1) * self.target_zoom),
            int((y1 - zoom_y1) * self.target_zoom),
            int((x2 - zoom_x1) * self.target_zoom),
            int((y2 - zoom_y1) * self.target_zoom)
        )

        # Draw safety analysis on zoomed frame
        zoomed_frame = self._draw_safety_analysis(zoomed_frame, adjusted_bbox, self.current_safety_analysis)

        return zoomed_frame

    def _process_normal_frame(self, frame: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """Process frame in normal tracking mode"""
        
        # Draw all detections with basic info
        for detection in detections:
            track_id = detection['track_id']
            x1, y1, x2, y2 = detection['bbox']
            confidence = detection['confidence']
            
            # Draw bounding box
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # Draw track ID and confidence
            label = f"ID: {track_id} ({confidence:.2f})"
            cv2.putText(frame, label, (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # Draw instructions
        cv2.putText(frame, "Press ENTER to select a worker for enhanced safety analysis", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        return frame
    
    def _process_zoomed_frame(self, frame: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """Process frame in zoomed analysis mode"""
        
        # Find selected track
        selected_detection = None
        for detection in detections:
            if detection['track_id'] == self.selected_track_id:
                selected_detection = detection
                break
        
        if selected_detection is None:
            # Track lost, reset
            self.selected_track_id = None
            self.zoom_level = 1.0
            return frame
        
        # Zoom into selected worker
        x1, y1, x2, y2 = selected_detection['bbox']
        
        # Calculate zoom region
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        
        zoom_width = int(self.frame_width / self.target_zoom)
        zoom_height = int(self.frame_height / self.target_zoom)
        
        zoom_x1 = max(0, center_x - zoom_width // 2)
        zoom_y1 = max(0, center_y - zoom_height // 2)
        zoom_x2 = min(self.frame_width, zoom_x1 + zoom_width)
        zoom_y2 = min(self.frame_height, zoom_y1 + zoom_height)
        
        # Extract and resize zoom region
        zoom_region = frame[zoom_y1:zoom_y2, zoom_x1:zoom_x2]
        zoomed_frame = cv2.resize(zoom_region, (self.frame_width, self.frame_height))
        
        # Adjust bounding box for zoomed frame
        adjusted_bbox = (
            int((x1 - zoom_x1) * self.target_zoom),
            int((y1 - zoom_y1) * self.target_zoom),
            int((x2 - zoom_x1) * self.target_zoom),
            int((y2 - zoom_y1) * self.target_zoom)
        )
        
        # Perform enhanced safety analysis
        safety_analysis = self.hybrid_analyzer.analyze_worker_safety(
            zoom_region, 
            (x1 - zoom_x1, y1 - zoom_y1, x2 - zoom_x1, y2 - zoom_y1),
            self.selected_track_id,
            self.environment
        )
        
        # Draw analysis results
        zoomed_frame = self._draw_safety_analysis(zoomed_frame, adjusted_bbox, safety_analysis)
        
        return zoomed_frame
    
    def _draw_safety_analysis(self, frame: np.ndarray, bbox: tuple, analysis: Dict[str, Any]) -> np.ndarray:
        """Draw comprehensive safety analysis on frame"""
        
        x1, y1, x2, y2 = bbox
        
        # Draw worker bounding box with safety color coding
        compliance = analysis['equipment_detection']['compliance']
        risk_color = self._get_risk_color(compliance.risk_level)
        cv2.rectangle(frame, (x1, y1), (x2, y2), risk_color, 3)
        
        # Draw safety status above worker
        safety_score = analysis['hybrid_score']['overall_safety']
        status_text = f"🛡️ Safety: {safety_score:.0f}% ({compliance.risk_level})"
        cv2.putText(frame, status_text, (x1, y1-15), cv2.FONT_HERSHEY_SIMPLEX, 0.6, risk_color, 2)
        
        # Draw detailed analysis panel
        if self.show_compliance:
            self._draw_compliance_panel(frame, analysis)
        
        if self.show_equipment:
            self._draw_equipment_detection(frame, analysis)
        
        if self.show_features:
            self._draw_energy_features(frame, analysis)
        
        return frame
    
    def _draw_compliance_panel(self, frame: np.ndarray, analysis: Dict[str, Any]):
        """Draw safety compliance panel"""
        compliance = analysis['equipment_detection']['compliance']
        
        # Panel background
        panel_x, panel_y = 10, 50
        panel_width, panel_height = 400, 200
        
        overlay = frame.copy()
        cv2.rectangle(overlay, (panel_x, panel_y), (panel_x + panel_width, panel_y + panel_height), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # Title
        cv2.putText(frame, "🛡️ SAFETY COMPLIANCE ANALYSIS", (panel_x + 10, panel_y + 25), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
        y_offset = 50
        
        # Compliance percentage
        compliance_color = self._get_risk_color(compliance.risk_level)
        cv2.putText(frame, f"Compliance: {compliance.compliance_percentage*100:.0f}%", 
                   (panel_x + 10, panel_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.5, compliance_color, 1)
        y_offset += 25
        
        # Risk level
        cv2.putText(frame, f"Risk Level: {compliance.risk_level}", 
                   (panel_x + 10, panel_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.5, compliance_color, 1)
        y_offset += 25
        
        # Detected equipment
        cv2.putText(frame, "Detected Equipment:", (panel_x + 10, panel_y + y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
        y_offset += 20
        
        for equipment in compliance.detected_equipment:
            equipment_text = f"✅ {equipment.equipment_type} ({equipment.confidence:.2f})"
            cv2.putText(frame, equipment_text, (panel_x + 20, panel_y + y_offset), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.35, (0, 255, 0), 1)
            y_offset += 15
        
        # Missing equipment
        if compliance.missing_equipment:
            cv2.putText(frame, "Missing Equipment:", (panel_x + 10, panel_y + y_offset), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            y_offset += 20
            
            for missing in compliance.missing_equipment:
                missing_text = f"❌ {missing}"
                cv2.putText(frame, missing_text, (panel_x + 20, panel_y + y_offset), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.35, (0, 0, 255), 1)
                y_offset += 15
    
    def _draw_equipment_detection(self, frame: np.ndarray, analysis: Dict[str, Any]):
        """Draw equipment detection details"""
        detected_equipment = analysis['equipment_detection']['compliance'].detected_equipment
        
        # Draw equipment bounding boxes and details
        for equipment in detected_equipment:
            eq_x1, eq_y1, eq_x2, eq_y2 = equipment.bbox
            
            # Draw equipment bounding box
            eq_color = (0, 255, 0) if equipment.compliance_score > 0.5 else (0, 165, 255)
            cv2.rectangle(frame, (eq_x1, eq_y1), (eq_x2, eq_y2), eq_color, 2)
            
            # Draw equipment label
            eq_label = f"{equipment.equipment_type} ({equipment.confidence:.2f})"
            cv2.putText(frame, eq_label, (eq_x1, eq_y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, eq_color, 1)
    
    def _draw_energy_features(self, frame: np.ndarray, analysis: Dict[str, Any]):
        """Draw energy features panel"""
        energy = analysis['energy_features']
        
        # Energy panel
        panel_x, panel_y = frame.shape[1] - 350, 50
        panel_width, panel_height = 340, 150
        
        overlay = frame.copy()
        cv2.rectangle(overlay, (panel_x, panel_y), (panel_x + panel_width, panel_y + panel_height), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, frame, 0.3, 0, frame)
        
        # Title
        cv2.putText(frame, "⚡ ENERGY ANALYSIS", (panel_x + 10, panel_y + 25), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        y_offset = 50
        
        # Energy features
        features = [
            f"Gradient: {energy['gradient_energy']:.1f}",
            f"Texture: {energy['texture_energy']:.1f}",
            f"Dir X: {energy['directional_energy_x']:.1f}",
            f"Dir Y: {energy['directional_energy_y']:.1f}"
        ]
        
        for feature in features:
            cv2.putText(frame, feature, (panel_x + 10, panel_y + y_offset), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            y_offset += 20
        
        # Hybrid scores
        hybrid = analysis['hybrid_score']
        cv2.putText(frame, f"Equipment: {hybrid['equipment_compliance']:.0f}%", 
                   (panel_x + 10, panel_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
        y_offset += 15
        cv2.putText(frame, f"Behavioral: {hybrid['behavioral_analysis']:.0f}%", 
                   (panel_x + 10, panel_y + y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
    
    def _get_risk_color(self, risk_level: str) -> tuple:
        """Get color for risk level"""
        colors = {
            'LOW': (0, 255, 0),      # Green
            'MEDIUM': (0, 255, 255), # Yellow
            'HIGH': (0, 165, 255),   # Orange
            'CRITICAL': (0, 0, 255)  # Red
        }
        return colors.get(risk_level, (128, 128, 128))
    
    def _draw_input_overlay(self, frame: np.ndarray):
        """Draw input overlay for track ID selection"""
        overlay = frame.copy()
        cv2.rectangle(overlay, (100, 200), (500, 300), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.8, frame, 0.2, 0, frame)
        
        cv2.putText(frame, "Enter Track ID for Enhanced Analysis:", 
                   (120, 230), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(frame, f"ID: {self.input_text}", 
                   (120, 270), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2)
    
    def _handle_keyboard_input(self, key: int) -> bool:
        """Handle keyboard input"""
        if self.input_mode:
            if key == 13:  # Enter
                try:
                    self.selected_track_id = int(self.input_text)
                    self.zoom_level = self.target_zoom
                    print(f"🎯 Selected track ID {self.selected_track_id} for enhanced safety analysis")
                    print(f"   Zooming to {self.target_zoom}x for detailed equipment detection")
                except ValueError:
                    print("❌ Invalid track ID")
                self.input_mode = False
                self.input_text = ""
            elif key == 27:  # Escape
                self.input_mode = False
                self.input_text = ""
            elif key == 8:  # Backspace
                self.input_text = self.input_text[:-1]
            elif 48 <= key <= 57:  # Numbers 0-9
                self.input_text += chr(key)
        else:
            if key == 13:  # Enter
                self.input_mode = True
                self.input_text = ""
            elif key == 27:  # Escape
                self.selected_track_id = None
                self.current_safety_analysis = None
                self.zoom_level = 1.0
                print("🔄 Reset to normal tracking mode")
            elif key == ord('f') or key == ord('F'):
                self.show_features = not self.show_features
                print(f"🔬 Energy features: {'ON' if self.show_features else 'OFF'}")
            elif key == ord('e') or key == ord('E'):
                self.show_equipment = not self.show_equipment
                print(f"🛡️ Equipment detection: {'ON' if self.show_equipment else 'OFF'}")
            elif key == ord('c') or key == ord('C'):
                self.show_compliance = not self.show_compliance
                print(f"📊 Compliance panel: {'ON' if self.show_compliance else 'OFF'}")
            elif key == ord('s') or key == ord('S'):
                timestamp = int(time.time())
                filename = f"enhanced_safety_analysis_{timestamp}.jpg"
                # Note: frame not available here, would need to pass it
                print(f"💾 Save functionality - frame capture needed")
            elif key == ord('q') or key == ord('Q'):
                return False

        return True


def main():
    """Main function to run the enhanced safety demo"""
    print("🛡️ Enhanced Safety Demo - Real Equipment Detection")
    print("Moving beyond energy interpretation to FACTUAL safety analysis! 🚀")
    print("=" * 70)
    
    # Check for command line arguments
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
    else:
        video_path = "videos/test4.mp4"  # Default
    
    environment = "construction"  # Default environment
    if len(sys.argv) > 2:
        environment = sys.argv[2]
    
    print(f"📹 Using video: {video_path}")
    print(f"🏗️ Environment: {environment}")
    
    if not os.path.exists(video_path):
        print(f"❌ Video file not found: {video_path}")
        print("Please ensure the video file exists or provide a valid path.")
        print("Usage: python enhanced_safety_demo.py [video_path] [environment]")
        return
    
    try:
        # Initialize and run demo
        demo = EnhancedSafetyDemo(video_path, environment)
        demo.run()
    
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

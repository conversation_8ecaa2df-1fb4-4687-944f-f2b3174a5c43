"""
AutoZoom Integration for Chain-of-Zoom Tracker

This module provides seamless integration between the Chain-of-Zoom tracker
and the team's AutoZoom PTZ system for industrial safety applications.

Key Integration Points:
1. Enhanced streak detection with persistent track IDs
2. Improved confidence evaluation across extreme zoom levels
3. Reliable repeated alarm prevention with consistent IDs
4. Optimized PTZ control with zoom decomposition
5. Industrial safety focus with life-saving applications

AutoZoom System Parameters (from team's requirements):
- C++ Side: frame-window, detection-percentage, track-limit, autozoom-conf-threshold
- Python Side: ZOOM_TIMEOUT_SECONDS, AUTOZOOM_CONF_THRESHOLD, RATIO_SCALE, etc.

Chain-of-Zoom Enhancements:
- Extreme zoom capability (8x-32x) with maintained track IDs
- AR-2 modeling for robust zoom transitions
- Multi-scale feature extraction for better decision making
- Energy conservation across zoom chain
"""

import numpy as np
import cv2
import time
from typing import List, Dict, Optional, Any, Tuple, Callable
from dataclasses import dataclass
from collections import defaultdict, deque

from .chain_of_zoom_tracker import ChainOfZoom<PERSON>racker
from .math_foundations import ZoomState, ScaleTransition


@dataclass
class AutoZoomConfig:
    """Configuration for AutoZoom integration"""
    # C++ side parameters
    frame_window: int = 10
    detection_percentage: float = 0.7
    track_limit: int = 50
    autozoom_conf_threshold: float = 0.6
    
    # Python side parameters
    zoom_timeout_seconds: int = 30
    autozoom_url: str = "http://localhost:8080"
    ratio_scale: float = 1.5
    skip_frame: int = 10
    zoom_in_wait_seconds: int = 3
    display_confidence: int = 1
    conf_display_thickness: int = 2
    zoom_out_wait_seconds: int = 2
    clear_cache_trackid_seconds: int = 60
    clear_cache_loc_seconds: int = 30
    zoom_time: int = 5
    
    # Chain-of-Zoom specific parameters
    enable_extreme_zoom: bool = True
    extreme_zoom_threshold: float = 2.0
    max_zoom_steps: int = 8
    chain_of_zoom_confidence_boost: float = 0.1


@dataclass
class StreakDetection:
    """Enhanced streak detection with Chain-of-Zoom"""
    track_id: int
    detections: List[Dict]  # List of detection data
    max_confidence: float
    start_frame: int
    last_frame: int
    zoom_levels: List[float]  # Zoom levels during streak
    consistent_across_zoom: bool  # Whether streak survived zoom changes


@dataclass
class AutoZoomDecision:
    """AutoZoom decision with Chain-of-Zoom enhancement"""
    track_id: int
    action: str  # 'zoom_in', 'send_alert', 'no_action'
    confidence: float
    predicted_position: Tuple[float, float]
    zoom_chain: List[float]  # For extreme zoom operations
    reasoning: str


class AutoZoomIntegration:
    """
    Integration layer between Chain-of-Zoom tracker and AutoZoom system
    
    This class provides enhanced AutoZoom capabilities with Chain-of-Zoom
    tracking for industrial safety applications. It maintains the existing
    AutoZoom API while adding extreme zoom tracking capabilities.
    
    Key Features:
    1. Drop-in replacement for existing AutoZoom tracking
    2. Enhanced streak detection with persistent IDs
    3. Extreme zoom capability (8x-32x) with stable tracking
    4. Improved confidence evaluation across zoom levels
    5. Industrial safety optimizations
    """
    
    def __init__(self, base_tracker, config: AutoZoomConfig = None):
        """
        Initialize AutoZoom integration
        
        Args:
            base_tracker: Base tracker (DeepSORT, ByteTrack, etc.)
            config: AutoZoom configuration parameters
        """
        self.config = config or AutoZoomConfig()
        
        # Initialize Chain-of-Zoom tracker
        self.chain_tracker = ChainOfZoomTracker(
            base_tracker=base_tracker,
            extreme_zoom_threshold=self.config.extreme_zoom_threshold,
            max_zoom_steps=self.config.max_zoom_steps,
            debug=True
        )
        
        # AutoZoom state management
        self.streak_detections = {}  # track_id -> StreakDetection
        self.zoom_history = deque(maxlen=100)  # Recent zoom operations
        self.alert_history = {}  # track_id -> last alert time
        self.location_cache = {}  # For repeated alarm prevention
        
        # Performance tracking
        self.stats = {
            'total_frames': 0,
            'zoom_operations': 0,
            'alerts_sent': 0,
            'extreme_zoom_operations': 0,
            'id_preservations_during_zoom': 0,
            'false_alerts_prevented': 0
        }
        
        print("🔗 AutoZoom + Chain-of-Zoom Integration initialized")
        print(f"   Extreme zoom enabled: {self.config.enable_extreme_zoom}")
        print(f"   Extreme zoom threshold: {self.config.extreme_zoom_threshold}x")
        print(f"   Industrial safety focus: ENABLED")
    
    def process_frame(self, frame: np.ndarray, detections: List[Any], 
                     current_zoom: float, ptz_callback: Optional[Callable] = None) -> Dict:
        """
        Main processing method for AutoZoom integration
        
        This replaces the existing AutoZoom processing with Chain-of-Zoom enhancement
        
        Args:
            frame: Current video frame
            detections: Object detections from AI
            current_zoom: Current PTZ zoom level
            ptz_callback: Callback function for PTZ control
            
        Returns:
            Dictionary with processing results and decisions
        """
        self.stats['total_frames'] += 1
        
        # Enhanced tracking with Chain-of-Zoom
        tracks = self.chain_tracker.update(frame, detections, current_zoom)
        
        # Update streak detections with persistent IDs
        self._update_streak_detections(tracks, current_zoom)
        
        # Make AutoZoom decisions with Chain-of-Zoom intelligence
        decisions = self._make_autozoom_decisions(tracks, frame, current_zoom)
        
        # Execute PTZ operations if callback provided
        executed_actions = []
        if ptz_callback:
            executed_actions = self._execute_ptz_actions(decisions, ptz_callback)
        
        # Clean up old data
        self._cleanup_old_data()
        
        return {
            'tracks': tracks,
            'decisions': decisions,
            'executed_actions': executed_actions,
            'stats': self.get_integration_stats(),
            'chain_of_zoom_active': self._is_chain_of_zoom_active(current_zoom)
        }
    
    def _update_streak_detections(self, tracks: List[Any], current_zoom: float) -> None:
        """Update streak detection with enhanced Chain-of-Zoom tracking"""
        current_frame = self.stats['total_frames']
        active_track_ids = set()
        
        for track in tracks:
            track_id = getattr(track, 'track_id', id(track))
            confidence = getattr(track, 'confidence', 0.5)
            active_track_ids.add(track_id)
            
            # Get bounding box
            if hasattr(track, 'tlbr'):
                bbox = track.tlbr
            elif hasattr(track, 'to_ltrb'):
                bbox = track.to_ltrb()
            else:
                bbox = [0, 0, 10, 10]
            
            detection_data = {
                'frame': current_frame,
                'confidence': confidence,
                'bbox': bbox,
                'zoom_level': current_zoom,
                'timestamp': time.time()
            }
            
            if track_id in self.streak_detections:
                # Update existing streak
                streak = self.streak_detections[track_id]
                streak.detections.append(detection_data)
                streak.max_confidence = max(streak.max_confidence, confidence)
                streak.last_frame = current_frame
                streak.zoom_levels.append(current_zoom)
                
                # Check if streak survived zoom changes (Chain-of-Zoom benefit!)
                zoom_changes = len(set(streak.zoom_levels[-self.config.frame_window:]))
                streak.consistent_across_zoom = zoom_changes > 1
                
                # Maintain window size
                if len(streak.detections) > self.config.frame_window:
                    streak.detections.pop(0)
                    
            else:
                # New streak
                self.streak_detections[track_id] = StreakDetection(
                    track_id=track_id,
                    detections=[detection_data],
                    max_confidence=confidence,
                    start_frame=current_frame,
                    last_frame=current_frame,
                    zoom_levels=[current_zoom],
                    consistent_across_zoom=False
                )
        
        # Remove inactive streaks
        inactive_ids = set(self.streak_detections.keys()) - active_track_ids
        for track_id in inactive_ids:
            if current_frame - self.streak_detections[track_id].last_frame > self.config.frame_window:
                del self.streak_detections[track_id]
    
    def _make_autozoom_decisions(self, tracks: List[Any], frame: np.ndarray, 
                                current_zoom: float) -> List[AutoZoomDecision]:
        """Make AutoZoom decisions with Chain-of-Zoom intelligence"""
        decisions = []
        
        for track_id, streak in self.streak_detections.items():
            # Check if streak meets criteria
            if not self._meets_streak_criteria(streak):
                continue
            
            # Enhanced confidence evaluation with Chain-of-Zoom
            enhanced_confidence = self._compute_enhanced_confidence(streak)
            
            # Predict position with Chain-of-Zoom spatial prediction
            predicted_pos = self._predict_position_with_chain(streak, current_zoom)
            
            # Check for repeated alarms (with consistent track IDs!)
            if self._is_repeated_alarm(track_id, predicted_pos):
                continue
            
            # Make decision based on enhanced confidence
            if enhanced_confidence < self.config.autozoom_conf_threshold:
                # Zoom in for better detection
                if self._can_zoom_in(track_id):
                    zoom_chain = self._plan_zoom_chain(current_zoom, predicted_pos)
                    decision = AutoZoomDecision(
                        track_id=track_id,
                        action='zoom_in',
                        confidence=enhanced_confidence,
                        predicted_position=predicted_pos,
                        zoom_chain=zoom_chain,
                        reasoning=f"Low confidence ({enhanced_confidence:.2f}) - zoom for verification"
                    )
                    decisions.append(decision)
                    
            elif enhanced_confidence > self.config.autozoom_conf_threshold:
                # High confidence - send alert
                decision = AutoZoomDecision(
                    track_id=track_id,
                    action='send_alert',
                    confidence=enhanced_confidence,
                    predicted_position=predicted_pos,
                    zoom_chain=[],
                    reasoning=f"High confidence ({enhanced_confidence:.2f}) - safety alert"
                )
                decisions.append(decision)
        
        return decisions
    
    def _meets_streak_criteria(self, streak: StreakDetection) -> bool:
        """Check if streak meets AutoZoom criteria"""
        # Must have minimum detections in window
        recent_detections = len(streak.detections)
        required_detections = int(self.config.frame_window * self.config.detection_percentage)
        
        return recent_detections >= required_detections
    
    def _compute_enhanced_confidence(self, streak: StreakDetection) -> float:
        """Compute enhanced confidence with Chain-of-Zoom benefits"""
        base_confidence = streak.max_confidence
        
        # Boost confidence if streak survived zoom changes (Chain-of-Zoom benefit!)
        if streak.consistent_across_zoom:
            base_confidence += self.config.chain_of_zoom_confidence_boost
            self.stats['id_preservations_during_zoom'] += 1
        
        # Boost confidence for longer streaks
        streak_length = len(streak.detections)
        length_boost = min(0.1, streak_length / self.config.frame_window * 0.1)
        
        enhanced_confidence = min(1.0, base_confidence + length_boost)
        return enhanced_confidence
    
    def _predict_position_with_chain(self, streak: StreakDetection, 
                                   current_zoom: float) -> Tuple[float, float]:
        """Predict position using Chain-of-Zoom spatial prediction"""
        if not streak.detections:
            return (0.0, 0.0)
        
        # Get recent positions
        recent_positions = []
        for detection in streak.detections[-3:]:  # Last 3 detections
            bbox = detection['bbox']
            cx = (bbox[0] + bbox[2]) / 2
            cy = (bbox[1] + bbox[3]) / 2
            recent_positions.append((cx, cy))
        
        if len(recent_positions) == 1:
            return recent_positions[0]
        
        # Simple linear prediction
        if len(recent_positions) >= 2:
            x1, y1 = recent_positions[-2]
            x2, y2 = recent_positions[-1]
            
            # Predict next position
            dx, dy = x2 - x1, y2 - y1
            predicted_x = x2 + dx
            predicted_y = y2 + dy
            
            return (predicted_x, predicted_y)
        
        return recent_positions[-1]
    
    def _is_repeated_alarm(self, track_id: int, position: Tuple[float, float]) -> bool:
        """Check for repeated alarms with enhanced track ID consistency"""
        current_time = time.time()
        
        # Check time-based cache
        if track_id in self.alert_history:
            last_alert_time = self.alert_history[track_id]
            if current_time - last_alert_time < self.config.clear_cache_trackid_seconds:
                return True
        
        # Check location-based cache (with RATIO_SCALE)
        px, py = position
        for cached_pos, cache_time in self.location_cache.items():
            if current_time - cache_time < self.config.clear_cache_loc_seconds:
                cx, cy = cached_pos
                distance = np.sqrt((px - cx)**2 + (py - cy)**2)
                
                # Use RATIO_SCALE for distance threshold
                if distance < self.config.ratio_scale * 50:  # 50 pixels base threshold
                    return True
        
        return False
    
    def _can_zoom_in(self, track_id: int) -> bool:
        """Check if zoom in is allowed for this track"""
        current_time = time.time()
        
        # Check recent zoom operations
        for zoom_op in self.zoom_history:
            if (zoom_op['track_id'] == track_id and 
                current_time - zoom_op['timestamp'] < self.config.zoom_timeout_seconds):
                return False
        
        return True
    
    def _plan_zoom_chain(self, current_zoom: float, 
                        predicted_pos: Tuple[float, float]) -> List[float]:
        """Plan zoom chain for extreme zoom operations"""
        if not self.config.enable_extreme_zoom:
            # Standard zoom operation
            target_zoom = min(current_zoom * 2.0, 8.0)  # Max 8x zoom
            return [target_zoom]
        
        # Chain-of-Zoom planning for extreme operations
        target_zoom = min(current_zoom * 4.0, 16.0)  # Up to 16x with Chain-of-Zoom
        
        # Use math engine to decompose zoom
        zoom_chain = self.chain_tracker.math_engine.decompose_zoom_change(
            current_zoom, target_zoom
        )
        
        return zoom_chain
    
    def _execute_ptz_actions(self, decisions: List[AutoZoomDecision], 
                           ptz_callback: Callable) -> List[Dict]:
        """Execute PTZ actions with Chain-of-Zoom coordination"""
        executed_actions = []
        
        for decision in decisions:
            if decision.action == 'zoom_in':
                # Execute zoom chain
                for zoom_level in decision.zoom_chain:
                    action_result = ptz_callback({
                        'action': 'zoom',
                        'zoom_level': zoom_level,
                        'position': decision.predicted_position,
                        'track_id': decision.track_id,
                        'chain_of_zoom': len(decision.zoom_chain) > 1
                    })
                    executed_actions.append(action_result)
                    
                    # Wait between zoom steps for Chain-of-Zoom
                    if len(decision.zoom_chain) > 1:
                        time.sleep(self.config.zoom_in_wait_seconds / len(decision.zoom_chain))
                
                # Record zoom operation
                self.zoom_history.append({
                    'track_id': decision.track_id,
                    'timestamp': time.time(),
                    'zoom_chain': decision.zoom_chain,
                    'extreme_zoom': len(decision.zoom_chain) > 1
                })
                
                self.stats['zoom_operations'] += 1
                if len(decision.zoom_chain) > 1:
                    self.stats['extreme_zoom_operations'] += 1
                    
            elif decision.action == 'send_alert':
                # Send safety alert
                action_result = ptz_callback({
                    'action': 'alert',
                    'track_id': decision.track_id,
                    'confidence': decision.confidence,
                    'position': decision.predicted_position,
                    'reasoning': decision.reasoning
                })
                executed_actions.append(action_result)
                
                # Record alert
                self.alert_history[decision.track_id] = time.time()
                self.location_cache[decision.predicted_position] = time.time()
                self.stats['alerts_sent'] += 1
        
        return executed_actions
    
    def _cleanup_old_data(self) -> None:
        """Clean up old cached data"""
        current_time = time.time()
        
        # Clean alert history
        expired_alerts = [
            track_id for track_id, timestamp in self.alert_history.items()
            if current_time - timestamp > self.config.clear_cache_trackid_seconds
        ]
        for track_id in expired_alerts:
            del self.alert_history[track_id]
        
        # Clean location cache
        expired_locations = [
            pos for pos, timestamp in self.location_cache.items()
            if current_time - timestamp > self.config.clear_cache_loc_seconds
        ]
        for pos in expired_locations:
            del self.location_cache[pos]
    
    def _is_chain_of_zoom_active(self, current_zoom: float) -> bool:
        """Check if Chain-of-Zoom is currently active"""
        return (self.config.enable_extreme_zoom and 
                current_zoom >= self.config.extreme_zoom_threshold)
    
    def get_integration_stats(self) -> Dict:
        """Get comprehensive integration statistics"""
        chain_stats = self.chain_tracker.get_performance_stats()
        
        return {
            **self.stats,
            **chain_stats,
            'active_streaks': len(self.streak_detections),
            'zoom_history_length': len(self.zoom_history),
            'alert_cache_size': len(self.alert_history),
            'location_cache_size': len(self.location_cache),
            'extreme_zoom_ratio': self.stats['extreme_zoom_operations'] / max(1, self.stats['zoom_operations']),
            'industrial_safety_focus': 'ENABLED - Life-saving applications'
        }

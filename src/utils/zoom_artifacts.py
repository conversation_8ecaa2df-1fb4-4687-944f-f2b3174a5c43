"""
Realistic PTZ Zoom Artifacts Simulation
Simulates the actual problems that occur during real PTZ camera zoom operations
"""

import cv2
import numpy as np
from scipy import ndimage
from scipy.ndimage import gaussian_filter
import random

class PTZZoomArtifacts:
    """Simulates realistic artifacts that occur during PTZ zoom operations"""
    
    def __init__(self):
        self.zoom_history = []
        self.frame_count = 0
        
    def apply_zoom_artifacts(self, frame, zoom_level, zoom_speed=0.1):
        """
        Apply realistic zoom artifacts to frame
        
        Args:
            frame: Input frame
            zoom_level: Current zoom level (1.0 = no zoom)
            zoom_speed: Speed of zoom change (affects motion blur)
        """
        self.frame_count += 1
        self.zoom_history.append(zoom_level)
        
        # Keep only recent history
        if len(self.zoom_history) > 10:
            self.zoom_history.pop(0)
        
        # Calculate zoom change rate
        zoom_change = 0
        if len(self.zoom_history) > 1:
            zoom_change = abs(self.zoom_history[-1] - self.zoom_history[-2])
        
        # Apply artifacts based on zoom level and change rate
        artifact_frame = frame.copy()
        
        # 1. Digital zoom artifacts (pixelation, interpolation artifacts)
        if zoom_level > 1.5:
            artifact_frame = self._apply_digital_zoom_artifacts(artifact_frame, zoom_level)
        
        # 2. Motion blur during zoom changes
        if zoom_change > 0.05:
            artifact_frame = self._apply_zoom_motion_blur(artifact_frame, zoom_change)
        
        # 3. Focus hunting (autofocus issues during zoom)
        if zoom_change > 0.1:
            artifact_frame = self._apply_focus_hunting(artifact_frame, zoom_change)
        
        # 4. Compression artifacts (more visible at high zoom)
        if zoom_level > 2.0:
            artifact_frame = self._apply_compression_artifacts(artifact_frame, zoom_level)
        
        # 5. Noise increase (sensor noise more visible at high zoom)
        if zoom_level > 1.2:
            artifact_frame = self._apply_zoom_noise(artifact_frame, zoom_level)
        
        # 6. Geometric distortion (disabled for performance - too slow for real-time demo)
        # if zoom_level > 2.5:
        #     artifact_frame = self._apply_geometric_distortion(artifact_frame, zoom_level)
        
        # 7. Temporal instability (slight frame-to-frame variations)
        artifact_frame = self._apply_temporal_instability(artifact_frame, zoom_level)
        
        return artifact_frame
    
    def _apply_digital_zoom_artifacts(self, frame, zoom_level):
        """Simulate digital zoom pixelation and interpolation artifacts"""
        h, w = frame.shape[:2]
        
        # Simulate digital zoom by downsampling then upsampling
        downsample_factor = max(1.0, zoom_level - 1.0)
        new_h = max(10, int(h / downsample_factor))
        new_w = max(10, int(w / downsample_factor))
        
        # Downsample
        small = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_AREA)
        
        # Add slight blur to simulate sensor limitations
        small = cv2.GaussianBlur(small, (3, 3), 0.5)
        
        # Upsample with different interpolation to create artifacts
        if zoom_level > 3.0:
            # Use nearest neighbor for more pixelation
            upsampled = cv2.resize(small, (w, h), interpolation=cv2.INTER_NEAREST)
        else:
            # Use linear interpolation
            upsampled = cv2.resize(small, (w, h), interpolation=cv2.INTER_LINEAR)
        
        return upsampled
    
    def _apply_zoom_motion_blur(self, frame, zoom_change):
        """Apply motion blur that occurs during zoom operations"""
        # Create radial blur kernel
        blur_strength = min(15, int(zoom_change * 100))
        if blur_strength < 2:
            return frame
        
        # Create radial motion blur
        h, w = frame.shape[:2]
        center_x, center_y = w // 2, h // 2
        
        # Create blur kernel
        kernel_size = blur_strength
        kernel = np.zeros((kernel_size, kernel_size))
        
        # Radial blur pattern
        for i in range(kernel_size):
            for j in range(kernel_size):
                dist = np.sqrt((i - kernel_size//2)**2 + (j - kernel_size//2)**2)
                if dist <= kernel_size//2:
                    kernel[i, j] = 1.0 / (1.0 + dist)
        
        kernel = kernel / np.sum(kernel)
        
        # Apply blur
        blurred = cv2.filter2D(frame, -1, kernel)
        
        # Blend with original based on blur strength
        alpha = min(0.7, zoom_change * 5)
        return cv2.addWeighted(frame, 1-alpha, blurred, alpha, 0)
    
    def _apply_focus_hunting(self, frame, zoom_change):
        """Simulate autofocus hunting during zoom changes"""
        # Random focus variations
        focus_offset = random.uniform(-2, 2) * zoom_change * 10
        
        if abs(focus_offset) > 0.5:
            # Apply slight blur to simulate focus hunting
            blur_amount = abs(focus_offset)
            kernel_size = max(3, int(blur_amount))
            if kernel_size % 2 == 0:
                kernel_size += 1
            
            blurred = cv2.GaussianBlur(frame, (kernel_size, kernel_size), blur_amount/2)
            
            # Blend with original
            alpha = min(0.3, abs(focus_offset) / 5)
            return cv2.addWeighted(frame, 1-alpha, blurred, alpha, 0)
        
        return frame
    
    def _apply_compression_artifacts(self, frame, zoom_level):
        """Simulate compression artifacts more visible at high zoom"""
        # JPEG compression artifacts
        compression_quality = max(30, int(100 - (zoom_level - 1) * 20))
        
        # Encode and decode to introduce compression artifacts
        encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), compression_quality]
        _, encimg = cv2.imencode('.jpg', frame, encode_param)
        compressed = cv2.imdecode(encimg, 1)
        
        # Blend with original
        alpha = min(0.4, (zoom_level - 2.0) / 3.0)
        return cv2.addWeighted(frame, 1-alpha, compressed, alpha, 0)
    
    def _apply_zoom_noise(self, frame, zoom_level):
        """Add noise that becomes more visible at high zoom levels"""
        noise_strength = (zoom_level - 1.0) * 10
        
        # Generate noise
        noise = np.random.normal(0, noise_strength, frame.shape).astype(np.uint8)
        
        # Add noise
        noisy = cv2.add(frame, noise)
        
        # Blend with original
        alpha = min(0.2, (zoom_level - 1.0) / 4.0)
        return cv2.addWeighted(frame, 1-alpha, noisy, alpha, 0)
    
    def _apply_geometric_distortion(self, frame, zoom_level):
        """Apply slight geometric distortion visible at high zoom"""
        h, w = frame.shape[:2]
        
        # Create slight barrel/pincushion distortion
        distortion_strength = (zoom_level - 2.5) * 0.1
        
        # Create distortion map
        map_x = np.zeros((h, w), dtype=np.float32)
        map_y = np.zeros((h, w), dtype=np.float32)
        
        center_x, center_y = w // 2, h // 2
        
        for y in range(h):
            for x in range(w):
                # Distance from center
                dx = x - center_x
                dy = y - center_y
                r = np.sqrt(dx*dx + dy*dy)
                
                # Distortion factor
                if r > 0:
                    r_norm = r / max(center_x, center_y)
                    distortion = 1 + distortion_strength * r_norm * r_norm
                    
                    map_x[y, x] = center_x + dx * distortion
                    map_y[y, x] = center_y + dy * distortion
                else:
                    map_x[y, x] = x
                    map_y[y, x] = y
        
        # Apply distortion
        distorted = cv2.remap(frame, map_x, map_y, cv2.INTER_LINEAR)
        
        # Blend with original
        alpha = min(0.3, distortion_strength * 2)
        return cv2.addWeighted(frame, 1-alpha, distorted, alpha, 0)
    
    def _apply_temporal_instability(self, frame, zoom_level):
        """Add slight temporal variations that occur in real cameras"""
        # Random brightness/contrast variations
        if random.random() < 0.3:  # 30% chance per frame
            brightness_var = random.uniform(-5, 5) * (zoom_level / 3.0)
            contrast_var = random.uniform(0.95, 1.05)
            
            # Apply variations
            adjusted = cv2.convertScaleAbs(frame, alpha=contrast_var, beta=brightness_var)
            
            # Blend with original
            alpha = 0.1
            return cv2.addWeighted(frame, 1-alpha, adjusted, alpha, 0)
        
        return frame

class ZoomScheduleGenerator:
    """Generate realistic zoom schedules for testing"""
    
    @staticmethod
    def create_security_patrol_zoom(duration_frames):
        """Simulate security guard zooming in on suspicious activity"""
        schedule = []
        
        # Start at normal view
        for i in range(duration_frames // 4):
            schedule.append(1.0)
        
        # Quick zoom in (suspicious activity detected)
        zoom_in_frames = duration_frames // 8
        for i in range(zoom_in_frames):
            zoom = 1.0 + (i / zoom_in_frames) * 3.0  # Zoom to 4x
            schedule.append(zoom)
        
        # Hold at high zoom (investigating)
        for i in range(duration_frames // 4):
            schedule.append(4.0)
        
        # Gradual zoom out (activity cleared)
        zoom_out_frames = duration_frames // 4
        for i in range(zoom_out_frames):
            zoom = 4.0 - (i / zoom_out_frames) * 3.0
            schedule.append(zoom)
        
        # Fill remaining frames
        remaining = duration_frames - len(schedule)
        for i in range(remaining):
            schedule.append(1.0)
        
        return schedule[:duration_frames]
    
    @staticmethod
    def create_tracking_zoom(duration_frames):
        """Simulate operator tracking a moving subject"""
        schedule = []
        
        for i in range(duration_frames):
            # Sinusoidal zoom pattern with some randomness
            base_zoom = 1.5 + 1.5 * np.sin(i * 0.02)  # 1.0 to 3.0
            noise = random.uniform(-0.2, 0.2)
            zoom = max(1.0, min(4.0, base_zoom + noise))
            schedule.append(zoom)
        
        return schedule
    
    @staticmethod
    def create_rapid_zoom_test(duration_frames):
        """Create challenging rapid zoom changes"""
        schedule = []
        
        for i in range(duration_frames):
            if i % 30 < 10:  # Zoom in for 10 frames
                zoom = 1.0 + (i % 10) * 0.3
            elif i % 30 < 20:  # Hold zoom
                zoom = 4.0
            else:  # Zoom out
                zoom = 4.0 - ((i % 30) - 20) * 0.3
            
            schedule.append(max(1.0, zoom))
        
        return schedule

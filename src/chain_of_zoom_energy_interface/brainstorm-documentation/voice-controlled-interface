"""
🎤 Voice-Controlled Chain-of-Zoom Interface
=============================================

Integrates OpenAI Whisper for natural language control of your 
Chain-of-Zoom safety tracking system.

Commands supported:
- "enhance track 5" -> Zoom and focus on specific worker
- "zoom in" / "zoom out" -> Manual zoom control  
- "detect helmets" -> Safety equipment detection mode
- "reset view" -> Return to normal view
- "follow worker in red" -> Auto-track by description
- "emergency zoom all" -> Multi-track emergency view
"""

import whisper
import pyaudio
import wave
import threading
import queue
import time
import numpy as np
import cv2
from typing import Optional, Callable, Dict, Any
import re
from dataclasses import dataclass

@dataclass
class VoiceCommand:
    """Structured voice command"""
    action: str
    target: Optional[str] = None
    value: Optional[float] = None
    confidence: float = 0.0

class VoiceControlledZoomInterface:
    """
    🎤 Voice interface for Chain-of-Zoom tracker
    
    Integrates Whisper ASR with your existing tracking system
    to enable natural language control during safety monitoring.
    """
    
    def __init__(self, chain_tracker, whisper_model: str = "base"):
        """
        Initialize voice interface
        
        Args:
            chain_tracker: Your ChainOfZoomTracker instance
            whisper_model: Whisper model size (tiny, base, small, medium, large)
        """
        self.chain_tracker = chain_tracker
        
        # Initialize Whisper
        print(f"🎤 Loading Whisper model: {whisper_model}")
        self.whisper_model = whisper.load_model(whisper_model)
        
        # Audio configuration
        self.audio_config = {
            'format': pyaudio.paInt16,
            'channels': 1,
            'rate': 16000,
            'chunk': 1024,
            'record_seconds': 3  # Command buffer length
        }
        
        # Voice command patterns
        self.command_patterns = {
            'zoom_in': [r'zoom in', r'enhance', r'closer'],
            'zoom_out': [r'zoom out', r'wider', r'back'],
            'track_id': [r'track (\d+)', r'follow (\d+)', r'focus (\d+)'],
            'track_description': [r'follow.*?(worker|person).*?(red|blue|yellow|helmet|vest)',
                                r'track.*?(worker|person).*?(red|blue|yellow|helmet|vest)'],
            'safety_detect': [r'detect helmets?', r'safety check', r'equipment check'],
            'reset': [r'reset', r'normal view', r'default'],
            'emergency': [r'emergency', r'alert', r'all workers'],
            'auto_zoom': [r'auto zoom on', r'smart zoom', r'intelligent tracking'],
            'manual_zoom': [r'manual zoom', r'zoom off', r'stop auto']
        }
        
        # Current state
        self.is_listening = False
        self.voice_thread = None
        self.command_queue = queue.Queue()
        self.current_auto_target = None
        
        # Safety detection integration point
        self.safety_detector = None  # Will integrate with your helmet detection
        
    def start_listening(self):
        """Start voice command listening in background thread"""
        if self.is_listening:
            return
            
        self.is_listening = True
        self.voice_thread = threading.Thread(target=self._voice_listener_loop)
        self.voice_thread.daemon = True
        self.voice_thread.start()
        print("🎤 Voice interface started - say commands now!")
    
    def stop_listening(self):
        """Stop voice command listening"""
        self.is_listening = False
        if self.voice_thread:
            self.voice_thread.join()
        print("🔇 Voice interface stopped")
    
    def _voice_listener_loop(self):
        """Main voice listening loop"""
        audio = pyaudio.PyAudio()
        
        try:
            while self.is_listening:
                # Record audio snippet
                audio_data = self._record_audio_snippet(audio)
                
                if audio_data is not None:
                    # Transcribe with Whisper
                    command_text = self._transcribe_audio(audio_data)
                    
                    if command_text:
                        # Parse and execute command
                        command = self._parse_voice_command(command_text)
                        if command:
                            self.command_queue.put(command)
                            print(f"🎤 Voice command: '{command_text}' -> {command.action}")
                        
                time.sleep(0.1)  # Prevent CPU overload
                
        except Exception as e:
            print(f"❌ Voice listener error: {e}")
        finally:
            audio.terminate()
    
    def _record_audio_snippet(self, audio) -> Optional[np.ndarray]:
        """Record short audio snippet for command detection"""
        try:
            stream = audio.open(
                format=self.audio_config['format'],
                channels=self.audio_config['channels'],
                rate=self.audio_config['rate'],
                input=True,
                frames_per_buffer=self.audio_config['chunk']
            )
            
            frames = []
            for _ in range(0, int(self.audio_config['rate'] / self.audio_config['chunk'] * 
                                self.audio_config['record_seconds'])):
                data = stream.read(self.audio_config['chunk'])
                frames.append(data)
            
            stream.stop_stream()
            stream.close()
            
            # Convert to numpy array
            audio_data = np.frombuffer(b''.join(frames), dtype=np.int16)
            audio_data = audio_data.astype(np.float32) / 32768.0  # Normalize to [-1, 1]
            
            # Basic voice activity detection (simple energy threshold)
            if np.mean(np.abs(audio_data)) > 0.01:  # Adjust threshold as needed
                return audio_data
            
            return None
            
        except Exception as e:
            print(f"❌ Audio recording error: {e}")
            return None
    
    def _transcribe_audio(self, audio_data: np.ndarray) -> Optional[str]:
        """Transcribe audio using Whisper"""
        try:
            # Whisper expects 16kHz audio
            result = self.whisper_model.transcribe(audio_data)
            text = result['text'].strip().lower()
            
            # Filter out very short or nonsensical transcriptions
            if len(text) > 3 and not text.startswith('['):
                return text
            
            return None
            
        except Exception as e:
            print(f"❌ Whisper transcription error: {e}")
            return None
    
    def _parse_voice_command(self, text: str) -> Optional[VoiceCommand]:
        """Parse transcribed text into structured command"""
        text = text.lower().strip()
        
        # Check each command pattern
        for command_type, patterns in self.command_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    return self._create_command(command_type, match, text)
        
        return None
    
    def _create_command(self, command_type: str, match, text: str) -> VoiceCommand:
        """Create structured command from pattern match"""
        if command_type == 'track_id':
            track_id = int(match.group(1))
            return VoiceCommand('track_id', target=str(track_id), confidence=0.9)
            
        elif command_type == 'track_description':
            description = match.group(0)
            return VoiceCommand('track_description', target=description, confidence=0.8)
            
        elif command_type == 'zoom_in':
            return VoiceCommand('zoom_in', value=1.5, confidence=0.9)
            
        elif command_type == 'zoom_out':
            return VoiceCommand('zoom_out', value=0.7, confidence=0.9)
            
        elif command_type == 'safety_detect':
            return VoiceCommand('safety_detect', confidence=0.9)
            
        elif command_type == 'reset':
            return VoiceCommand('reset', confidence=0.9)
            
        elif command_type == 'emergency':
            return VoiceCommand('emergency', confidence=0.9)
            
        elif command_type == 'auto_zoom':
            return VoiceCommand('auto_zoom', target='on', confidence=0.8)
            
        elif command_type == 'manual_zoom':
            return VoiceCommand('auto_zoom', target='off', confidence=0.8)
        
        return VoiceCommand(command_type, confidence=0.7)
    
    def process_voice_commands(self, frame: np.ndarray, tracks: list, 
                             current_zoom: float) -> Dict[str, Any]:
        """
        Process pending voice commands and return control actions
        
        Args:
            frame: Current video frame
            tracks: Current tracks from your tracker
            current_zoom: Current zoom level
            
        Returns:
            Dict with zoom/tracking control actions
        """
        actions = {}
        
        # Process all pending commands
        while not self.command_queue.empty():
            try:
                command = self.command_queue.get_nowait()
                action = self._execute_voice_command(command, frame, tracks, current_zoom)
                if action:
                    actions.update(action)
            except queue.Empty:
                break
        
        return actions
    
    def _execute_voice_command(self, command: VoiceCommand, frame: np.ndarray, 
                             tracks: list, current_zoom: float) -> Optional[Dict]:
        """Execute a specific voice command"""
        
        if command.action == 'track_id':
            # Focus on specific track ID
            track_id = int(command.target)
            target_track = self._find_track_by_id(tracks, track_id)
            if target_track:
                bbox = self._extract_bbox(target_track)
                return {
                    'zoom_to_bbox': bbox,
                    'target_zoom': current_zoom * 2.0,
                    'track_id': track_id
                }
        
        elif command.action == 'track_description':
            # This would integrate with your safety detection
            # For now, focus on first detected person
            if tracks:
                bbox = self._extract_bbox(tracks[0])
                return {
                    'zoom_to_bbox': bbox,
                    'target_zoom': current_zoom * 1.5
                }
        
        elif command.action == 'zoom_in':
            return {
                'target_zoom': current_zoom * command.value,
                'smooth_transition': True
            }
        
        elif command.action == 'zoom_out':
            return {
                'target_zoom': current_zoom * command.value,
                'smooth_transition': True
            }
        
        elif command.action == 'safety_detect':
            # Trigger safety equipment detection
            return {
                'enable_safety_detection': True,
                'detection_mode': 'helmet_and_vest'
            }
        
        elif command.action == 'reset':
            return {
                'target_zoom': 1.0,
                'reset_tracking': True,
                'smooth_transition': True
            }
        
        elif command.action == 'emergency':
            # Emergency multi-track view
            return {
                'emergency_mode': True,
                'target_zoom': 0.8,  # Zoom out to see all workers
                'highlight_all_tracks': True
            }
        
        elif command.action == 'auto_zoom':
            if command.target == 'on':
                return {'auto_zoom_enabled': True}
            else:
                return {'auto_zoom_enabled': False}
        
        return None
    
    def _find_track_by_id(self, tracks: list, track_id: int):
        """Find track by ID"""
        for track in tracks:
            if getattr(track, 'track_id', None) == track_id:
                return track
        return None
    
    def _extract_bbox(self, track) -> list:
        """Extract bounding box from track (reuse your existing logic)"""
        if hasattr(track, 'to_ltrb'):
            return list(map(int, track.to_ltrb()))
        elif hasattr(track, 'tlbr'):
            return list(map(int, track.tlbr))
        else:
            return [100, 100, 200, 200]  # Default bbox


# Integration example with your Chain-of-Zoom system
class VoiceEnabledChainTracker:
    """
    🎤 Voice-enabled wrapper for your Chain-of-Zoom tracker
    """
    
    def __init__(self, base_tracker, whisper_model: str = "base"):
        # Initialize your existing tracker
        from chain_of_zoom_tracker import ChainOfZoomTracker
        self.chain_tracker = ChainOfZoomTracker(base_tracker)
        
        # Add voice interface
        self.voice_interface = VoiceControlledZoomInterface(
            self.chain_tracker, whisper_model
        )
        
        # State
        self.auto_zoom_enabled = False
        self.emergency_mode = False
        
    def start_voice_control(self):
        """Start voice control system"""
        self.voice_interface.start_listening()
        print("🎤 Voice control active!")
        print("Try saying: 'track 1', 'zoom in', 'reset', 'detect helmets'")
    
    def update(self, frame: np.ndarray, detections: list, 
               zoom_level: float) -> tuple:
        """
        Update with voice control integration
        
        Returns:
            (tracks, control_actions) - tracks and any voice-triggered actions
        """
        # Get tracks from your Chain-of-Zoom system
        tracks = self.chain_tracker.update(frame, detections, zoom_level)
        
        # Process voice commands
        voice_actions = self.voice_interface.process_voice_commands(
            frame, tracks, zoom_level
        )
        
        # Apply voice-triggered zoom changes
        new_zoom = zoom_level
        if 'target_zoom' in voice_actions:
            new_zoom = voice_actions['target_zoom']
            print(f"🎤 Voice zoom: {zoom_level:.2f} -> {new_zoom:.2f}")
        
        return tracks, {
            'new_zoom_level': new_zoom,
            'voice_actions': voice_actions,
            'emergency_mode': voice_actions.get('emergency_mode', False)
        }
    
    def stop_voice_control(self):
        """Stop voice control"""
        self.voice_interface.stop_listening()


# Usage example
if __name__ == "__main__":
    # This is how you'd integrate with your existing system
    
    # Your existing base tracker (DeepSORT, ByteTrack, etc.)
    # base_tracker = YourBaseTracker()
    
    # Create voice-enabled Chain-of-Zoom tracker
    # voice_tracker = VoiceEnabledChainTracker(base_tracker, "base")
    
    # Start voice control
    # voice_tracker.start_voice_control()
    
    # In your main loop:
    # tracks, actions = voice_tracker.update(frame, detections, current_zoom)
    
    # Apply actions to your video pipeline
    # if actions['new_zoom_level'] != current_zoom:
    #     # Update zoom in your video system
    #     pass
    
    print("🎤 Voice interface ready for integration!")
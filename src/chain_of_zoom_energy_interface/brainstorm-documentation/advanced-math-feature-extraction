"""
🔬 Advanced Mathematical Feature Extraction for Safety Monitoring
================================================================

This extends your Laplacian energy approach with advanced mathematical 
feature extraction that can detect:

1. 🏗️ Worker posture and pose estimation
2. 🦺 Safety equipment detection (helmets, vests, harnesses)
3. 📐 Spatial relationships and depth estimation
4. 🚶 Movement patterns and behavior analysis
5. ⚠️ Anomaly detection (falls, unsafe positions)

Mathematical Extensions:
- Gaussian Scale-Space Analysis
- Directional Energy Decomposition  
- Texture Anisotropy Measures
- Spatial Frequency Analysis
- Morphological Feature Extraction
"""

import numpy as np
import cv2
import torch
import torch.nn.functional as F
from scipy import ndimage, signal
from scipy.spatial.distance import pdist, squareform
from skimage.feature import local_binary_pattern, graycomatrix, graycoprops
from skimage.filters import gabor
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import time

@dataclass
class AdvancedWorkerFeatures:
    """
    🔬 Comprehensive worker feature representation
    
    This extends your basic Laplacian energy with advanced mathematical
    features for safety monitoring and behavioral analysis.
    """
    # Basic identification
    track_id: int
    zoom_level: float
    bbox: List[int]
    
    # Your original Laplacian energy
    laplacian_energy: float
    
    # 📐 Geometric and structural features
    aspect_ratio: float           # Height/width for posture analysis
    compactness: float           # Perimeter²/area for shape analysis
    orientation_angle: float     # Primary body orientation
    symmetry_score: float        # Left-right symmetry measure
    
    # 🎨 Advanced texture features  
    texture_homogeneity: float   # GLCM homogeneity
    texture_contrast: float      # GLCM contrast
    texture_energy: float        # GLCM energy
    texture_entropy: float       # Shannon entropy of texture
    
    # 📊 Frequency domain features
    frequency_peak: float        # Dominant spatial frequency
    frequency_spread: float      # Frequency distribution width
    high_freq_energy: float      # High frequency content (fine details)
    low_freq_energy: float       # Low frequency content (overall shape)
    
    # 🧭 Directional analysis
    edge_orientation_hist: np.ndarray  # Histogram of edge orientations
    gradient_coherence: float    # How aligned are gradients
    anisotropy_index: float      # Directional uniformity measure
    
    # 🏗️ Morphological features
    solidity: float              # Area/convex hull area
    extent: float                # Area/bounding box area
    eccentricity: float          # Ellipse eccentricity
    perimeter_complexity: float  # Fractal dimension of boundary
    
    # 🔍 Multi-scale analysis
    scale_invariant_features: np.ndarray  # Features across scales
    coarse_features: np.ndarray  # Large scale structure
    fine_features: np.ndarray    # Small scale details
    
    # 🚶 Motion and temporal features (if available)
    motion_energy: float         # Movement intensity
    motion_direction: float      # Primary movement direction
    temporal_consistency: float  # Feature stability over time
    
    # 🦺 Safety-specific features
    helmet_likelihood: float     # Probability of helmet presence
    vest_likelihood: float       # Probability of safety vest
    harness_likelihood: float    # Probability of safety harness
    posture_risk_score: float    # Risk assessment based on posture
    
    # ⚠️ Anomaly detection features
    anomaly_score: float         # Overall anomaly measure
    fall_risk_indicator: float   # Specific fall risk assessment
    unusual_motion_flag: bool    # Abnormal movement detection
    
    # 📊 Confidence and quality metrics
    feature_quality: float       # Overall feature extraction quality
    extraction_time_ms: float    # Performance metric
    
    def to_safety_vector(self) -> np.ndarray:
        """Convert to safety-focused feature vector"""
        return np.array([
            self.helmet_likelihood,
            self.vest_likelihood, 
            self.harness_likelihood,
            self.posture_risk_score,
            self.anomaly_score,
            self.fall_risk_indicator,
            self.aspect_ratio,
            self.orientation_angle,
            self.motion_energy,
            self.temporal_consistency
        ])
    
    def to_full_vector(self) -> np.ndarray:
        """Convert to comprehensive feature vector for ML"""
        basic_features = np.array([
            self.laplacian_energy, self.aspect_ratio, self.compactness,
            self.orientation_angle, self.symmetry_score, self.texture_homogeneity,
            self.texture_contrast, self.texture_energy, self.texture_entropy,
            self.frequency_peak, self.frequency_spread, self.high_freq_energy,
            self.low_freq_energy, self.gradient_coherence, self.anisotropy_index,
            self.solidity, self.extent, self.eccentricity, self.perimeter_complexity,
            self.motion_energy, self.motion_direction, self.temporal_consistency,
            self.helmet_likelihood, self.vest_likelihood, self.harness_likelihood,
            self.posture_risk_score, self.anomaly_score, self.fall_risk_indicator
        ])
        
        # Combine with multi-scale features
        return np.concatenate([
            basic_features,
            self.edge_orientation_hist.flatten(),
            self.scale_invariant_features.flatten(),
            self.coarse_features.flatten(),
            self.fine_features.flatten()
        ])


class AdvancedMathematicalExtractor:
    """
    🔬 Advanced Mathematical Feature Extractor
    
    This class implements state-of-the-art mathematical feature extraction
    methods that extend your Laplacian energy approach for comprehensive
    worker safety monitoring.
    
    Mathematical Foundations:
    1. Scale-space theory for multi-resolution analysis
    2. Differential geometry for shape analysis  
    3. Fourier analysis for frequency domain features
    4. Information theory for entropy measures
    5. Morphological mathematics for structural analysis
    """
    
    def __init__(self, 
                 scales: List[float] = [1.0, 2.0, 4.0, 8.0],
                 orientations: int = 8,
                 safety_models_path: Optional[str] = None,
                 enable_gpu: bool = True):
        """
        Initialize advanced feature extractor
        
        Args:
            scales: Gaussian scales for multi-resolution analysis
            orientations: Number of orientations for directional analysis
            safety_models_path: Path to pre-trained safety detection models
            enable_gpu: Enable GPU acceleration if available
        """
        self.scales = scales
        self.orientations = orientations
        self.enable_gpu = enable_gpu and torch.cuda.is_available()
        
        # Pre-compute Gabor filters for texture analysis
        self._initialize_gabor_filters()
        
        # Pre-compute Gaussian kernels for scale-space analysis
        self._initialize_gaussian_kernels()
        
        # Safety detection models (would be trained on your specific data)
        self.safety_models = self._load_safety_models(safety_models_path)
        
        # Performance tracking
        self.stats = {
            'extractions': 0,
            'total_time': 0.0,
            'gpu_accelerated': 0
        }
        
        print(f"🔬 Advanced Mathematical Extractor initialized")
        print(f"   Scales: {scales}")
        print(f"   Orientations: {orientations}")
        print(f"   GPU enabled: {self.enable_gpu}")
    
    def extract_comprehensive_features(self, frame: np.ndarray, track: Any,
                                     zoom_level: float, 
                                     previous_features: Optional[AdvancedWorkerFeatures] = None
                                     ) -> AdvancedWorkerFeatures:
        """
        🎯 Extract comprehensive mathematical features for worker safety
        
        This is your main entry point that extends your Laplacian approach
        with advanced mathematical analysis.
        
        Args:
            frame: Current video frame
            track: Track object with bounding box
            zoom_level: Current zoom level
            previous_features: Previous frame features for temporal analysis
            
        Returns:
            AdvancedWorkerFeatures with all mathematical features
        """
        start_time = time.time()
        
        # Extract ROI
        roi, bbox = self._extract_roi(frame, track)
        if roi is None:
            return self._create_zero_features(track, zoom_level)
        
        # Convert to grayscale for mathematical analysis
        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY) if len(roi.shape) == 3 else roi
        
        # 1. Your original Laplacian energy (preserved!)
        laplacian_energy = self._compute_laplacian_energy(gray_roi)
        
        # 2. 📐 Geometric and structural analysis
        geometric_features = self._extract_geometric_features(gray_roi, bbox)
        
        # 3. 🎨 Advanced texture analysis
        texture_features = self._extract_texture_features(gray_roi)
        
        # 4. 📊 Frequency domain analysis
        frequency_features = self._extract_frequency_features(gray_roi)
        
        # 5. 🧭 Directional analysis
        directional_features = self._extract_directional_features(gray_roi)
        
        # 6. 🏗️ Morphological analysis
        morphological_features = self._extract_morphological_features(gray_roi)
        
        # 7. 🔍 Multi-scale analysis
        multiscale_features = self._extract_multiscale_features(gray_roi)
        
        # 8. 🚶 Motion analysis (if previous features available)
        motion_features = self._extract_motion_features(
            gray_roi, previous_features, zoom_level
        )
        
        # 9. 🦺 Safety-specific analysis
        safety_features = self._extract_safety_features(roi, gray_roi)
        
        # 10. ⚠️ Anomaly detection
        anomaly_features = self._extract_anomaly_features(
            gray_roi, geometric_features, motion_features
        )
        
        # Combine all features
        extraction_time = (time.time() - start_time) * 1000
        
        comprehensive_features = AdvancedWorkerFeatures(
            track_id=getattr(track, 'track_id', 0),
            zoom_level=zoom_level,
            bbox=bbox,
            laplacian_energy=laplacian_energy,
            
            # Geometric
            aspect_ratio=geometric_features['aspect_ratio'],
            compactness=geometric_features['compactness'],
            orientation_angle=geometric_features['orientation_angle'],
            symmetry_score=geometric_features['symmetry_score'],
            
            # Texture
            texture_homogeneity=texture_features['homogeneity'],
            texture_contrast=texture_features['contrast'],
            texture_energy=texture_features['energy'],
            texture_entropy=texture_features['entropy'],
            
            # Frequency
            frequency_peak=frequency_features['peak'],
            frequency_spread=frequency_features['spread'],
            high_freq_energy=frequency_features['high_energy'],
            low_freq_energy=frequency_features['low_energy'],
            
            # Directional
            edge_orientation_hist=directional_features['orientation_hist'],
            gradient_coherence=directional_features['coherence'],
            anisotropy_index=directional_features['anisotropy'],
            
            # Morphological
            solidity=morphological_features['solidity'],
            extent=morphological_features['extent'],
            eccentricity=morphological_features['eccentricity'],
            perimeter_complexity=morphological_features['complexity'],
            
            # Multi-scale
            scale_invariant_features=multiscale_features['invariant'],
            coarse_features=multiscale_features['coarse'],
            fine_features=multiscale_features['fine'],
            
            # Motion
            motion_energy=motion_features['energy'],
            motion_direction=motion_features['direction'],
            temporal_consistency=motion_features['consistency'],
            
            # Safety
            helmet_likelihood=safety_features['helmet'],
            vest_likelihood=safety_features['vest'],
            harness_likelihood=safety_features['harness'],
            posture_risk_score=safety_features['posture_risk'],
            
            # Anomaly
            anomaly_score=anomaly_features['score'],
            fall_risk_indicator=anomaly_features['fall_risk'],
            unusual_motion_flag=anomaly_features['unusual_motion'],
            
            # Quality
            feature_quality=self._assess_feature_quality(roi, bbox),
            extraction_time_ms=extraction_time
        )
        
        # Update statistics
        self._update_stats(extraction_time)
        
        return comprehensive_features
    
    def _compute_laplacian_energy(self, gray_roi: np.ndarray) -> float:
        """Your original Laplacian energy computation (preserved exactly!)"""
        try:
            laplacian = cv2.Laplacian(gray_roi, cv2.CV_64F, ksize=3)
            energy = np.sqrt(np.mean(laplacian ** 2))
            return float(energy)
        except:
            return 0.0
    
    def _extract_geometric_features(self, gray_roi: np.ndarray, 
                                  bbox: List[int]) -> Dict[str, float]:
        """
        📐 Extract geometric and structural features
        
        Mathematical basis: Differential geometry and shape analysis
        """
        height, width = gray_roi.shape
        
        # Aspect ratio (key for posture analysis)
        aspect_ratio = height / max(width, 1)
        
        # Compactness (perimeter² / area)
        contours, _ = cv2.findContours(
            cv2.threshold(gray_roi, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)[1],
            cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
        )
        
        if contours:
            largest_contour = max(contours, key=cv2.contourArea)
            area
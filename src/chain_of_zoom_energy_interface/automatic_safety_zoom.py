"""
🚨 Automatic Safety-Triggered Zoom System
=========================================

This system automatically triggers your Chain-of-Zoom when safety violations
or anomalies are detected, using the advanced mathematical features.

Key Features:
• Real-time helmet/vest detection using color and shape analysis
• Fall detection using posture and motion analysis  
• Crowd density monitoring and social distancing
• Automatic zoom-in on safety violations
• Configurable confidence thresholds
• Integration with existing emergency protocols

The system continuously monitors all workers and automatically zooms 
on the highest priority safety concern.

Built with love for worker safety! 💙
"""

import numpy as np
import cv2
import time
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
from collections import deque
import threading
import queue
from enum import Enum

class SafetyViolationType(Enum):
    """Types of safety violations that can trigger auto-zoom"""
    MISSING_HELMET = "missing_helmet"
    MISSING_VEST = "missing_vest"
    FALL_DETECTED = "fall_detected"
    UNSAFE_POSTURE = "unsafe_posture"
    CROWDING = "crowding"
    UNUSUAL_BEHAVIOR = "unusual_behavior"
    EQUIPMENT_VIOLATION = "equipment_violation"

@dataclass
class SafetyAlert:
    """Structured safety alert with all relevant information"""
    violation_type: SafetyViolationType
    track_id: int
    confidence: float
    urgency: float  # 0.0 to 1.0
    bbox: List[int]
    timestamp: float
    description: str
    recommended_zoom: float
    auto_zoom_enabled: bool = True

@dataclass
class SafetyMonitoringConfig:
    """Configuration for safety monitoring system"""
    # Detection thresholds
    helmet_threshold: float = 0.6
    vest_threshold: float = 0.5
    fall_risk_threshold: float = 0.7
    anomaly_threshold: float = 0.6
    
    # Auto-zoom parameters
    safety_zoom_factor: float = 2.5
    emergency_zoom_factor: float = 3.5
    crowd_zoom_factor: float = 1.8
    
    # Spatial thresholds
    min_safe_distance: float = 2.0  # meters
    max_crowd_density: float = 0.5  # people per square meter
    
    # Temporal parameters
    alert_persistence_seconds: float = 5.0
    alert_cooldown_seconds: float = 2.0
    
    # System behavior
    enable_auto_zoom: bool = True
    enable_audio_alerts: bool = False
    enable_emergency_protocol: bool = True
    log_all_detections: bool = True

class AutomaticSafetyZoomSystem:
    """
    🚨 Automatic Safety-Triggered Zoom System
    
    Continuously monitors all workers using advanced mathematical features
    and automatically triggers Chain-of-Zoom on safety violations.
    """
    
    def __init__(self, 
                 chain_tracker,
                 feature_extractor,
                 spatial_intelligence=None,
                 config: SafetyMonitoringConfig = None):
        """
        Initialize automatic safety zoom system
        
        Args:
            chain_tracker: Your ChainOfZoomTracker instance
            feature_extractor: AdvancedMathematicalExtractor instance
            spatial_intelligence: SpatialIntelligence instance (optional)
            config: Safety monitoring configuration
        """
        self.chain_tracker = chain_tracker
        self.feature_extractor = feature_extractor
        self.spatial_intelligence = spatial_intelligence
        self.config = config or SafetyMonitoringConfig()
        
        # Alert management
        self.active_alerts = {}  # track_id -> SafetyAlert
        self.alert_history = deque(maxlen=100)
        self.last_zoom_time = 0
        
        # Safety state tracking
        self.worker_safety_profiles = {}  # track_id -> safety profile
        self.global_safety_state = {
            'total_workers': 0,
            'violations_count': 0,
            'highest_risk_worker': None,
            'crowd_risk_level': 'low'
        }
        
        # Performance tracking
        self.detection_stats = {
            'total_detections': 0,
            'helmet_violations': 0,
            'vest_violations': 0,
            'fall_detections': 0,
            'auto_zooms_triggered': 0,
            'false_positives': 0
        }
        
        # Emergency protocols
        self.emergency_contacts = []
        self.emergency_active = False
        
        print("🚨 Automatic Safety Zoom System initialized")
        print(f"   Helmet threshold: {self.config.helmet_threshold}")
        print(f"   Vest threshold: {self.config.vest_threshold}")
        print(f"   Auto-zoom enabled: {self.config.enable_auto_zoom}")
    
    def process_frame(self, frame: np.ndarray, detections: List[Any], 
                     zoom_level: float) -> Dict[str, Any]:
        """
        🎯 Main processing function - analyzes frame for safety violations
        
        Returns:
            Complete analysis with auto-zoom decisions and safety alerts
        """
        # 1. Get tracks from Chain-of-Zoom
        tracks = self.chain_tracker.update(frame, detections, zoom_level)
        
        # 2. Extract advanced features for each worker
        current_features = []
        for track in tracks:
            features = self.feature_extractor.extract_comprehensive_features(
                frame, track, zoom_level, self._get_previous_features(track)
            )
            current_features.append(features)
            self._update_worker_profile(features)
        
        # 3. Analyze spatial relationships (if available)
        spatial_analysis = {}
        if self.spatial_intelligence:
            spatial_analysis = self.spatial_intelligence.analyze_spatial_relationships(
                current_features, frame.shape[:2], zoom_level
            )
        
        # 4. Detect safety violations
        safety_alerts = self._detect_safety_violations(
            current_features, spatial_analysis, frame.shape[:2]
        )
        
        # 5. Update global safety state
        self._update_global_safety_state(current_features, spatial_analysis)
        
        # 6. Generate auto-zoom decision
        auto_zoom_decision = self._generate_auto_zoom_decision(
            safety_alerts, zoom_level, spatial_analysis
        )
        
        # 7. Handle emergency protocols if needed
        emergency_status = self._check_emergency_conditions(safety_alerts)
        
        # 8. Update statistics
        self._update_detection_stats(safety_alerts)
        
        return {
            'tracks': tracks,
            'features': current_features,
            'safety_alerts': safety_alerts,
            'spatial_analysis': spatial_analysis,
            'auto_zoom_decision': auto_zoom_decision,
            'global_safety_state': self.global_safety_state.copy(),
            'emergency_status': emergency_status,
            'system_stats': self._get_system_stats()
        }
    
    def _detect_safety_violations(self, features: List, spatial_analysis: Dict,
                                frame_shape: Tuple[int, int]) -> List[SafetyAlert]:
        """
        🔍 Comprehensive safety violation detection
        
        Analyzes all extracted features to identify safety concerns
        """
        alerts = []
        current_time = time.time()
        
        for feature in features:
            track_id = feature.track_id
            
            # 1. Helmet detection
            if feature.helmet_likelihood < self.config.helmet_threshold:
                alert = SafetyAlert(
                    violation_type=SafetyViolationType.MISSING_HELMET,
                    track_id=track_id,
                    confidence=1.0 - feature.helmet_likelihood,
                    urgency=0.8,
                    bbox=feature.bbox,
                    timestamp=current_time,
                    description=f"Worker {track_id} missing helmet (confidence: {feature.helmet_likelihood:.2f})",
                    recommended_zoom=self.config.safety_zoom_factor
                )
                alerts.append(alert)
            
            # 2. Safety vest detection
            if feature.vest_likelihood < self.config.vest_threshold:
                alert = SafetyAlert(
                    violation_type=SafetyViolationType.MISSING_VEST,
                    track_id=track_id,
                    confidence=1.0 - feature.vest_likelihood,
                    urgency=0.6,
                    bbox=feature.bbox,
                    timestamp=current_time,
                    description=f"Worker {track_id} missing safety vest (confidence: {feature.vest_likelihood:.2f})",
                    recommended_zoom=self.config.safety_zoom_factor
                )
                alerts.append(alert)
            
            # 3. Fall detection
            if feature.fall_risk_indicator > self.config.fall_risk_threshold:
                alert = SafetyAlert(
                    violation_type=SafetyViolationType.FALL_DETECTED,
                    track_id=track_id,
                    confidence=feature.fall_risk_indicator,
                    urgency=0.9,
                    bbox=feature.bbox,
                    timestamp=current_time,
                    description=f"Fall risk detected for worker {track_id} (score: {feature.fall_risk_indicator:.2f})",
                    recommended_zoom=self.config.emergency_zoom_factor
                )
                alerts.append(alert)
            
            # 4. Unsafe posture detection
            if feature.posture_risk_score > 0.7:
                alert = SafetyAlert(
                    violation_type=SafetyViolationType.UNSAFE_POSTURE,
                    track_id=track_id,
                    confidence=feature.posture_risk_score,
                    urgency=0.5,
                    bbox=feature.bbox,
                    timestamp=current_time,
                    description=f"Unsafe posture detected for worker {track_id} (score: {feature.posture_risk_score:.2f})",
                    recommended_zoom=self.config.safety_zoom_factor
                )
                alerts.append(alert)
            
            # 5. Anomaly detection
            if feature.anomaly_score > self.config.anomaly_threshold:
                alert = SafetyAlert(
                    violation_type=SafetyViolationType.UNUSUAL_BEHAVIOR,
                    track_id=track_id,
                    confidence=feature.anomaly_score,
                    urgency=0.7,
                    bbox=feature.bbox,
                    timestamp=current_time,
                    description=f"Unusual behavior detected for worker {track_id} (score: {feature.anomaly_score:.2f})",
                    recommended_zoom=self.config.safety_zoom_factor
                )
                alerts.append(alert)
        
        return alerts

    def _generate_auto_zoom_decision(self, safety_alerts: List[SafetyAlert],
                                   current_zoom: float,
                                   spatial_analysis: Dict) -> Optional[Dict]:
        """
        🎯 Generate automatic zoom decision based on safety alerts

        Prioritizes alerts by urgency and determines optimal zoom action
        """
        if not self.config.enable_auto_zoom or not safety_alerts:
            return None

        current_time = time.time()

        # Check cooldown period
        if current_time - self.last_zoom_time < self.config.alert_cooldown_seconds:
            return None

        # Sort alerts by urgency (highest first)
        sorted_alerts = sorted(safety_alerts, key=lambda x: x.urgency, reverse=True)
        highest_priority_alert = sorted_alerts[0]

        # Only zoom if urgency is significant
        if highest_priority_alert.urgency < 0.5:
            return None

        # Calculate target zoom level
        target_zoom = current_zoom * highest_priority_alert.recommended_zoom
        target_zoom = min(target_zoom, 8.0)  # Max zoom limit

        # Calculate zoom center from bounding box
        bbox = highest_priority_alert.bbox
        center_x = (bbox[0] + bbox[2]) // 2
        center_y = (bbox[1] + bbox[3]) // 2

        self.last_zoom_time = current_time

        return {
            'should_zoom': True,
            'target_zoom': target_zoom,
            'zoom_center': (center_x, center_y),
            'focus_bbox': bbox,
            'alert': highest_priority_alert,
            'reason': highest_priority_alert.description,
            'urgency': highest_priority_alert.urgency
        }

    def _get_previous_features(self, track):
        """Get previous features for temporal analysis"""
        track_id = getattr(track, 'track_id', 0)
        return self.worker_safety_profiles.get(track_id, {}).get('last_features', None)

    def _update_worker_profile(self, features):
        """Update worker safety profile with new features"""
        track_id = features.track_id

        if track_id not in self.worker_safety_profiles:
            self.worker_safety_profiles[track_id] = {
                'first_seen': time.time(),
                'violation_count': 0,
                'last_features': None,
                'safety_score_history': deque(maxlen=10)
            }

        profile = self.worker_safety_profiles[track_id]
        profile['last_features'] = features

        # Calculate overall safety score
        safety_score = (features.helmet_likelihood + features.vest_likelihood) / 2.0
        profile['safety_score_history'].append(safety_score)

        # Count violations
        if features.helmet_likelihood < self.config.helmet_threshold:
            profile['violation_count'] += 1
        if features.vest_likelihood < self.config.vest_threshold:
            profile['violation_count'] += 1

    def _update_global_safety_state(self, features: List, spatial_analysis: Dict):
        """Update global safety monitoring state"""
        self.global_safety_state['total_workers'] = len(features)

        # Count violations
        violations = 0
        highest_risk_worker = None
        highest_risk_score = 0.0

        for feature in features:
            worker_risk = 0.0

            if feature.helmet_likelihood < self.config.helmet_threshold:
                violations += 1
                worker_risk += 0.4

            if feature.vest_likelihood < self.config.vest_threshold:
                violations += 1
                worker_risk += 0.3

            if feature.fall_risk_indicator > self.config.fall_risk_threshold:
                violations += 1
                worker_risk += 0.5

            if worker_risk > highest_risk_score:
                highest_risk_score = worker_risk
                highest_risk_worker = feature.track_id

        self.global_safety_state['violations_count'] = violations
        self.global_safety_state['highest_risk_worker'] = highest_risk_worker

        # Determine crowd risk level
        if len(features) > 10:
            self.global_safety_state['crowd_risk_level'] = 'high'
        elif len(features) > 5:
            self.global_safety_state['crowd_risk_level'] = 'medium'
        else:
            self.global_safety_state['crowd_risk_level'] = 'low'

    def _check_emergency_conditions(self, safety_alerts: List[SafetyAlert]) -> Dict:
        """Check for emergency conditions requiring immediate response"""
        emergency_alerts = [alert for alert in safety_alerts if alert.urgency > 0.8]

        emergency_status = {
            'emergency_active': len(emergency_alerts) > 0,
            'emergency_count': len(emergency_alerts),
            'highest_urgency': max([alert.urgency for alert in emergency_alerts]) if emergency_alerts else 0.0,
            'emergency_types': [alert.violation_type.value for alert in emergency_alerts]
        }

        # Activate emergency protocols if needed
        if emergency_status['emergency_active'] and self.config.enable_emergency_protocol:
            self._activate_emergency_protocol(emergency_alerts)

        return emergency_status

    def _activate_emergency_protocol(self, emergency_alerts: List[SafetyAlert]):
        """Activate emergency response protocols"""
        if not self.emergency_active:
            self.emergency_active = True
            print("🚨 EMERGENCY PROTOCOL ACTIVATED")

            for alert in emergency_alerts:
                print(f"   ⚠️ {alert.description}")

            # In production, this would:
            # - Send notifications to safety personnel
            # - Log incidents to safety management system
            # - Trigger audio/visual alarms
            # - Initiate emergency response procedures

    def _update_detection_stats(self, safety_alerts: List[SafetyAlert]):
        """Update detection statistics"""
        self.detection_stats['total_detections'] += len(safety_alerts)

        for alert in safety_alerts:
            if alert.violation_type == SafetyViolationType.MISSING_HELMET:
                self.detection_stats['helmet_violations'] += 1
            elif alert.violation_type == SafetyViolationType.MISSING_VEST:
                self.detection_stats['vest_violations'] += 1
            elif alert.violation_type == SafetyViolationType.FALL_DETECTED:
                self.detection_stats['fall_detections'] += 1

    def _get_system_stats(self) -> Dict:
        """Get comprehensive system statistics"""
        return {
            'detection_stats': self.detection_stats.copy(),
            'active_alerts_count': len(self.active_alerts),
            'worker_profiles_count': len(self.worker_safety_profiles),
            'emergency_active': self.emergency_active,
            'global_safety_state': self.global_safety_state.copy()
        }

    def get_auto_zoom_command(self, results: Dict) -> Optional[Dict]:
        """
        Extract auto-zoom command from processing results

        This is the main interface for your video pipeline
        """
        return results.get('auto_zoom_decision')

    def reset_emergency_state(self):
        """Reset emergency state (for testing/demo purposes)"""
        self.emergency_active = False
        self.active_alerts.clear()
        print("🔄 Emergency state reset")

    def configure_thresholds(self, **kwargs):
        """Dynamically configure detection thresholds"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                print(f"🔧 Updated {key} = {value}")

    def get_safety_dashboard_data(self) -> Dict:
        """Get data for safety monitoring dashboard"""
        return {
            'global_state': self.global_safety_state.copy(),
            'active_alerts': list(self.active_alerts.values()),
            'recent_alerts': list(self.alert_history)[-10:],
            'detection_stats': self.detection_stats.copy(),
            'worker_count': len(self.worker_safety_profiles),
            'emergency_active': self.emergency_active
        }

"""
🔬 Advanced Mathematical Feature Extractor for Safety Monitoring
================================================================

This extends your Laplacian energy approach with advanced mathematical 
feature extraction that can detect:

1. 🏗️ Worker posture and pose estimation
2. 🦺 Safety equipment detection (helmets, vests, harnesses)
3. 📐 Spatial relationships and depth estimation
4. 🚶 Movement patterns and behavior analysis
5. ⚠️ Anomaly detection (falls, unsafe positions)

Mathematical Extensions:
- Gaussian Scale-Space Analysis
- Directional Energy Decomposition  
- Texture Anisotropy Measures
- Spatial Frequency Analysis
- Morphological Feature Extraction

Built with love for worker safety! 💙
"""

import numpy as np
import cv2
import torch
import torch.nn.functional as F
from scipy import ndimage, signal
from scipy.spatial.distance import pdist, squareform
from skimage.feature import local_binary_pattern, graycomatrix, graycoprops
from skimage.filters import gabor
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import time
import warnings
warnings.filterwarnings('ignore')

@dataclass
class AdvancedWorkerFeatures:
    """
    🔬 Comprehensive worker feature representation
    
    This extends your basic Laplacian energy with advanced mathematical
    features for safety monitoring and behavioral analysis.
    """
    # Basic identification
    track_id: int
    zoom_level: float
    bbox: List[int]
    
    # Your original Laplacian energy (preserved!)
    laplacian_energy: float
    
    # 📐 Geometric and structural features
    aspect_ratio: float           # Height/width for posture analysis
    compactness: float           # Perimeter²/area for shape analysis
    orientation_angle: float     # Primary body orientation
    symmetry_score: float        # Left-right symmetry measure
    
    # 🎨 Advanced texture features  
    texture_homogeneity: float   # GLCM homogeneity
    texture_contrast: float      # GLCM contrast
    texture_energy: float        # GLCM energy
    texture_entropy: float       # Shannon entropy of texture
    
    # 📊 Frequency domain features
    frequency_peak: float        # Dominant spatial frequency
    frequency_spread: float      # Frequency distribution width
    high_freq_energy: float      # High frequency content (fine details)
    low_freq_energy: float       # Low frequency content (overall shape)
    
    # 🧭 Directional analysis
    edge_orientation_hist: np.ndarray  # Histogram of edge orientations
    gradient_coherence: float    # How aligned are gradients
    anisotropy_index: float      # Directional uniformity measure
    
    # 🏗️ Morphological features
    solidity: float              # Area/convex hull area
    extent: float                # Area/bounding box area
    eccentricity: float          # Ellipse eccentricity
    perimeter_complexity: float  # Fractal dimension of boundary
    
    # 🔍 Multi-scale analysis
    scale_invariant_features: np.ndarray  # Features across scales
    coarse_features: np.ndarray  # Large scale structure
    fine_features: np.ndarray    # Small scale details
    
    # 🚶 Motion and temporal features (if available)
    motion_energy: float         # Movement intensity
    motion_direction: float      # Primary movement direction
    temporal_consistency: float  # Feature stability over time
    
    # 🦺 Safety-specific features
    helmet_likelihood: float     # Probability of helmet presence
    vest_likelihood: float       # Probability of safety vest
    harness_likelihood: float    # Probability of safety harness
    posture_risk_score: float    # Risk assessment based on posture
    
    # ⚠️ Anomaly detection features
    anomaly_score: float         # Overall anomaly measure
    fall_risk_indicator: float   # Specific fall risk assessment
    unusual_motion_flag: bool    # Abnormal movement detection
    
    # 📊 Confidence and quality metrics
    feature_quality: float       # Overall feature extraction quality
    extraction_time_ms: float    # Performance metric
    
    def to_safety_vector(self) -> np.ndarray:
        """Convert to safety-focused feature vector"""
        return np.array([
            self.helmet_likelihood,
            self.vest_likelihood, 
            self.harness_likelihood,
            self.posture_risk_score,
            self.anomaly_score,
            self.fall_risk_indicator,
            self.aspect_ratio,
            self.orientation_angle,
            self.motion_energy,
            self.temporal_consistency
        ])
    
    def to_full_vector(self) -> np.ndarray:
        """Convert to comprehensive feature vector for ML"""
        basic_features = np.array([
            self.laplacian_energy, self.aspect_ratio, self.compactness,
            self.orientation_angle, self.symmetry_score, self.texture_homogeneity,
            self.texture_contrast, self.texture_energy, self.texture_entropy,
            self.frequency_peak, self.frequency_spread, self.high_freq_energy,
            self.low_freq_energy, self.gradient_coherence, self.anisotropy_index,
            self.solidity, self.extent, self.eccentricity, self.perimeter_complexity,
            self.motion_energy, self.motion_direction, self.temporal_consistency,
            self.helmet_likelihood, self.vest_likelihood, self.harness_likelihood,
            self.posture_risk_score, self.anomaly_score, self.fall_risk_indicator
        ])
        
        # Combine with multi-scale features
        return np.concatenate([
            basic_features,
            self.edge_orientation_hist.flatten(),
            self.scale_invariant_features.flatten(),
            self.coarse_features.flatten(),
            self.fine_features.flatten()
        ])


class AdvancedMathematicalExtractor:
    """
    🔬 Advanced Mathematical Feature Extractor
    
    This class implements state-of-the-art mathematical feature extraction
    methods that extend your Laplacian energy approach for comprehensive
    worker safety monitoring.
    
    Mathematical Foundations:
    1. Scale-space theory for multi-resolution analysis
    2. Differential geometry for shape analysis  
    3. Fourier analysis for frequency domain features
    4. Information theory for entropy measures
    5. Morphological mathematics for structural analysis
    """
    
    def __init__(self, 
                 scales: List[float] = [1.0, 2.0, 4.0, 8.0],
                 orientations: int = 8,
                 safety_models_path: Optional[str] = None,
                 enable_gpu: bool = True):
        """
        Initialize advanced feature extractor
        
        Args:
            scales: Gaussian scales for multi-resolution analysis
            orientations: Number of orientations for directional analysis
            safety_models_path: Path to pre-trained safety detection models
            enable_gpu: Enable GPU acceleration if available
        """
        self.scales = scales
        self.orientations = orientations
        self.enable_gpu = enable_gpu and torch.cuda.is_available()
        
        # Pre-compute Gabor filters for texture analysis
        self._initialize_gabor_filters()
        
        # Pre-compute Gaussian kernels for scale-space analysis
        self._initialize_gaussian_kernels()
        
        # Safety detection models (would be trained on your specific data)
        self.safety_models = self._load_safety_models(safety_models_path)
        
        # Performance tracking
        self.stats = {
            'extractions': 0,
            'total_time': 0.0,
            'gpu_accelerated': 0
        }
        
        print(f"🔬 Advanced Mathematical Extractor initialized")
        print(f"   Scales: {scales}")
        print(f"   Orientations: {orientations}")
        print(f"   GPU enabled: {self.enable_gpu}")
    
    def extract_comprehensive_features(self, frame: np.ndarray, track: Any,
                                     zoom_level: float, 
                                     previous_features: Optional[AdvancedWorkerFeatures] = None
                                     ) -> AdvancedWorkerFeatures:
        """
        🎯 Extract comprehensive mathematical features for worker safety
        
        This is your main entry point that extends your Laplacian approach
        with advanced mathematical analysis.
        
        Args:
            frame: Current video frame
            track: Track object with bounding box
            zoom_level: Current zoom level
            previous_features: Previous frame features for temporal analysis
            
        Returns:
            AdvancedWorkerFeatures with all mathematical features
        """
        start_time = time.time()
        
        # Extract ROI
        roi, bbox = self._extract_roi(frame, track)
        if roi is None:
            return self._create_zero_features(track, zoom_level)
        
        # Convert to grayscale for mathematical analysis
        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY) if len(roi.shape) == 3 else roi
        
        # 1. Your original Laplacian energy (preserved!)
        laplacian_energy = self._compute_laplacian_energy(gray_roi)
        
        # 2. 📐 Geometric and structural analysis
        geometric_features = self._extract_geometric_features(gray_roi, bbox)
        
        # 3. 🎨 Advanced texture analysis
        texture_features = self._extract_texture_features(gray_roi)
        
        # 4. 📊 Frequency domain analysis
        frequency_features = self._extract_frequency_features(gray_roi)
        
        # 5. 🧭 Directional analysis
        directional_features = self._extract_directional_features(gray_roi)
        
        # 6. 🏗️ Morphological analysis
        morphological_features = self._extract_morphological_features(gray_roi)
        
        # 7. 🔍 Multi-scale analysis
        multiscale_features = self._extract_multiscale_features(gray_roi)
        
        # 8. 🚶 Motion analysis (if previous features available)
        motion_features = self._extract_motion_features(
            gray_roi, previous_features, zoom_level
        )
        
        # 9. 🦺 Safety-specific analysis
        safety_features = self._extract_safety_features(roi, gray_roi)
        
        # 10. ⚠️ Anomaly detection
        anomaly_features = self._extract_anomaly_features(
            gray_roi, geometric_features, motion_features
        )
        
        # Combine all features
        extraction_time = (time.time() - start_time) * 1000
        
        comprehensive_features = AdvancedWorkerFeatures(
            track_id=getattr(track, 'track_id', 0),
            zoom_level=zoom_level,
            bbox=bbox,
            laplacian_energy=laplacian_energy,
            
            # Geometric
            aspect_ratio=geometric_features['aspect_ratio'],
            compactness=geometric_features['compactness'],
            orientation_angle=geometric_features['orientation_angle'],
            symmetry_score=geometric_features['symmetry_score'],
            
            # Texture
            texture_homogeneity=texture_features['homogeneity'],
            texture_contrast=texture_features['contrast'],
            texture_energy=texture_features['energy'],
            texture_entropy=texture_features['entropy'],
            
            # Frequency
            frequency_peak=frequency_features['peak'],
            frequency_spread=frequency_features['spread'],
            high_freq_energy=frequency_features['high_energy'],
            low_freq_energy=frequency_features['low_energy'],
            
            # Directional
            edge_orientation_hist=directional_features['orientation_hist'],
            gradient_coherence=directional_features['coherence'],
            anisotropy_index=directional_features['anisotropy'],
            
            # Morphological
            solidity=morphological_features['solidity'],
            extent=morphological_features['extent'],
            eccentricity=morphological_features['eccentricity'],
            perimeter_complexity=morphological_features['complexity'],
            
            # Multi-scale
            scale_invariant_features=multiscale_features['invariant'],
            coarse_features=multiscale_features['coarse'],
            fine_features=multiscale_features['fine'],
            
            # Motion
            motion_energy=motion_features['energy'],
            motion_direction=motion_features['direction'],
            temporal_consistency=motion_features['consistency'],
            
            # Safety
            helmet_likelihood=safety_features['helmet'],
            vest_likelihood=safety_features['vest'],
            harness_likelihood=safety_features['harness'],
            posture_risk_score=safety_features['posture_risk'],
            
            # Anomaly
            anomaly_score=anomaly_features['score'],
            fall_risk_indicator=anomaly_features['fall_risk'],
            unusual_motion_flag=anomaly_features['unusual_motion'],
            
            # Quality
            feature_quality=self._assess_feature_quality(roi, bbox),
            extraction_time_ms=extraction_time
        )
        
        # Update statistics
        self._update_stats(extraction_time)
        
        return comprehensive_features

    def _compute_laplacian_energy(self, gray_roi: np.ndarray) -> float:
        """Your original Laplacian energy computation (preserved exactly!)"""
        try:
            laplacian = cv2.Laplacian(gray_roi, cv2.CV_64F, ksize=3)
            energy = np.sqrt(np.mean(laplacian ** 2))
            return float(energy)
        except:
            return 0.0

    def _extract_roi(self, frame: np.ndarray, track: Any) -> Tuple[Optional[np.ndarray], List[int]]:
        """Extract region of interest from track"""
        try:
            # Extract bounding box
            if hasattr(track, 'to_ltrb'):
                x1, y1, x2, y2 = map(int, track.to_ltrb())
            elif hasattr(track, 'tlbr'):
                x1, y1, x2, y2 = map(int, track.tlbr)
            elif hasattr(track, 'bbox'):
                x1, y1, x2, y2 = map(int, track.bbox)
            else:
                # Default bbox if no track info
                h, w = frame.shape[:2]
                x1, y1, x2, y2 = w//4, h//4, 3*w//4, 3*h//4

            # Ensure bounds are valid
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(frame.shape[1], x2), min(frame.shape[0], y2)

            if x2 <= x1 or y2 <= y1:
                return None, [x1, y1, x2, y2]

            roi = frame[y1:y2, x1:x2]
            return roi, [x1, y1, x2, y2]

        except Exception as e:
            print(f"❌ ROI extraction error: {e}")
            return None, [0, 0, 100, 100]

    def _create_zero_features(self, track: Any, zoom_level: float) -> AdvancedWorkerFeatures:
        """Create zero-filled features for invalid ROI"""
        return AdvancedWorkerFeatures(
            track_id=getattr(track, 'track_id', 0),
            zoom_level=zoom_level,
            bbox=[0, 0, 100, 100],
            laplacian_energy=0.0,
            aspect_ratio=1.0,
            compactness=0.0,
            orientation_angle=0.0,
            symmetry_score=0.0,
            texture_homogeneity=0.0,
            texture_contrast=0.0,
            texture_energy=0.0,
            texture_entropy=0.0,
            frequency_peak=0.0,
            frequency_spread=0.0,
            high_freq_energy=0.0,
            low_freq_energy=0.0,
            edge_orientation_hist=np.zeros(8),
            gradient_coherence=0.0,
            anisotropy_index=0.0,
            solidity=0.0,
            extent=0.0,
            eccentricity=0.0,
            perimeter_complexity=0.0,
            scale_invariant_features=np.zeros(4),
            coarse_features=np.zeros(4),
            fine_features=np.zeros(4),
            motion_energy=0.0,
            motion_direction=0.0,
            temporal_consistency=0.0,
            helmet_likelihood=0.5,
            vest_likelihood=0.5,
            harness_likelihood=0.5,
            posture_risk_score=0.0,
            anomaly_score=0.0,
            fall_risk_indicator=0.0,
            unusual_motion_flag=False,
            feature_quality=0.0,
            extraction_time_ms=0.0
        )

    def _extract_geometric_features(self, gray_roi: np.ndarray, bbox: List[int]) -> Dict[str, float]:
        """📐 Extract geometric and structural features"""
        height, width = gray_roi.shape

        # Aspect ratio (key for posture analysis)
        aspect_ratio = height / max(width, 1)

        # Find contours for shape analysis
        try:
            # Threshold image
            _, binary = cv2.threshold(gray_roi, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                largest_contour = max(contours, key=cv2.contourArea)
                area = cv2.contourArea(largest_contour)
                perimeter = cv2.arcLength(largest_contour, True)

                # Compactness (perimeter² / area)
                compactness = (perimeter ** 2) / max(area, 1) if area > 0 else 0

                # Orientation angle
                if len(largest_contour) >= 5:
                    ellipse = cv2.fitEllipse(largest_contour)
                    orientation_angle = ellipse[2]  # Angle in degrees
                else:
                    orientation_angle = 0.0

                # Symmetry score (left-right symmetry)
                symmetry_score = self._compute_symmetry_score(gray_roi)

            else:
                compactness = 0.0
                orientation_angle = 0.0
                symmetry_score = 0.0

        except Exception as e:
            compactness = 0.0
            orientation_angle = 0.0
            symmetry_score = 0.0

        return {
            'aspect_ratio': aspect_ratio,
            'compactness': compactness,
            'orientation_angle': orientation_angle,
            'symmetry_score': symmetry_score
        }

    def _compute_symmetry_score(self, gray_roi: np.ndarray) -> float:
        """Compute left-right symmetry score"""
        try:
            height, width = gray_roi.shape
            mid = width // 2

            left_half = gray_roi[:, :mid]
            right_half = gray_roi[:, mid:]

            # Flip right half
            right_half_flipped = np.fliplr(right_half)

            # Resize to match if needed
            min_width = min(left_half.shape[1], right_half_flipped.shape[1])
            left_half = left_half[:, :min_width]
            right_half_flipped = right_half_flipped[:, :min_width]

            # Compute correlation
            correlation = np.corrcoef(left_half.flatten(), right_half_flipped.flatten())[0, 1]
            return max(0.0, correlation) if not np.isnan(correlation) else 0.0

        except:
            return 0.0

    def _extract_texture_features(self, gray_roi: np.ndarray) -> Dict[str, float]:
        """🎨 OPTIMIZED texture analysis for real-time performance"""
        try:
            # Fast resize for consistent analysis
            if gray_roi.shape[0] < 16 or gray_roi.shape[1] < 16:
                gray_roi = cv2.resize(gray_roi, (16, 16))
            elif gray_roi.shape[0] > 64 or gray_roi.shape[1] > 64:
                gray_roi = cv2.resize(gray_roi, (32, 32))

            # FAST texture measures using simple statistics
            # Standard deviation as texture measure
            texture_std = np.std(gray_roi.astype(float))

            # Local variance (simplified GLCM alternative)
            kernel = np.ones((3,3), np.float32) / 9
            local_mean = cv2.filter2D(gray_roi.astype(np.float32), -1, kernel)
            local_variance = cv2.filter2D((gray_roi.astype(np.float32) - local_mean)**2, -1, kernel)
            homogeneity = 1.0 / (1.0 + np.mean(local_variance))

            # Gradient-based contrast (much faster than GLCM)
            grad_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)
            contrast = np.mean(np.sqrt(grad_x**2 + grad_y**2))

            # Energy as normalized variance
            energy = texture_std / 255.0

            # Fast entropy using histogram
            hist = cv2.calcHist([gray_roi], [0], None, [16], [0, 256])  # Reduced bins
            hist = hist.flatten() / hist.sum()
            entropy = -np.sum(hist * np.log2(hist + 1e-10))

            return {
                'homogeneity': float(homogeneity),
                'contrast': float(contrast / 100.0),  # Normalize
                'energy': float(energy),
                'entropy': float(entropy / 4.0)  # Normalize
            }

        except Exception as e:
            return {
                'homogeneity': 0.0,
                'contrast': 0.0,
                'energy': 0.0,
                'entropy': 0.0
            }

    def _extract_frequency_features(self, gray_roi: np.ndarray) -> Dict[str, float]:
        """📊 OPTIMIZED frequency analysis for real-time performance"""
        try:
            # Resize for speed if too large
            if gray_roi.shape[0] > 32 or gray_roi.shape[1] > 32:
                gray_roi = cv2.resize(gray_roi, (32, 32))

            # Fast FFT on smaller image
            fft = np.fft.fft2(gray_roi)
            magnitude_spectrum = np.abs(fft)

            # Simple frequency measures
            rows, cols = gray_roi.shape
            center_energy = np.sum(magnitude_spectrum[:rows//4, :cols//4])  # Low freq
            edge_energy = np.sum(magnitude_spectrum) - center_energy  # High freq
            total_energy = center_energy + edge_energy

            # Normalized energy ratios
            low_freq_energy = center_energy / (total_energy + 1e-10)
            high_freq_energy = edge_energy / (total_energy + 1e-10)

            # Peak frequency (simplified)
            flat_spectrum = magnitude_spectrum.flatten()
            peak_idx = np.argmax(flat_spectrum)
            frequency_peak = peak_idx / len(flat_spectrum)

            # Frequency spread (simplified as energy distribution)
            frequency_spread = high_freq_energy  # Higher spread = more high freq

            return {
                'peak': float(frequency_peak),
                'spread': float(frequency_spread),
                'high_energy': float(high_freq_energy),
                'low_energy': float(low_freq_energy)
            }

        except Exception as e:
            return {
                'peak': 0.0,
                'spread': 0.0,
                'high_energy': 0.0,
                'low_energy': 0.0
            }

    def _extract_directional_features(self, gray_roi: np.ndarray) -> Dict[str, Any]:
        """🧭 Directional analysis using gradient information"""
        try:
            # Compute gradients
            grad_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)

            # Gradient magnitude and direction
            magnitude = np.sqrt(grad_x**2 + grad_y**2)
            direction = np.arctan2(grad_y, grad_x)

            # Edge orientation histogram
            orientation_bins = np.linspace(-np.pi, np.pi, self.orientations + 1)
            orientation_hist, _ = np.histogram(direction[magnitude > np.mean(magnitude)],
                                             bins=orientation_bins)
            orientation_hist = orientation_hist.astype(float) / (orientation_hist.sum() + 1e-10)

            # Gradient coherence (how aligned are gradients)
            coherence_matrix = np.array([[np.sum(grad_x**2), np.sum(grad_x * grad_y)],
                                       [np.sum(grad_x * grad_y), np.sum(grad_y**2)]])
            eigenvals = np.linalg.eigvals(coherence_matrix)
            coherence = (eigenvals[0] - eigenvals[1]) / (eigenvals[0] + eigenvals[1] + 1e-10)

            # Anisotropy index
            grad_x_energy = np.sum(grad_x**2)
            grad_y_energy = np.sum(grad_y**2)
            total_energy = grad_x_energy + grad_y_energy
            anisotropy = abs(grad_x_energy - grad_y_energy) / (total_energy + 1e-10)

            return {
                'orientation_hist': orientation_hist,
                'coherence': float(coherence),
                'anisotropy': float(anisotropy)
            }

        except Exception as e:
            return {
                'orientation_hist': np.zeros(self.orientations),
                'coherence': 0.0,
                'anisotropy': 0.0
            }

    def _extract_morphological_features(self, gray_roi: np.ndarray) -> Dict[str, float]:
        """🏗️ OPTIMIZED morphological analysis for real-time performance"""
        try:
            # Fast threshold using simple binary threshold
            threshold_val = np.mean(gray_roi)
            binary = (gray_roi > threshold_val).astype(np.uint8) * 255

            # Find contours with simplified approximation
            contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return {'solidity': 0.5, 'extent': 0.5, 'eccentricity': 0.5, 'complexity': 0.5}

            largest_contour = max(contours, key=cv2.contourArea)
            area = cv2.contourArea(largest_contour)

            if area < 10:  # Too small
                return {'solidity': 0.5, 'extent': 0.5, 'eccentricity': 0.5, 'complexity': 0.5}

            # Fast bounding box calculations
            x, y, w, h = cv2.boundingRect(largest_contour)

            # Simplified solidity (area / bounding box area as approximation)
            bbox_area = w * h
            solidity = area / (bbox_area + 1e-10)

            # Extent (same as solidity in this fast approximation)
            extent = solidity

            # Fast eccentricity from bounding box aspect ratio
            aspect_ratio = max(w, h) / (min(w, h) + 1e-10)
            eccentricity = min(1.0, (aspect_ratio - 1.0) / 10.0)  # Normalized

            # Fast complexity from perimeter to area ratio
            perimeter = cv2.arcLength(largest_contour, True)
            complexity = min(1.0, perimeter / (2 * np.sqrt(np.pi * area) + 1e-10) - 1.0)

            return {
                'solidity': float(max(0.0, min(1.0, solidity))),
                'extent': float(max(0.0, min(1.0, extent))),
                'eccentricity': float(max(0.0, min(1.0, eccentricity))),
                'complexity': float(max(0.0, min(1.0, complexity)))
            }

        except Exception as e:
            return {
                'solidity': 0.5,
                'extent': 0.5,
                'eccentricity': 0.5,
                'complexity': 0.5
            }

    def _extract_multiscale_features(self, gray_roi: np.ndarray) -> Dict[str, np.ndarray]:
        """🔍 Multi-scale analysis using Gaussian pyramid"""
        try:
            # Scale-invariant features across different scales
            scale_features = []
            coarse_features = []
            fine_features = []

            for scale in self.scales:
                # Apply Gaussian blur at different scales
                blurred = cv2.GaussianBlur(gray_roi, (0, 0), scale)

                # Compute Laplacian energy at this scale
                laplacian = cv2.Laplacian(blurred, cv2.CV_64F)
                energy = np.sqrt(np.mean(laplacian**2))
                scale_features.append(energy)

                # Separate coarse and fine features
                if scale <= 2.0:
                    fine_features.append(energy)
                else:
                    coarse_features.append(energy)

            # Pad arrays to consistent length
            scale_features = np.array(scale_features[:4])  # Take first 4
            if len(scale_features) < 4:
                scale_features = np.pad(scale_features, (0, 4 - len(scale_features)))

            coarse_features = np.array(coarse_features[:4])
            if len(coarse_features) < 4:
                coarse_features = np.pad(coarse_features, (0, 4 - len(coarse_features)))

            fine_features = np.array(fine_features[:4])
            if len(fine_features) < 4:
                fine_features = np.pad(fine_features, (0, 4 - len(fine_features)))

            return {
                'invariant': scale_features,
                'coarse': coarse_features,
                'fine': fine_features
            }

        except Exception as e:
            return {
                'invariant': np.zeros(4),
                'coarse': np.zeros(4),
                'fine': np.zeros(4)
            }

    def _extract_motion_features(self, gray_roi: np.ndarray,
                               previous_features: Optional[AdvancedWorkerFeatures],
                               zoom_level: float) -> Dict[str, float]:
        """🚶 Motion analysis using temporal information"""
        try:
            if previous_features is None:
                return {'energy': 0.0, 'direction': 0.0, 'consistency': 1.0}

            # Simple motion energy based on feature differences
            current_energy = np.mean(gray_roi.astype(float)**2)

            # Estimate motion from feature changes
            motion_energy = abs(current_energy - previous_features.laplacian_energy) / (zoom_level**2)

            # Motion direction (simplified)
            motion_direction = 0.0  # Would need optical flow for accurate direction

            # Temporal consistency (how stable are features)
            consistency = 1.0 / (1.0 + motion_energy)

            return {
                'energy': float(motion_energy),
                'direction': float(motion_direction),
                'consistency': float(consistency)
            }

        except Exception as e:
            return {'energy': 0.0, 'direction': 0.0, 'consistency': 1.0}

    def _extract_safety_features(self, roi: np.ndarray, gray_roi: np.ndarray) -> Dict[str, float]:
        """🦺 Safety-specific feature extraction"""
        try:
            # Convert to HSV for color analysis
            hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV) if len(roi.shape) == 3 else cv2.cvtColor(cv2.cvtColor(gray_roi, cv2.COLOR_GRAY2BGR), cv2.COLOR_BGR2HSV)

            # Helmet detection (look for hard hat colors and shapes in upper region)
            upper_region = hsv[:hsv.shape[0]//3, :]  # Top third

            # Yellow/orange helmet detection
            yellow_lower = np.array([15, 50, 50])
            yellow_upper = np.array([35, 255, 255])
            yellow_mask = cv2.inRange(upper_region, yellow_lower, yellow_upper)
            helmet_likelihood = np.sum(yellow_mask > 0) / (upper_region.shape[0] * upper_region.shape[1])

            # Safety vest detection (high-visibility colors)
            # Orange vest
            orange_lower = np.array([5, 50, 50])
            orange_upper = np.array([15, 255, 255])
            orange_mask = cv2.inRange(hsv, orange_lower, orange_upper)

            # Yellow vest
            yellow_vest_mask = cv2.inRange(hsv, yellow_lower, yellow_upper)

            vest_likelihood = (np.sum(orange_mask > 0) + np.sum(yellow_vest_mask > 0)) / (hsv.shape[0] * hsv.shape[1])

            # Harness detection (simplified - look for straps/lines)
            edges = cv2.Canny(gray_roi, 50, 150)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=20, minLineLength=10, maxLineGap=5)
            harness_likelihood = len(lines) / 50.0 if lines is not None else 0.0
            harness_likelihood = min(1.0, harness_likelihood)

            # Posture risk assessment
            height, width = gray_roi.shape
            aspect_ratio = height / width

            # Risk increases with extreme aspect ratios (lying down, unusual poses)
            if aspect_ratio < 0.5:  # Very wide (lying down)
                posture_risk = 0.8
            elif aspect_ratio > 3.0:  # Very tall (unusual)
                posture_risk = 0.6
            else:
                posture_risk = max(0.0, (abs(aspect_ratio - 1.5) - 0.5) / 2.0)

            return {
                'helmet': float(helmet_likelihood),
                'vest': float(vest_likelihood),
                'harness': float(harness_likelihood),
                'posture_risk': float(posture_risk)
            }

        except Exception as e:
            return {
                'helmet': 0.5,  # Neutral likelihood
                'vest': 0.5,
                'harness': 0.5,
                'posture_risk': 0.0
            }

    def _extract_anomaly_features(self, gray_roi: np.ndarray,
                                geometric_features: Dict,
                                motion_features: Dict) -> Dict[str, Any]:
        """⚠️ Anomaly detection features"""
        try:
            # Overall anomaly score based on multiple factors
            anomaly_factors = []

            # Geometric anomalies
            aspect_ratio = geometric_features['aspect_ratio']
            if aspect_ratio < 0.3 or aspect_ratio > 4.0:
                anomaly_factors.append(0.7)

            # Motion anomalies
            motion_energy = motion_features['energy']
            if motion_energy > 10.0:  # Very high motion
                anomaly_factors.append(0.6)

            # Texture anomalies (very uniform or very chaotic)
            texture_variance = np.var(gray_roi.astype(float))
            if texture_variance < 10 or texture_variance > 5000:
                anomaly_factors.append(0.5)

            # Overall anomaly score
            anomaly_score = np.mean(anomaly_factors) if anomaly_factors else 0.0

            # Fall risk indicator (based on aspect ratio and motion)
            fall_risk = 0.0
            if aspect_ratio < 0.6:  # Lying down
                fall_risk = 0.9
            elif aspect_ratio < 0.8 and motion_energy > 5.0:  # Falling motion
                fall_risk = 0.7

            # Unusual motion flag
            unusual_motion = motion_energy > 15.0 or (motion_energy > 8.0 and aspect_ratio < 0.7)

            return {
                'score': float(anomaly_score),
                'fall_risk': float(fall_risk),
                'unusual_motion': bool(unusual_motion)
            }

        except Exception as e:
            return {
                'score': 0.0,
                'fall_risk': 0.0,
                'unusual_motion': False
            }

    def _assess_feature_quality(self, roi: np.ndarray, bbox: List[int]) -> float:
        """📊 Assess overall quality of feature extraction"""
        try:
            # Size quality (larger ROIs generally better)
            area = (bbox[2] - bbox[0]) * (bbox[3] - bbox[1])
            size_quality = min(1.0, area / 10000.0)  # Normalize to 100x100 pixels

            # Contrast quality
            contrast = np.std(roi.astype(float))
            contrast_quality = min(1.0, contrast / 50.0)

            # Sharpness quality (using Laplacian variance)
            if len(roi.shape) == 3:
                gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
            else:
                gray = roi

            laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()
            sharpness_quality = min(1.0, laplacian_var / 1000.0)

            # Overall quality
            quality = (size_quality + contrast_quality + sharpness_quality) / 3.0
            return float(quality)

        except Exception as e:
            return 0.5  # Neutral quality

    def _update_stats(self, extraction_time: float):
        """Update performance statistics"""
        self.stats['extractions'] += 1
        self.stats['total_time'] += extraction_time
        if self.enable_gpu:
            self.stats['gpu_accelerated'] += 1

    def _initialize_gabor_filters(self):
        """Pre-compute Gabor filters for texture analysis"""
        self.gabor_filters = []
        for theta in np.linspace(0, np.pi, self.orientations, endpoint=False):
            for frequency in [0.1, 0.3, 0.5]:
                kernel = cv2.getGaborKernel((21, 21), 5, theta, 2*np.pi*frequency, 0.5, 0, ktype=cv2.CV_32F)
                self.gabor_filters.append(kernel)

    def _initialize_gaussian_kernels(self):
        """Pre-compute Gaussian kernels for scale-space analysis"""
        self.gaussian_kernels = {}
        for scale in self.scales:
            size = int(6 * scale + 1)
            if size % 2 == 0:
                size += 1
            kernel = cv2.getGaussianKernel(size, scale)
            self.gaussian_kernels[scale] = kernel

    def _load_safety_models(self, models_path: Optional[str]) -> Dict:
        """Load pre-trained safety detection models"""
        # Placeholder for future ML models
        # In production, you would load trained models for helmet/vest detection
        return {
            'helmet_detector': None,
            'vest_detector': None,
            'pose_estimator': None
        }

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        avg_time = self.stats['total_time'] / max(1, self.stats['extractions'])
        return {
            'total_extractions': self.stats['extractions'],
            'average_time_ms': avg_time,
            'gpu_accelerated_percent': (self.stats['gpu_accelerated'] / max(1, self.stats['extractions'])) * 100,
            'fps_capability': 1000.0 / max(1, avg_time)
        }

    def reset_stats(self):
        """Reset performance statistics"""
        self.stats = {
            'extractions': 0,
            'total_time': 0.0,
            'gpu_accelerated': 0
        }

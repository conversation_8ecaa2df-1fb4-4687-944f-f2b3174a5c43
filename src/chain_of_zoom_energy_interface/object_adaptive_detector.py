"""
🎯 Object-Adaptive Mathematical Detection Enhancement
====================================================

Addressing <PERSON><PERSON>'s feedback: "object-based tracking might require different features"

This module implements object-specific mathematical feature extraction and detection
enhancement that adapts to different object types (person, vehicle, equipment) while
maintaining the universal Laplacian energy foundation.

Key Innovations:
1. 🔍 Pre-Detection Enhancement: Use math to strengthen weak YOLO detections
2. 🎯 Object-Adaptive Features: Different feature sets for different object types
3. 🚀 Detection Confidence Boosting: Mathematical validation of detections
4. 🔬 Universal Math Foundation: Laplacian energy works on all objects
5. 🧠 Smart Feature Selection: Choose optimal features per object class

Built to empower tracking with mathematical intelligence! 💙
"""

import cv2
import numpy as np
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import math

class ObjectType(Enum):
    """Object type classification for adaptive feature extraction"""
    PERSON = "person"
    VEHICLE = "vehicle"  # cars, trucks, buses
    EQUIPMENT = "equipment"  # forklifts, machinery
    SMALL_VEHICLE = "small_vehicle"  # motorcycles, bicycles
    UNKNOWN = "unknown"

@dataclass
class ObjectAdaptiveFeatures:
    """
    🎯 Object-adaptive feature representation
    
    Features are selected and weighted based on object type for optimal discrimination
    """
    # Universal features (work for all objects)
    laplacian_energy: float
    gradient_magnitude: float
    texture_variance: float
    edge_density: float
    
    # Object-specific features
    shape_features: Dict[str, float]
    motion_features: Dict[str, float]
    appearance_features: Dict[str, float]
    
    # Detection enhancement
    detection_confidence: float
    mathematical_confidence: float
    combined_confidence: float
    
    # Metadata
    object_type: ObjectType
    feature_quality: float
    extraction_time_ms: float

class ObjectAdaptiveDetector:
    """
    🎯 Object-Adaptive Mathematical Detection Enhancement
    
    This class implements Alankar's insight about object-specific features
    while maintaining the universal mathematical foundation.
    """
    
    def __init__(self):
        """Initialize the object-adaptive detector"""
        print("🎯 Initializing Object-Adaptive Mathematical Detector...")
        print("   Implementing Alankar's feedback on object-specific features")
        
        # Object type mappings
        self.yolo_to_object_type = {
            0: ObjectType.PERSON,           # person
            2: ObjectType.VEHICLE,          # car
            3: ObjectType.SMALL_VEHICLE,    # motorcycle
            5: ObjectType.VEHICLE,          # bus
            7: ObjectType.EQUIPMENT,        # truck (often forklift)
            1: ObjectType.SMALL_VEHICLE,    # bicycle
            6: ObjectType.VEHICLE,          # train
            8: ObjectType.VEHICLE,          # boat
        }
        
        # Object-specific feature weights
        self.feature_weights = {
            ObjectType.PERSON: {
                'laplacian_energy': 1.0,
                'aspect_ratio': 1.5,      # Important for person detection
                'vertical_edges': 1.3,    # People have strong vertical structure
                'symmetry': 1.2,          # Human symmetry
                'texture_variance': 0.8,  # Less important for people
            },
            ObjectType.VEHICLE: {
                'laplacian_energy': 1.0,
                'horizontal_edges': 1.4,  # Vehicles have strong horizontal lines
                'corner_density': 1.3,    # Rectangular shapes
                'texture_variance': 1.2,  # Metal/paint textures
                'size_consistency': 1.1,  # Vehicles have consistent sizes
            },
            ObjectType.EQUIPMENT: {
                'laplacian_energy': 1.0,
                'edge_density': 1.5,      # Machinery has many edges
                'texture_complexity': 1.4, # Complex mechanical textures
                'angular_features': 1.3,   # Mechanical angles
                'motion_patterns': 1.2,    # Equipment moves differently
            },
            ObjectType.SMALL_VEHICLE: {
                'laplacian_energy': 1.0,
                'aspect_ratio': 1.1,      # Different from cars
                'edge_sharpness': 1.3,    # Sharp edges on bikes/motorcycles
                'size_constraints': 1.4,  # Size validation important
            }
        }
        
        # Detection enhancement thresholds
        self.confidence_thresholds = {
            'weak_detection': 0.3,      # Below this, try to enhance
            'strong_detection': 0.7,    # Above this, trust YOLO
            'mathematical_boost': 0.2,  # How much math can boost confidence
        }
        
        print("✅ Object-Adaptive Detector initialized")
        print(f"   Supporting {len(self.yolo_to_object_type)} object types")
        print(f"   Feature adaptation for: {list(set(self.yolo_to_object_type.values()))}")
    
    def enhance_detections(self, frame: np.ndarray, yolo_results) -> List[Dict]:
        """
        🚀 Enhance YOLO detections with mathematical analysis
        
        This is the main entry point that strengthens weak detections
        and validates strong ones using object-adaptive features.
        """
        enhanced_detections = []
        
        if not yolo_results or len(yolo_results) == 0:
            return enhanced_detections
        
        # Process each YOLO result
        for result in yolo_results:
            if result.boxes is None:
                continue
                
            boxes = result.boxes
            for i in range(len(boxes)):
                try:
                    # Extract basic detection info
                    bbox = boxes.xyxy[i].cpu().numpy()
                    confidence = float(boxes.conf[i].cpu().numpy())
                    class_id = int(boxes.cls[i].cpu().numpy())
                    track_id = int(boxes.id[i].cpu().numpy()) if boxes.id is not None else -1
                    
                    # Determine object type
                    object_type = self.yolo_to_object_type.get(class_id, ObjectType.UNKNOWN)
                    
                    # Extract mathematical features
                    math_features = self._extract_object_adaptive_features(
                        frame, bbox, object_type
                    )
                    
                    # Enhance detection confidence
                    enhanced_confidence = self._enhance_detection_confidence(
                        confidence, math_features, object_type
                    )
                    
                    # Create enhanced detection
                    enhanced_detection = {
                        'bbox': bbox,
                        'confidence': confidence,
                        'enhanced_confidence': enhanced_confidence,
                        'class_id': class_id,
                        'track_id': track_id,
                        'object_type': object_type,
                        'math_features': math_features,
                        'enhancement_applied': enhanced_confidence > confidence
                    }
                    
                    enhanced_detections.append(enhanced_detection)
                    
                    # Log significant enhancements
                    if enhanced_confidence > confidence + 0.1:
                        print(f"🚀 Enhanced {object_type.value} detection: "
                              f"{confidence:.2f} → {enhanced_confidence:.2f}")
                
                except Exception as e:
                    # Skip problematic detections
                    continue
        
        return enhanced_detections
    
    def _extract_object_adaptive_features(self, frame: np.ndarray, bbox: np.ndarray, 
                                        object_type: ObjectType) -> ObjectAdaptiveFeatures:
        """
        🔬 Extract features adapted to specific object type
        
        This implements Alankar's insight about different features for different objects
        """
        start_time = time.time()
        
        # Extract ROI
        x1, y1, x2, y2 = map(int, bbox)
        roi = frame[y1:y2, x1:x2]
        
        if roi.size == 0:
            return self._create_zero_features(object_type)
        
        # Convert to grayscale for mathematical analysis
        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY) if len(roi.shape) == 3 else roi
        
        # Universal mathematical features (work for all objects)
        laplacian_energy = self._compute_laplacian_energy(gray_roi)
        gradient_magnitude = self._compute_gradient_magnitude(gray_roi)
        texture_variance = self._compute_texture_variance(gray_roi)
        edge_density = self._compute_edge_density(gray_roi)
        
        # Object-specific feature extraction
        if object_type == ObjectType.PERSON:
            shape_features = self._extract_person_shape_features(gray_roi)
            motion_features = self._extract_person_motion_features(gray_roi)
            appearance_features = self._extract_person_appearance_features(roi)
        elif object_type in [ObjectType.VEHICLE, ObjectType.EQUIPMENT]:
            shape_features = self._extract_vehicle_shape_features(gray_roi)
            motion_features = self._extract_vehicle_motion_features(gray_roi)
            appearance_features = self._extract_vehicle_appearance_features(roi)
        elif object_type == ObjectType.SMALL_VEHICLE:
            shape_features = self._extract_small_vehicle_features(gray_roi)
            motion_features = self._extract_small_vehicle_motion_features(gray_roi)
            appearance_features = self._extract_small_vehicle_appearance_features(roi)
        else:
            # Unknown object type - use generic features
            shape_features = self._extract_generic_shape_features(gray_roi)
            motion_features = self._extract_generic_motion_features(gray_roi)
            appearance_features = self._extract_generic_appearance_features(roi)
        
        # Calculate feature quality
        feature_quality = self._assess_feature_quality(gray_roi, object_type)
        
        extraction_time = (time.time() - start_time) * 1000
        
        return ObjectAdaptiveFeatures(
            laplacian_energy=laplacian_energy,
            gradient_magnitude=gradient_magnitude,
            texture_variance=texture_variance,
            edge_density=edge_density,
            shape_features=shape_features,
            motion_features=motion_features,
            appearance_features=appearance_features,
            detection_confidence=0.0,  # Will be set later
            mathematical_confidence=0.0,  # Will be calculated
            combined_confidence=0.0,  # Will be calculated
            object_type=object_type,
            feature_quality=feature_quality,
            extraction_time_ms=extraction_time
        )
    
    def _compute_laplacian_energy(self, gray_roi: np.ndarray) -> float:
        """Universal Laplacian energy - works for all object types"""
        try:
            laplacian = cv2.Laplacian(gray_roi, cv2.CV_64F, ksize=3)
            energy = np.sqrt(np.mean(laplacian ** 2))
            return float(energy)
        except:
            return 0.0
    
    def _compute_gradient_magnitude(self, gray_roi: np.ndarray) -> float:
        """Compute gradient magnitude for edge strength"""
        try:
            grad_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)
            magnitude = np.sqrt(grad_x**2 + grad_y**2)
            return float(np.mean(magnitude))
        except:
            return 0.0
    
    def _compute_texture_variance(self, gray_roi: np.ndarray) -> float:
        """Compute texture variance for surface analysis"""
        try:
            return float(np.var(gray_roi))
        except:
            return 0.0
    
    def _compute_edge_density(self, gray_roi: np.ndarray) -> float:
        """Compute edge density for structural analysis"""
        try:
            edges = cv2.Canny(gray_roi, 50, 150)
            edge_pixels = np.sum(edges > 0)
            total_pixels = gray_roi.shape[0] * gray_roi.shape[1]
            return float(edge_pixels / total_pixels) if total_pixels > 0 else 0.0
        except:
            return 0.0

    # ==================== OBJECT-SPECIFIC FEATURE EXTRACTION ====================

    def _extract_person_shape_features(self, gray_roi: np.ndarray) -> Dict[str, float]:
        """Extract shape features optimized for person detection"""
        try:
            h, w = gray_roi.shape
            aspect_ratio = h / w if w > 0 else 0.0

            # Vertical edge strength (people have strong vertical structure)
            sobel_y = cv2.Sobel(gray_roi, cv2.CV_64F, 0, 1, ksize=3)
            vertical_edges = np.mean(np.abs(sobel_y))

            # Symmetry analysis (humans are roughly symmetric)
            left_half = gray_roi[:, :w//2]
            right_half = cv2.flip(gray_roi[:, w//2:], 1)
            min_width = min(left_half.shape[1], right_half.shape[1])
            if min_width > 0:
                symmetry = 1.0 - np.mean(np.abs(left_half[:, :min_width] - right_half[:, :min_width])) / 255.0
            else:
                symmetry = 0.0

            return {
                'aspect_ratio': float(aspect_ratio),
                'vertical_edges': float(vertical_edges),
                'symmetry': float(max(0.0, symmetry)),
                'height_width_ratio': float(aspect_ratio)
            }
        except:
            return {'aspect_ratio': 0.0, 'vertical_edges': 0.0, 'symmetry': 0.0, 'height_width_ratio': 0.0}

    def _extract_vehicle_shape_features(self, gray_roi: np.ndarray) -> Dict[str, float]:
        """Extract shape features optimized for vehicle detection"""
        try:
            h, w = gray_roi.shape
            aspect_ratio = w / h if h > 0 else 0.0  # Vehicles are typically wider than tall

            # Horizontal edge strength (vehicles have strong horizontal lines)
            sobel_x = cv2.Sobel(gray_roi, cv2.CV_64F, 1, 0, ksize=3)
            horizontal_edges = np.mean(np.abs(sobel_x))

            # Corner detection (vehicles have rectangular shapes)
            corners = cv2.goodFeaturesToTrack(gray_roi, maxCorners=100, qualityLevel=0.01, minDistance=10)
            corner_density = len(corners) / (h * w) if corners is not None else 0.0

            # Rectangular shape analysis
            contours, _ = cv2.findContours(cv2.Canny(gray_roi, 50, 150), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            rectangularity = 0.0
            if contours:
                largest_contour = max(contours, key=cv2.contourArea)
                if cv2.contourArea(largest_contour) > 100:
                    rect_area = cv2.boundingRect(largest_contour)[2] * cv2.boundingRect(largest_contour)[3]
                    contour_area = cv2.contourArea(largest_contour)
                    rectangularity = contour_area / rect_area if rect_area > 0 else 0.0

            return {
                'aspect_ratio': float(aspect_ratio),
                'horizontal_edges': float(horizontal_edges),
                'corner_density': float(corner_density * 1000),  # Scale for visibility
                'rectangularity': float(rectangularity)
            }
        except:
            return {'aspect_ratio': 0.0, 'horizontal_edges': 0.0, 'corner_density': 0.0, 'rectangularity': 0.0}

    def _extract_small_vehicle_features(self, gray_roi: np.ndarray) -> Dict[str, float]:
        """Extract features for motorcycles, bicycles"""
        try:
            h, w = gray_roi.shape

            # Edge sharpness (bikes/motorcycles have sharp edges)
            edges = cv2.Canny(gray_roi, 50, 150)
            edge_sharpness = np.sum(edges > 0) / (h * w)

            # Size constraints (should be smaller than cars)
            size_score = 1.0 / (1.0 + (h * w) / 10000.0)  # Penalty for large size

            # Linear features (bikes have linear structures)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=20, minLineLength=10, maxLineGap=5)
            line_density = len(lines) / (h * w) if lines is not None else 0.0

            return {
                'edge_sharpness': float(edge_sharpness),
                'size_score': float(size_score),
                'line_density': float(line_density * 1000),
                'compactness': float((h * w) / (h + w) if (h + w) > 0 else 0.0)
            }
        except:
            return {'edge_sharpness': 0.0, 'size_score': 0.0, 'line_density': 0.0, 'compactness': 0.0}

    def _extract_generic_shape_features(self, gray_roi: np.ndarray) -> Dict[str, float]:
        """Generic shape features for unknown objects"""
        try:
            h, w = gray_roi.shape
            return {
                'aspect_ratio': float(h / w if w > 0 else 0.0),
                'area': float(h * w),
                'perimeter_area_ratio': float((2 * (h + w)) / (h * w) if (h * w) > 0 else 0.0),
                'compactness': float((h * w) / ((h + w) ** 2) if (h + w) > 0 else 0.0)
            }
        except:
            return {'aspect_ratio': 0.0, 'area': 0.0, 'perimeter_area_ratio': 0.0, 'compactness': 0.0}

    # ==================== MOTION AND APPEARANCE FEATURES ====================

    def _extract_person_motion_features(self, gray_roi: np.ndarray) -> Dict[str, float]:
        """Motion features for person tracking"""
        # Simplified - in full implementation would use optical flow
        return {'motion_energy': 0.0, 'motion_direction': 0.0, 'stability': 1.0}

    def _extract_vehicle_motion_features(self, gray_roi: np.ndarray) -> Dict[str, float]:
        """Motion features for vehicle tracking"""
        return {'motion_energy': 0.0, 'motion_smoothness': 1.0, 'directional_consistency': 1.0}

    def _extract_small_vehicle_motion_features(self, gray_roi: np.ndarray) -> Dict[str, float]:
        """Motion features for small vehicles"""
        return {'motion_energy': 0.0, 'agility_score': 1.0, 'path_consistency': 1.0}

    def _extract_generic_motion_features(self, gray_roi: np.ndarray) -> Dict[str, float]:
        """Generic motion features"""
        return {'motion_energy': 0.0, 'motion_consistency': 1.0}

    def _extract_person_appearance_features(self, roi: np.ndarray) -> Dict[str, float]:
        """Appearance features for person detection"""
        try:
            # Color analysis for clothing/skin detection
            hsv = cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)

            # Skin color detection (simplified)
            skin_mask = cv2.inRange(hsv, (0, 20, 70), (20, 255, 255))
            skin_ratio = np.sum(skin_mask > 0) / (roi.shape[0] * roi.shape[1])

            # Clothing color variance
            color_variance = np.var(roi.reshape(-1, 3), axis=0).mean()

            return {
                'skin_ratio': float(skin_ratio),
                'color_variance': float(color_variance),
                'brightness': float(np.mean(roi)),
                'color_complexity': float(np.std(roi))
            }
        except:
            return {'skin_ratio': 0.0, 'color_variance': 0.0, 'brightness': 0.0, 'color_complexity': 0.0}

    def _extract_vehicle_appearance_features(self, roi: np.ndarray) -> Dict[str, float]:
        """Appearance features for vehicle detection"""
        try:
            # Metallic/paint surface analysis
            gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # Surface smoothness (vehicles have smooth painted surfaces)
            smoothness = 1.0 / (1.0 + np.std(gray))

            # Color uniformity (vehicles often have uniform colors)
            color_uniformity = 1.0 / (1.0 + np.std(roi.reshape(-1, 3), axis=0).mean())

            return {
                'surface_smoothness': float(smoothness),
                'color_uniformity': float(color_uniformity),
                'metallic_score': float(np.mean(gray) / 255.0),
                'reflectance': float(np.max(gray) / 255.0)
            }
        except:
            return {'surface_smoothness': 0.0, 'color_uniformity': 0.0, 'metallic_score': 0.0, 'reflectance': 0.0}

    def _extract_small_vehicle_appearance_features(self, roi: np.ndarray) -> Dict[str, float]:
        """Appearance features for small vehicles"""
        return self._extract_vehicle_appearance_features(roi)  # Similar to vehicles

    def _extract_generic_appearance_features(self, roi: np.ndarray) -> Dict[str, float]:
        """Generic appearance features"""
        try:
            return {
                'brightness': float(np.mean(roi)),
                'contrast': float(np.std(roi)),
                'color_diversity': float(np.std(roi.reshape(-1, 3), axis=0).mean()),
                'saturation': float(np.mean(cv2.cvtColor(roi, cv2.COLOR_BGR2HSV)[:,:,1]))
            }
        except:
            return {'brightness': 0.0, 'contrast': 0.0, 'color_diversity': 0.0, 'saturation': 0.0}

    # ==================== DETECTION ENHANCEMENT ====================

    def _enhance_detection_confidence(self, yolo_confidence: float,
                                    math_features: ObjectAdaptiveFeatures,
                                    object_type: ObjectType) -> float:
        """
        🚀 Enhance detection confidence using mathematical analysis

        This is where we implement Alankar's insight - use math to strengthen detections
        """
        try:
            # Calculate mathematical confidence based on object-specific features
            math_confidence = self._calculate_mathematical_confidence(math_features, object_type)

            # Determine enhancement strategy based on YOLO confidence
            if yolo_confidence < self.confidence_thresholds['weak_detection']:
                # Weak detection - try to boost with math
                enhancement_factor = math_confidence * self.confidence_thresholds['mathematical_boost']
                enhanced_confidence = min(1.0, yolo_confidence + enhancement_factor)

            elif yolo_confidence > self.confidence_thresholds['strong_detection']:
                # Strong detection - validate with math
                if math_confidence > 0.5:
                    enhanced_confidence = min(1.0, yolo_confidence * 1.05)  # Small boost
                else:
                    enhanced_confidence = yolo_confidence * 0.95  # Small penalty
            else:
                # Medium detection - moderate enhancement
                enhancement_factor = (math_confidence - 0.5) * 0.1
                enhanced_confidence = max(0.0, min(1.0, yolo_confidence + enhancement_factor))

            # Update features with confidence values
            math_features.detection_confidence = yolo_confidence
            math_features.mathematical_confidence = math_confidence
            math_features.combined_confidence = enhanced_confidence

            return enhanced_confidence

        except Exception as e:
            return yolo_confidence  # Fallback to original confidence

    def _calculate_mathematical_confidence(self, features: ObjectAdaptiveFeatures,
                                         object_type: ObjectType) -> float:
        """Calculate confidence based on mathematical features"""
        try:
            # Get object-specific feature weights
            weights = self.feature_weights.get(object_type, self.feature_weights[ObjectType.PERSON])

            # Universal features (work for all objects)
            universal_score = (
                features.laplacian_energy * weights.get('laplacian_energy', 1.0) +
                features.gradient_magnitude * 0.1 +
                features.edge_density * 2.0
            ) / 100.0  # Normalize

            # Object-specific features
            shape_score = self._score_shape_features(features.shape_features, object_type)
            appearance_score = self._score_appearance_features(features.appearance_features, object_type)

            # Combine scores
            total_score = (universal_score + shape_score + appearance_score) / 3.0

            # Clamp to [0, 1]
            return max(0.0, min(1.0, total_score))

        except:
            return 0.5  # Neutral confidence

    def _score_shape_features(self, shape_features: Dict[str, float], object_type: ObjectType) -> float:
        """Score shape features based on object type expectations"""
        try:
            if object_type == ObjectType.PERSON:
                # People should have vertical aspect ratio and symmetry
                aspect_score = 1.0 if 1.5 <= shape_features.get('aspect_ratio', 0) <= 3.0 else 0.5
                symmetry_score = shape_features.get('symmetry', 0)
                return (aspect_score + symmetry_score) / 2.0

            elif object_type in [ObjectType.VEHICLE, ObjectType.EQUIPMENT]:
                # Vehicles should have horizontal aspect ratio and rectangularity
                aspect_score = 1.0 if 0.3 <= shape_features.get('aspect_ratio', 0) <= 2.0 else 0.5
                rect_score = shape_features.get('rectangularity', 0)
                return (aspect_score + rect_score) / 2.0

            elif object_type == ObjectType.SMALL_VEHICLE:
                # Small vehicles should have good edge sharpness and size constraints
                edge_score = min(1.0, shape_features.get('edge_sharpness', 0) * 10)
                size_score = shape_features.get('size_score', 0)
                return (edge_score + size_score) / 2.0

            return 0.5  # Neutral for unknown types

        except:
            return 0.5

    def _score_appearance_features(self, appearance_features: Dict[str, float], object_type: ObjectType) -> float:
        """Score appearance features based on object type expectations"""
        try:
            if object_type == ObjectType.PERSON:
                # People should have some skin ratio and color complexity
                skin_score = min(1.0, appearance_features.get('skin_ratio', 0) * 5)
                complexity_score = min(1.0, appearance_features.get('color_complexity', 0) / 50)
                return (skin_score + complexity_score) / 2.0

            elif object_type in [ObjectType.VEHICLE, ObjectType.EQUIPMENT]:
                # Vehicles should have surface smoothness and color uniformity
                smooth_score = appearance_features.get('surface_smoothness', 0)
                uniform_score = appearance_features.get('color_uniformity', 0)
                return (smooth_score + uniform_score) / 2.0

            return 0.5  # Neutral for other types

        except:
            return 0.5

    def _assess_feature_quality(self, gray_roi: np.ndarray, object_type: ObjectType) -> float:
        """Assess the quality of extracted features"""
        try:
            h, w = gray_roi.shape

            # Size quality (larger ROIs generally have better features)
            size_quality = min(1.0, (h * w) / 10000.0)

            # Contrast quality (higher contrast = better features)
            contrast_quality = min(1.0, np.std(gray_roi) / 50.0)

            # Sharpness quality (sharper images = better features)
            laplacian_var = cv2.Laplacian(gray_roi, cv2.CV_64F).var()
            sharpness_quality = min(1.0, laplacian_var / 1000.0)

            return (size_quality + contrast_quality + sharpness_quality) / 3.0

        except:
            return 0.5

    def _create_zero_features(self, object_type: ObjectType) -> ObjectAdaptiveFeatures:
        """Create zero-filled features for invalid ROI"""
        return ObjectAdaptiveFeatures(
            laplacian_energy=0.0,
            gradient_magnitude=0.0,
            texture_variance=0.0,
            edge_density=0.0,
            shape_features={},
            motion_features={},
            appearance_features={},
            detection_confidence=0.0,
            mathematical_confidence=0.0,
            combined_confidence=0.0,
            object_type=object_type,
            feature_quality=0.0,
            extraction_time_ms=0.0
        )

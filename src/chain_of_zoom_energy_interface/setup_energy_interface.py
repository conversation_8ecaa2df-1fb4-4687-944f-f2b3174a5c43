#!/usr/bin/env python3
"""
🚀 Chain-of-Zoom Energy Interface Setup Script
==============================================

Automated setup for the next-generation workplace safety monitoring system.

This script will:
1. Check system requirements
2. Install required dependencies
3. Verify component functionality
4. Run a quick system test

Built with love for worker safety! 💙
"""

import subprocess
import sys
import os
import importlib
from typing import List, Tuple, Dict

class EnergyInterfaceSetup:
    """Setup manager for Chain-of-Zoom Energy Interface"""
    
    def __init__(self):
        self.required_packages = [
            ("opencv-python", "cv2"),
            ("numpy", "numpy"),
            ("torch", "torch"),
            ("torchvision", "torchvision"),
            ("ultralytics", "ultralytics"),
            ("scipy", "scipy"),
            ("scikit-image", "skimage"),
            ("openai-whisper", "whisper"),
            ("pyaudio", "pyaudio"),
        ]
        
        self.optional_packages = [
            ("matplotlib", "matplotlib"),
            ("seaborn", "seaborn"),
            ("pandas", "pandas"),
        ]
        
        self.system_info = {}
        
    def run_setup(self):
        """Run complete setup process"""
        print("🚀 Chain-of-Zoom Energy Interface Setup")
        print("=" * 50)
        print("Setting up next-generation workplace safety monitoring...")
        print("Built with love for worker protection! 💙\n")
        
        # 1. Check system requirements
        print("📋 Checking system requirements...")
        self._check_system_requirements()
        
        # 2. Check Python version
        print("\n🐍 Checking Python version...")
        self._check_python_version()
        
        # 3. Check existing packages
        print("\n📦 Checking existing packages...")
        missing_packages = self._check_packages()
        
        # 4. Install missing packages
        if missing_packages:
            print(f"\n⬇️ Installing {len(missing_packages)} missing packages...")
            self._install_packages(missing_packages)
        else:
            print("\n✅ All required packages already installed!")
        
        # 5. Verify installations
        print("\n🔍 Verifying installations...")
        self._verify_installations()
        
        # 6. Check GPU availability
        print("\n🖥️ Checking GPU availability...")
        self._check_gpu_support()
        
        # 7. Test components
        print("\n🧪 Testing system components...")
        self._test_components()
        
        # 8. Setup complete
        print("\n🎉 Setup Complete!")
        self._print_next_steps()
    
    def _check_system_requirements(self):
        """Check basic system requirements"""
        import platform
        
        self.system_info['platform'] = platform.system()
        self.system_info['architecture'] = platform.machine()
        self.system_info['python_version'] = platform.python_version()
        
        print(f"   Platform: {self.system_info['platform']}")
        print(f"   Architecture: {self.system_info['architecture']}")
        print(f"   Python: {self.system_info['python_version']}")
        
        # Check for macOS Apple Silicon
        if self.system_info['platform'] == 'Darwin' and self.system_info['architecture'] == 'arm64':
            print("   🍎 Apple Silicon detected - MPS acceleration available")
            self.system_info['apple_silicon'] = True
        else:
            self.system_info['apple_silicon'] = False
    
    def _check_python_version(self):
        """Check Python version compatibility"""
        version = sys.version_info
        
        if version.major != 3:
            print("❌ Python 3 required")
            sys.exit(1)
        
        if version.minor < 8:
            print("⚠️ Python 3.8+ recommended for best compatibility")
        else:
            print(f"✅ Python {version.major}.{version.minor} is compatible")
    
    def _check_packages(self) -> List[str]:
        """Check which packages are missing"""
        missing = []
        
        for package_name, import_name in self.required_packages:
            try:
                importlib.import_module(import_name)
                print(f"   ✅ {package_name}")
            except ImportError:
                print(f"   ❌ {package_name} (missing)")
                missing.append(package_name)
        
        return missing
    
    def _install_packages(self, packages: List[str]):
        """Install missing packages using uv or pip"""
        # Try uv first (preferred)
        uv_available = self._check_uv_available()
        
        for package in packages:
            if uv_available:
                cmd = ["uv", "pip", "install", package]
            else:
                cmd = [sys.executable, "-m", "pip", "install", package]
            
            print(f"   Installing {package}...")
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                print(f"   ✅ {package} installed successfully")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ Failed to install {package}: {e}")
                if package in ["pyaudio", "openai-whisper"]:
                    print(f"      Note: {package} is optional for voice control")
                else:
                    print(f"      This may cause issues with the system")
    
    def _check_uv_available(self) -> bool:
        """Check if uv package manager is available"""
        try:
            subprocess.run(["uv", "--version"], capture_output=True, check=True)
            print("   📦 Using uv package manager")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("   📦 Using pip package manager")
            return False
    
    def _verify_installations(self):
        """Verify that all packages can be imported"""
        all_good = True
        
        for package_name, import_name in self.required_packages:
            try:
                importlib.import_module(import_name)
                print(f"   ✅ {import_name} imports successfully")
            except ImportError as e:
                print(f"   ❌ {import_name} import failed: {e}")
                all_good = False
        
        if all_good:
            print("   🎉 All packages verified!")
        else:
            print("   ⚠️ Some packages have import issues")
    
    def _check_gpu_support(self):
        """Check GPU acceleration availability"""
        try:
            import torch
            
            # Check CUDA
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0)
                print(f"   🚀 CUDA available: {gpu_count} GPU(s)")
                print(f"      Primary GPU: {gpu_name}")
                self.system_info['cuda_available'] = True
            else:
                print("   ❌ CUDA not available")
                self.system_info['cuda_available'] = False
            
            # Check MPS (Apple Silicon)
            if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                print("   🍎 MPS (Apple Silicon) acceleration available")
                self.system_info['mps_available'] = True
            else:
                self.system_info['mps_available'] = False
            
            if not self.system_info.get('cuda_available', False) and not self.system_info.get('mps_available', False):
                print("   💻 CPU-only mode (slower performance)")
                
        except ImportError:
            print("   ❌ PyTorch not available for GPU check")
    
    def _test_components(self):
        """Test basic functionality of each component"""
        try:
            # Test YOLO
            print("   🤖 Testing YOLO model...")
            from ultralytics import YOLO
            model = YOLO('yolov8n.pt')
            print("      ✅ YOLO model loads successfully")
            
            # Test OpenCV
            print("   📷 Testing OpenCV...")
            import cv2
            print(f"      ✅ OpenCV {cv2.__version__} available")
            
            # Test advanced components
            print("   🔬 Testing advanced mathematical extractor...")
            sys.path.append(os.path.dirname(__file__))
            from advanced_mathematical_extractor import AdvancedMathematicalExtractor
            extractor = AdvancedMathematicalExtractor()
            print("      ✅ Advanced feature extractor initialized")
            
            # Test voice components (optional)
            try:
                print("   🎤 Testing voice control components...")
                import whisper
                import pyaudio
                print("      ✅ Voice control components available")
                self.system_info['voice_available'] = True
            except ImportError:
                print("      ⚠️ Voice control components not available (optional)")
                self.system_info['voice_available'] = False
            
            print("   🎉 All component tests passed!")
            
        except Exception as e:
            print(f"   ❌ Component test failed: {e}")
            print("      Some features may not work correctly")
    
    def _print_next_steps(self):
        """Print next steps for the user"""
        print("\n🎯 Next Steps:")
        print("=" * 30)
        
        print("\n1. 🎮 Run the complete demo:")
        print("   python complete_safety_system_demo.py --video videos/test4.mp4")
        
        print("\n2. 🔧 Basic integration:")
        print("   from advanced_mathematical_extractor import AdvancedMathematicalExtractor")
        print("   from automatic_safety_zoom import AutomaticSafetyZoomSystem")
        
        if self.system_info.get('voice_available', False):
            print("\n3. 🎤 Voice control available:")
            print("   Try voice commands: 'track 5', 'zoom in', 'detect helmets'")
        else:
            print("\n3. 🎤 Voice control setup:")
            print("   Install: pip install openai-whisper pyaudio")
        
        if self.system_info.get('cuda_available', False):
            print("\n4. 🚀 GPU acceleration enabled (CUDA)")
        elif self.system_info.get('mps_available', False):
            print("\n4. 🍎 GPU acceleration enabled (Apple Silicon MPS)")
        else:
            print("\n4. 💻 CPU-only mode (consider GPU for better performance)")
        
        print("\n📚 Documentation:")
        print("   See README.md for complete usage guide")
        
        print("\n💙 Built with love for worker safety!")
        print("   Ready to save lives and prevent workplace accidents!")


def main():
    """Main setup function"""
    try:
        setup = EnergyInterfaceSetup()
        setup.run_setup()
        return 0
    except KeyboardInterrupt:
        print("\n\n🛑 Setup interrupted by user")
        return 1
    except Exception as e:
        print(f"\n\n❌ Setup failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())

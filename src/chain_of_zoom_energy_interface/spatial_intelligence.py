"""
🌍 Spatial Intelligence Framework
=================================

Implements 3D understanding from 2D projections using energy scaling
for distance estimation and crowd analysis in workplace safety monitoring.

Key Features:
• Distance estimation using known person height as reference
• Real-world sizing: Converting pixel measurements to meters
• Crowd analysis: Social distancing monitoring
• Ground plane mapping: Foot position to distance calculations
• Energy-based spatial relationships
• Multi-worker spatial interaction analysis

Mathematical Foundation:
- Uses your energy scaling relationships (E ∝ zoom²) plus perspective geometry
- Distance estimation: distance = reference_height / tan(angular_height)
- Real-world measurements: real_width = distance * tan((pixel_width / frame_width) * camera_fov)
- Crowd density analysis: density = num_workers / (area_pixels * pixel_to_meter_ratio²)

Built with love for worker safety! 💙
"""

import numpy as np
import cv2
import time
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass
from collections import defaultdict
import math

@dataclass
class SpatialWorkerInfo:
    """Spatial information for a single worker"""
    track_id: int
    pixel_position: Tuple[int, int]  # (x, y) in pixels
    estimated_distance: float       # Distance from camera in meters
    real_world_position: Tuple[float, float]  # (x, y) in meters
    height_pixels: int              # Height in pixels
    estimated_height_meters: float  # Estimated real height
    ground_position: Tuple[float, float]  # Foot position on ground plane
    confidence: float               # Confidence in spatial estimation

@dataclass
class SpatialRelationship:
    """Relationship between two workers"""
    worker1_id: int
    worker2_id: int
    distance_meters: float
    angle_degrees: float
    interaction_type: str  # 'close', 'normal', 'distant'
    safety_concern: bool   # True if too close for safety

@dataclass
class CrowdAnalysis:
    """Overall crowd analysis"""
    total_workers: int
    density_per_sqm: float
    average_distance: float
    closest_pair_distance: float
    safety_violations: int
    crowd_risk_level: str  # 'low', 'medium', 'high'

@dataclass
class CalibrationPoint:
    """Manual calibration reference point"""
    pixel_position: Tuple[float, float]
    real_world_distance: float
    real_world_height: float
    description: str
    timestamp: float

@dataclass
class CameraCalibration:
    """Enhanced camera calibration parameters"""
    height: float
    fov_degrees: float
    tilt_angle_degrees: float
    focal_length_pixels: float
    principal_point: Tuple[float, float]
    distortion_coefficients: List[float]
    calibration_quality: float  # 0.0 to 1.0
    last_calibrated: float

class SpatialIntelligence:
    """
    🌍 Enhanced Spatial Intelligence Framework

    Converts 2D tracking information into 3D spatial understanding
    using energy scaling relationships and perspective geometry.

    NEW FEATURES:
    - Manual calibration interface
    - Real-world measurement validation
    - Distance override controls
    - Calibration quality assessment
    """

    def __init__(self,
                 camera_height: float = 3.0,      # Camera height in meters
                 camera_fov: float = 60.0,        # Field of view in degrees
                 reference_person_height: float = 1.7,  # Average person height in meters
                 safe_distance: float = 2.0,      # Safe distance between workers in meters
                 frame_width: int = 1920,         # Frame width in pixels
                 frame_height: int = 1080,        # Frame height in pixels
                 enable_manual_calibration: bool = True):  # Enable calibration interface
        """
        Initialize enhanced spatial intelligence system

        Args:
            camera_height: Height of camera above ground in meters
            camera_fov: Camera field of view in degrees
            reference_person_height: Reference person height for scaling
            safe_distance: Minimum safe distance between workers
            frame_width: Video frame width in pixels
            frame_height: Video frame height in pixels
            enable_manual_calibration: Enable manual calibration interface
        """
        self.camera_height = camera_height
        self.camera_fov = math.radians(camera_fov)
        self.reference_person_height = reference_person_height
        self.safe_distance = safe_distance
        self.frame_width = frame_width
        self.frame_height = frame_height
        self.enable_manual_calibration = enable_manual_calibration

        # Enhanced calibration system
        self.camera_calibration = CameraCalibration(
            height=camera_height,
            fov_degrees=camera_fov,
            tilt_angle_degrees=15.0,  # Initial estimate
            focal_length_pixels=(frame_width / 2) / math.tan(self.camera_fov / 2),
            principal_point=(frame_width / 2, frame_height / 2),
            distortion_coefficients=[0.0, 0.0, 0.0, 0.0],  # No distortion initially
            calibration_quality=0.3,  # Low initial quality
            last_calibrated=time.time()
        )

        # Derived parameters
        self.focal_length_pixels = self.camera_calibration.focal_length_pixels
        self.ground_plane_y = int(frame_height * 0.8)  # Assume ground is at 80% of frame height

        # Enhanced calibration parameters
        self.pixel_to_meter_ratio = 0.01  # Initial estimate, updated dynamically
        self.camera_tilt_angle = math.radians(self.camera_calibration.tilt_angle_degrees)

        # Manual calibration system
        self.calibration_points = []  # List of CalibrationPoint
        self.distance_overrides = {}  # track_id -> manual_distance
        self.calibration_mode = False
        self.pending_calibration_point = None

        # Tracking state
        self.worker_spatial_history = {}  # track_id -> list of spatial info
        self.spatial_relationships = []
        self.crowd_analysis_history = []

        # Validation metrics
        self.distance_validation_history = []
        self.calibration_accuracy_metrics = {
            'mean_error': 0.0,
            'std_error': 0.0,
            'max_error': 0.0,
            'validation_count': 0
        }

        print("🌍 Enhanced Spatial Intelligence Framework initialized")
        print(f"   Camera height: {camera_height}m")
        print(f"   Camera FOV: {math.degrees(self.camera_fov):.1f}°")
        print(f"   Reference person height: {reference_person_height}m")
        print(f"   Safe distance: {safe_distance}m")
        print(f"   Manual calibration: {'Enabled' if enable_manual_calibration else 'Disabled'}")
        print(f"   Calibration quality: {self.camera_calibration.calibration_quality:.1%}")
    
    def analyze_spatial_relationships(self, features: List, frame_shape: Tuple[int, int], 
                                    zoom_level: float) -> Dict[str, Any]:
        """
        🎯 Main spatial analysis function
        
        Analyzes spatial relationships between workers and estimates
        real-world positions and distances.
        
        Args:
            features: List of AdvancedWorkerFeatures
            frame_shape: (height, width) of current frame
            zoom_level: Current zoom level
            
        Returns:
            Comprehensive spatial analysis results
        """
        # Update frame dimensions if changed
        self.frame_height, self.frame_width = frame_shape
        
        # 1. Convert features to spatial information
        spatial_workers = self._extract_spatial_info(features, zoom_level)
        
        # 2. Estimate real-world positions
        for worker in spatial_workers:
            self._estimate_real_world_position(worker, zoom_level)
        
        # 3. Analyze relationships between workers
        relationships = self._analyze_worker_relationships(spatial_workers)
        
        # 4. Perform crowd analysis
        crowd_analysis = self._analyze_crowd_dynamics(spatial_workers, relationships)
        
        # 5. Update tracking history
        self._update_spatial_history(spatial_workers)
        
        # 6. Detect spatial safety violations
        safety_violations = self._detect_spatial_safety_violations(relationships, crowd_analysis)
        
        return {
            'spatial_workers': spatial_workers,
            'relationships': relationships,
            'crowd_analysis': crowd_analysis,
            'safety_violations': safety_violations,
            'frame_analysis': {
                'zoom_level': zoom_level,
                'frame_shape': frame_shape,
                'pixel_to_meter_ratio': self.pixel_to_meter_ratio,
                'estimated_coverage_area': self._estimate_coverage_area(zoom_level)
            }
        }
    
    def _extract_spatial_info(self, features: List, zoom_level: float) -> List[SpatialWorkerInfo]:
        """Extract spatial information from worker features"""
        spatial_workers = []
        
        for feature in features:
            # Extract pixel position (center of bounding box)
            bbox = feature.bbox
            pixel_x = (bbox[0] + bbox[2]) // 2
            pixel_y = (bbox[1] + bbox[3]) // 2
            height_pixels = bbox[3] - bbox[1]
            
            # Create spatial info
            spatial_info = SpatialWorkerInfo(
                track_id=feature.track_id,
                pixel_position=(pixel_x, pixel_y),
                estimated_distance=0.0,  # Will be calculated
                real_world_position=(0.0, 0.0),  # Will be calculated
                height_pixels=height_pixels,
                estimated_height_meters=0.0,  # Will be calculated
                ground_position=(0.0, 0.0),  # Will be calculated
                confidence=feature.feature_quality
            )
            
            spatial_workers.append(spatial_info)
        
        return spatial_workers
    
    def _estimate_real_world_position(self, worker: SpatialWorkerInfo, zoom_level: float):
        """
        🎯 Enhanced real-world position estimation with calibration validation

        Uses multiple methods for robust distance estimation:
        1. Perspective geometry with calibration correction
        2. Manual calibration point interpolation
        3. Distance override if manually set
        4. Validation against known reference points
        """
        try:
            # Check for manual distance override first
            if worker.track_id in self.distance_overrides:
                estimated_distance = self.distance_overrides[worker.track_id]
                print(f"🎯 Using manual distance override for track {worker.track_id}: {estimated_distance:.1f}m")
            else:
                # Use enhanced distance estimation
                estimated_distance = self._calculate_enhanced_distance(worker, zoom_level)

            # Calculate angular position with calibration correction
            pixel_x, pixel_y = worker.pixel_position

            # Apply principal point correction
            corrected_x = pixel_x - self.camera_calibration.principal_point[0]
            corrected_y = pixel_y - self.camera_calibration.principal_point[1]

            # Convert pixel coordinates to angular coordinates
            angle_x = math.atan(corrected_x / self.focal_length_pixels)
            angle_y = math.atan(corrected_y / self.focal_length_pixels)

            # Calculate real-world position with enhanced geometry
            real_x = estimated_distance * math.tan(angle_x)
            real_z = estimated_distance  # Distance from camera

            # Enhanced ground position calculation
            ground_position = self._calculate_ground_position(
                estimated_distance, angle_x, angle_y, worker.height_pixels
            )

            # Update worker spatial info
            worker.estimated_distance = estimated_distance
            worker.real_world_position = (real_x, real_z)
            worker.ground_position = ground_position
            worker.estimated_height_meters = self._estimate_actual_height(worker, estimated_distance)

            # Update calibration based on this measurement
            self._update_calibration_from_measurement(worker, estimated_distance)

            # Validate distance if we have calibration points
            if self.calibration_points:
                self._validate_distance_estimate(worker, estimated_distance)

        except Exception as e:
            print(f"⚠️ Error in position estimation for track {worker.track_id}: {e}")
            # Enhanced fallback with better estimates
            fallback_distance = self._get_fallback_distance(worker)
            worker.estimated_distance = fallback_distance
            worker.real_world_position = (0.0, fallback_distance)
            worker.ground_position = (0.0, fallback_distance)
            worker.estimated_height_meters = self.reference_person_height

    def _calculate_enhanced_distance(self, worker: SpatialWorkerInfo, zoom_level: float) -> float:
        """
        Calculate distance using multiple methods and choose the most reliable
        """
        distances = []

        # Method 1: Traditional perspective geometry
        effective_height = worker.height_pixels / zoom_level
        if effective_height > 5:  # Only if reasonably sized
            distance_perspective = (self.reference_person_height * self.focal_length_pixels) / effective_height
            distances.append(('perspective', distance_perspective, 0.7))

        # Method 2: Calibration point interpolation
        if len(self.calibration_points) >= 2:
            distance_calibrated = self._interpolate_from_calibration_points(worker.pixel_position)
            if distance_calibrated > 0:
                distances.append(('calibrated', distance_calibrated, 0.9))

        # Method 3: Ground plane intersection (if camera tilt is known)
        if self.camera_calibration.calibration_quality > 0.5:
            distance_ground = self._calculate_ground_plane_distance(worker)
            if distance_ground > 0:
                distances.append(('ground_plane', distance_ground, 0.8))

        # Choose best estimate based on confidence weights
        if distances:
            # Weighted average of available methods
            total_weight = sum(weight for _, _, weight in distances)
            weighted_distance = sum(dist * weight for _, dist, weight in distances) / total_weight

            # Clamp to reasonable range with better bounds
            final_distance = max(0.5, min(weighted_distance, 100.0))

            # Log the estimation method used
            best_method = max(distances, key=lambda x: x[2])[0]
            print(f"📏 Distance for track {worker.track_id}: {final_distance:.1f}m (method: {best_method})")

            return final_distance
        else:
            # Fallback to simple estimation
            return max(2.0, min(20.0, 10.0))  # Conservative fallback

    def _calculate_ground_position(self, distance: float, angle_x: float, angle_y: float,
                                 height_pixels: float) -> Tuple[float, float]:
        """
        Calculate ground position using enhanced camera geometry
        """
        # Account for camera height and tilt
        camera_tilt = math.radians(self.camera_calibration.tilt_angle_degrees)

        # Calculate the actual ground intersection
        # This is more accurate than the simple approximation
        ground_distance = distance * math.cos(camera_tilt)

        # Calculate lateral position
        ground_x = ground_distance * math.tan(angle_x)

        # Calculate forward position (accounting for camera height)
        height_angle = math.atan(self.camera_height / distance)
        corrected_angle = angle_y + height_angle
        ground_y = distance * math.cos(corrected_angle)

        return (ground_x, ground_y)
    
    def _analyze_worker_relationships(self, spatial_workers: List[SpatialWorkerInfo]) -> List[SpatialRelationship]:
        """Analyze spatial relationships between workers"""
        relationships = []
        
        for i, worker1 in enumerate(spatial_workers):
            for j, worker2 in enumerate(spatial_workers[i+1:], i+1):
                # Calculate distance between workers
                pos1 = worker1.ground_position
                pos2 = worker2.ground_position
                
                distance = math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)
                
                # Calculate angle between workers
                angle = math.degrees(math.atan2(pos2[1] - pos1[1], pos2[0] - pos1[0]))
                
                # Classify interaction type
                if distance < self.safe_distance:
                    interaction_type = 'close'
                    safety_concern = True
                elif distance < self.safe_distance * 2:
                    interaction_type = 'normal'
                    safety_concern = False
                else:
                    interaction_type = 'distant'
                    safety_concern = False
                
                relationship = SpatialRelationship(
                    worker1_id=worker1.track_id,
                    worker2_id=worker2.track_id,
                    distance_meters=distance,
                    angle_degrees=angle,
                    interaction_type=interaction_type,
                    safety_concern=safety_concern
                )
                
                relationships.append(relationship)
        
        return relationships
    
    def _analyze_crowd_dynamics(self, spatial_workers: List[SpatialWorkerInfo], 
                              relationships: List[SpatialRelationship]) -> CrowdAnalysis:
        """Analyze overall crowd dynamics and density"""
        if not spatial_workers:
            return CrowdAnalysis(0, 0.0, 0.0, 0.0, 0, 'low')
        
        # Calculate coverage area (simplified)
        coverage_area = self._estimate_coverage_area(1.0)  # Base zoom level
        
        # Calculate density
        density = len(spatial_workers) / max(coverage_area, 1.0)
        
        # Calculate average distance between workers
        distances = [rel.distance_meters for rel in relationships]
        average_distance = np.mean(distances) if distances else float('inf')
        closest_distance = min(distances) if distances else float('inf')
        
        # Count safety violations
        safety_violations = sum(1 for rel in relationships if rel.safety_concern)
        
        # Determine crowd risk level
        if density > 0.5 or safety_violations > 2:
            risk_level = 'high'
        elif density > 0.2 or safety_violations > 0:
            risk_level = 'medium'
        else:
            risk_level = 'low'
        
        return CrowdAnalysis(
            total_workers=len(spatial_workers),
            density_per_sqm=density,
            average_distance=average_distance,
            closest_pair_distance=closest_distance,
            safety_violations=safety_violations,
            crowd_risk_level=risk_level
        )
    
    def _estimate_coverage_area(self, zoom_level: float) -> float:
        """Estimate the area covered by the camera view in square meters"""
        # Simplified calculation based on camera parameters
        # In production, this would be more sophisticated
        base_area = 100.0  # Base coverage area in square meters
        return base_area / (zoom_level ** 2)
    
    def _update_spatial_history(self, spatial_workers: List[SpatialWorkerInfo]):
        """Update spatial tracking history for temporal analysis"""
        current_time = time.time()
        
        for worker in spatial_workers:
            track_id = worker.track_id
            
            if track_id not in self.worker_spatial_history:
                self.worker_spatial_history[track_id] = []
            
            # Add current spatial info with timestamp
            history_entry = {
                'timestamp': current_time,
                'spatial_info': worker,
                'velocity': self._calculate_velocity(track_id, worker)
            }
            
            self.worker_spatial_history[track_id].append(history_entry)
            
            # Keep only recent history (last 30 seconds)
            cutoff_time = current_time - 30.0
            self.worker_spatial_history[track_id] = [
                entry for entry in self.worker_spatial_history[track_id]
                if entry['timestamp'] > cutoff_time
            ]
    
    def _calculate_velocity(self, track_id: int, current_worker: SpatialWorkerInfo) -> Tuple[float, float]:
        """Calculate worker velocity based on position history"""
        if track_id not in self.worker_spatial_history or len(self.worker_spatial_history[track_id]) < 2:
            return (0.0, 0.0)
        
        previous_entry = self.worker_spatial_history[track_id][-1]
        previous_pos = previous_entry['spatial_info'].ground_position
        current_pos = current_worker.ground_position
        
        time_diff = time.time() - previous_entry['timestamp']
        if time_diff <= 0:
            return (0.0, 0.0)
        
        velocity_x = (current_pos[0] - previous_pos[0]) / time_diff
        velocity_y = (current_pos[1] - previous_pos[1]) / time_diff
        
        return (velocity_x, velocity_y)
    
    def _detect_spatial_safety_violations(self, relationships: List[SpatialRelationship], 
                                        crowd_analysis: CrowdAnalysis) -> List[Dict]:
        """Detect spatial safety violations"""
        violations = []
        
        # Social distancing violations
        for rel in relationships:
            if rel.safety_concern:
                violations.append({
                    'type': 'social_distancing',
                    'severity': 'medium',
                    'description': f"Workers {rel.worker1_id} and {rel.worker2_id} too close ({rel.distance_meters:.1f}m)",
                    'workers': [rel.worker1_id, rel.worker2_id],
                    'distance': rel.distance_meters
                })
        
        # Crowd density violations
        if crowd_analysis.crowd_risk_level == 'high':
            violations.append({
                'type': 'crowd_density',
                'severity': 'high',
                'description': f"High crowd density: {crowd_analysis.density_per_sqm:.2f} workers/m²",
                'workers': [],  # Affects all workers
                'density': crowd_analysis.density_per_sqm
            })
        
        return violations

    # ==================== CALIBRATION HELPER METHODS ====================

    def _interpolate_from_calibration_points(self, pixel_position: Tuple[float, float]) -> float:
        """Interpolate distance from calibration points"""
        if len(self.calibration_points) < 2:
            return 0.0

        # Find the two closest calibration points
        distances_to_points = []
        for point in self.calibration_points:
            pixel_dist = math.sqrt(
                (pixel_position[0] - point.pixel_position[0])**2 +
                (pixel_position[1] - point.pixel_position[1])**2
            )
            distances_to_points.append((pixel_dist, point))

        # Sort by distance and take the two closest
        distances_to_points.sort(key=lambda x: x[0])
        closest_points = distances_to_points[:2]

        # Weighted interpolation based on pixel distance
        total_weight = 0
        weighted_distance = 0

        for pixel_dist, point in closest_points:
            weight = 1.0 / (pixel_dist + 1.0)  # Avoid division by zero
            weighted_distance += point.real_world_distance * weight
            total_weight += weight

        return weighted_distance / total_weight if total_weight > 0 else 0.0

    def _calculate_ground_plane_distance(self, worker: SpatialWorkerInfo) -> float:
        """Calculate distance using ground plane intersection"""
        try:
            pixel_x, pixel_y = worker.pixel_position

            # Calculate angle from camera center
            angle_y = math.atan((pixel_y - self.frame_height/2) / self.focal_length_pixels)

            # Account for camera tilt
            total_angle = angle_y + math.radians(self.camera_calibration.tilt_angle_degrees)

            # Calculate distance to ground plane intersection
            if abs(total_angle) > 0.001:  # Avoid division by zero
                distance = self.camera_height / math.tan(abs(total_angle))
                return max(0.5, min(distance, 100.0))  # Reasonable bounds

        except Exception:
            pass

        return 0.0

    def _estimate_actual_height(self, worker: SpatialWorkerInfo, distance: float) -> float:
        """Estimate actual height of the worker based on distance"""
        try:
            # Use perspective geometry to estimate real height
            angular_height = worker.height_pixels / self.focal_length_pixels
            estimated_height = distance * angular_height

            # Clamp to reasonable human height range
            return max(1.2, min(estimated_height, 2.2))
        except Exception:
            return self.reference_person_height

    def _update_calibration_from_measurement(self, worker: SpatialWorkerInfo, distance: float):
        """Update calibration parameters based on new measurement"""
        if worker.height_pixels > 20:  # Only use if reasonably sized
            # Update pixel-to-meter ratio
            pixel_ratio = worker.height_pixels / self.reference_person_height
            distance_ratio = distance / 10.0  # Normalize to 10m reference

            # Smooth update
            new_ratio = pixel_ratio / distance_ratio
            self.pixel_to_meter_ratio = 0.95 * self.pixel_to_meter_ratio + 0.05 * new_ratio

    def _validate_distance_estimate(self, worker: SpatialWorkerInfo, estimated_distance: float):
        """Validate distance estimate against calibration points"""
        if not self.calibration_points:
            return

        # Find nearest calibration point
        nearest_point = min(
            self.calibration_points,
            key=lambda p: math.sqrt(
                (worker.pixel_position[0] - p.pixel_position[0])**2 +
                (worker.pixel_position[1] - p.pixel_position[1])**2
            )
        )

        # Calculate expected distance based on calibration
        expected_distance = self._interpolate_from_calibration_points(worker.pixel_position)

        if expected_distance > 0:
            error = abs(estimated_distance - expected_distance)
            if error > 1.0:  # Significant error
                print(f"⚠️ Distance validation warning for track {worker.track_id}:")
                print(f"   Estimated: {estimated_distance:.1f}m")
                print(f"   Expected: {expected_distance:.1f}m")
                print(f"   Error: {error:.1f}m")

    def _get_fallback_distance(self, worker: SpatialWorkerInfo) -> float:
        """Get a reasonable fallback distance estimate"""
        # Use position in frame to estimate distance
        pixel_y = worker.pixel_position[1]

        # Objects lower in frame are typically closer
        relative_y = pixel_y / self.frame_height

        # Simple heuristic: closer to bottom = closer to camera
        if relative_y > 0.8:  # Bottom of frame
            return 5.0
        elif relative_y > 0.6:  # Middle-bottom
            return 10.0
        elif relative_y > 0.4:  # Middle
            return 15.0
        else:  # Top of frame
            return 25.0

    def _recalculate_calibration(self):
        """Recalculate calibration parameters based on calibration points"""
        if len(self.calibration_points) < 2:
            return

        # Calculate calibration quality based on number and distribution of points
        quality = min(1.0, len(self.calibration_points) / 5.0)  # Max quality with 5+ points

        # Adjust for point distribution (better if spread across frame)
        if len(self.calibration_points) >= 2:
            positions = [p.pixel_position for p in self.calibration_points]
            x_spread = max(p[0] for p in positions) - min(p[0] for p in positions)
            y_spread = max(p[1] for p in positions) - min(p[1] for p in positions)

            spread_factor = min(1.0, (x_spread + y_spread) / (self.frame_width + self.frame_height))
            quality *= (0.5 + 0.5 * spread_factor)  # Bonus for good distribution

        # Update calibration
        self.camera_calibration.calibration_quality = quality
        self.camera_calibration.last_calibrated = time.time()

        print(f"🔧 Calibration updated: {quality:.1%} quality with {len(self.calibration_points)} points")

    def _update_accuracy_metrics(self):
        """Update accuracy metrics based on validation history"""
        if not self.distance_validation_history:
            return

        # Calculate metrics from recent validations (last 20)
        recent_validations = self.distance_validation_history[-20:]
        errors = [v['absolute_error'] for v in recent_validations]

        self.calibration_accuracy_metrics = {
            'mean_error': np.mean(errors),
            'std_error': np.std(errors),
            'max_error': max(errors),
            'validation_count': len(self.distance_validation_history)
        }
    
    # ==================== MANUAL CALIBRATION INTERFACE ====================

    def start_calibration_mode(self):
        """Start manual calibration mode"""
        self.calibration_mode = True
        print("🎯 Calibration mode activated!")
        print("   Click on objects with known distances to calibrate the system")
        print("   Press 'c' to add calibration point, 'q' to quit calibration")

    def add_calibration_point(self, pixel_x: float, pixel_y: float,
                            real_distance: float, real_height: float = None,
                            description: str = "Manual calibration point"):
        """
        Add a manual calibration point

        Args:
            pixel_x, pixel_y: Pixel coordinates of the reference point
            real_distance: Known real-world distance in meters
            real_height: Known real-world height in meters (optional)
            description: Description of the calibration point
        """
        if real_height is None:
            real_height = self.reference_person_height

        calibration_point = CalibrationPoint(
            pixel_position=(pixel_x, pixel_y),
            real_world_distance=real_distance,
            real_world_height=real_height,
            description=description,
            timestamp=time.time()
        )

        self.calibration_points.append(calibration_point)

        # Recalculate calibration parameters
        self._recalculate_calibration()

        print(f"✅ Added calibration point: {description}")
        print(f"   Position: ({pixel_x:.0f}, {pixel_y:.0f})")
        print(f"   Distance: {real_distance:.1f}m")
        print(f"   New calibration quality: {self.camera_calibration.calibration_quality:.1%}")

    def set_distance_override(self, track_id: int, distance: float):
        """
        Manually override distance for a specific track

        Args:
            track_id: Track ID to override
            distance: Manual distance in meters
        """
        self.distance_overrides[track_id] = distance
        print(f"🎯 Distance override set for track {track_id}: {distance:.1f}m")

    def clear_distance_override(self, track_id: int):
        """Clear distance override for a track"""
        if track_id in self.distance_overrides:
            del self.distance_overrides[track_id]
            print(f"🗑️ Distance override cleared for track {track_id}")

    def validate_distance_measurement(self, pixel_position: Tuple[float, float],
                                    known_distance: float) -> Dict[str, float]:
        """
        Validate distance measurement against known reference

        Returns:
            Dictionary with validation results
        """
        # Create temporary worker info for validation
        temp_worker = SpatialWorkerInfo(
            track_id=-1,
            pixel_position=pixel_position,
            height_pixels=100,  # Assume standard height
            width_pixels=50,
            confidence=1.0
        )

        # Calculate estimated distance
        estimated_distance = self._calculate_enhanced_distance(temp_worker, 1.0)

        # Calculate error metrics
        error = abs(estimated_distance - known_distance)
        relative_error = error / known_distance if known_distance > 0 else float('inf')

        # Update validation history
        validation_result = {
            'estimated_distance': estimated_distance,
            'known_distance': known_distance,
            'absolute_error': error,
            'relative_error': relative_error,
            'timestamp': time.time()
        }

        self.distance_validation_history.append(validation_result)

        # Update accuracy metrics
        self._update_accuracy_metrics()

        print(f"📏 Distance validation:")
        print(f"   Estimated: {estimated_distance:.1f}m")
        print(f"   Known: {known_distance:.1f}m")
        print(f"   Error: {error:.1f}m ({relative_error:.1%})")

        return validation_result

    def get_calibration_status(self) -> Dict:
        """Get comprehensive calibration status"""
        return {
            'calibration_quality': self.camera_calibration.calibration_quality,
            'calibration_points': len(self.calibration_points),
            'distance_overrides': len(self.distance_overrides),
            'validation_measurements': len(self.distance_validation_history),
            'accuracy_metrics': self.calibration_accuracy_metrics.copy(),
            'camera_parameters': {
                'height': self.camera_calibration.height,
                'fov_degrees': self.camera_calibration.fov_degrees,
                'tilt_angle': self.camera_calibration.tilt_angle_degrees,
                'focal_length': self.camera_calibration.focal_length_pixels
            },
            'last_calibrated': self.camera_calibration.last_calibrated
        }

    def get_spatial_stats(self) -> Dict:
        """Get enhanced spatial intelligence statistics"""
        return {
            'tracked_workers': len(self.worker_spatial_history),
            'pixel_to_meter_ratio': self.pixel_to_meter_ratio,
            'camera_parameters': {
                'height': self.camera_height,
                'fov_degrees': math.degrees(self.camera_fov),
                'focal_length_pixels': self.focal_length_pixels
            },
            'calibration_quality': self.camera_calibration.calibration_quality,
            'calibration_status': self.get_calibration_status()
        }

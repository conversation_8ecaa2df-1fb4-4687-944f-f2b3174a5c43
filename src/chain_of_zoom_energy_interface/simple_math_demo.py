#!/usr/bin/env python3
"""
🔬 Simple Advanced Mathematical Demo
===================================

Simplified demo focusing on video display with advanced mathematical processing.
No complex GUI - just pure mathematical enhancement visualization!

Built to save lives through mathematical excellence! 💙
"""

import cv2
import numpy as np
import argparse
import time
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# Try to import advanced components, but work without them if needed
try:
    from src.chain_of_zoom_energy_interface.advanced_mathematical_framework import AdvancedMathematicalFramework
    ADVANCED_MATH_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Advanced math not available: {e}")
    ADVANCED_MATH_AVAILABLE = False

try:
    from src.chain_of_zoom_energy_interface.object_adaptive_detector import ObjectAdaptiveDetector
    OBJECT_DETECTOR_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ Object detector not available: {e}")
    OBJECT_DETECTOR_AVAILABLE = False

class SimpleMathDemo:
    """Simple demo focusing on advanced mathematical processing"""
    
    def __init__(self, video_path: str):
        print("🔬 Initializing Simple Advanced Mathematical Demo...")
        print("   Focusing on video display with mathematical enhancement!")
        
        # Video setup
        self.video_path = video_path
        self.cap = cv2.VideoCapture(video_path)
        if not self.cap.isOpened():
            raise ValueError(f"❌ Cannot open video: {video_path}")
        
        # Get video properties
        self.fps = int(self.cap.get(cv2.CAP_PROP_FPS))
        self.frame_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        self.frame_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"📹 Video: {video_path}")
        print(f"   Resolution: {self.frame_width}x{self.frame_height}")
        print(f"   FPS: {self.fps}")
        
        # Initialize advanced mathematical framework
        print("\n🔬 Initializing Advanced Mathematical Framework...")
        if ADVANCED_MATH_AVAILABLE:
            try:
                self.advanced_math = AdvancedMathematicalFramework(
                    sequence_memory_depth=10,
                    modular_level=8,
                    enable_gpu=False  # Keep simple for demo
                )
                print("✅ Advanced Mathematical Framework initialized!")
            except Exception as e:
                print(f"⚠️ Math framework error: {e}")
                self.advanced_math = None
        else:
            print("⚠️ Using basic mathematical processing")
            self.advanced_math = None

        # Initialize object detector
        print("\n🎯 Initializing Object Detector...")
        if OBJECT_DETECTOR_AVAILABLE:
            try:
                self.object_detector = ObjectAdaptiveDetector()
                print("✅ Object detector initialized!")
            except Exception as e:
                print(f"⚠️ Detector error: {e}")
                self.object_detector = None
        else:
            print("⚠️ Using basic object detection")
            self.object_detector = None
        
        # Demo state
        self.frame_count = 0
        self.track_history = {}

        # Basic mathematical processing
        self.basic_math_processor = BasicMathProcessor()

        print("\n🎯 Simple Mathematical Demo Ready!")
        print("   Press 'Q' to quit, 'SPACE' to pause")
    
    def run(self):
        """Run the simple mathematical demo"""
        print("\n🚀 Starting Simple Mathematical Demo...")
        print("   Video should appear in a new window!")
        
        # Create window
        window_name = "Advanced Mathematical Safety Demo"
        cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
        
        # Try to position window
        try:
            cv2.moveWindow(window_name, 50, 50)
        except:
            pass
        
        paused = False
        
        try:
            while True:
                if not paused:
                    ret, frame = self.cap.read()
                    if not ret:
                        # Loop video
                        print("🔄 Looping video...")
                        self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                        ret, frame = self.cap.read()
                        if not ret:
                            print("❌ Cannot read video")
                            break
                    
                    self.frame_count += 1
                
                # Process frame
                processed_frame = self._process_frame(frame.copy())
                
                # Display frame
                cv2.imshow(window_name, processed_frame)
                
                # Handle input
                key = cv2.waitKey(30) & 0xFF
                if key == ord('q') or key == 27:  # 'q' or ESC
                    print("🛑 Demo stopped by user")
                    break
                elif key == ord(' '):  # SPACE
                    paused = not paused
                    print(f"⏸️ {'Paused' if paused else 'Resumed'}")
                
                # Debug output every 60 frames
                if self.frame_count % 60 == 0:
                    print(f"🎬 Frame {self.frame_count} - Mathematical processing active")
                
        except KeyboardInterrupt:
            print("\n🛑 Demo interrupted")
        except Exception as e:
            print(f"\n❌ Demo error: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self._cleanup()
    
    def _process_frame(self, frame: np.ndarray) -> np.ndarray:
        """Process frame with advanced mathematical analysis"""
        try:
            # Add frame counter
            cv2.putText(frame, f"Frame: {self.frame_count}", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
            
            # Add mathematical status
            math_status = "✅ Advanced Math Active" if self.advanced_math else "❌ Math Disabled"
            cv2.putText(frame, math_status, (10, 70), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
            
            # Detect objects if detector available
            if self.object_detector:
                detections = self._detect_objects(frame)
                
                # Draw detections with mathematical enhancement
                for i, detection in enumerate(detections[:5]):  # Limit for performance
                    self._draw_detection_with_math(frame, detection, i)
            
            # Add demo info
            cv2.putText(frame, "Advanced Mathematical Safety Demo", (10, frame.shape[0] - 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
            cv2.putText(frame, "Press Q to quit, SPACE to pause", (10, frame.shape[0] - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (200, 200, 200), 1)
            
            return frame
            
        except Exception as e:
            print(f"⚠️ Frame processing error: {e}")
            return frame
    
    def _detect_objects(self, frame: np.ndarray) -> list:
        """Detect objects in frame using real YOLO detection"""
        try:
            if self.object_detector:
                # Use real object detection
                detections = self.object_detector.detect_and_enhance(frame)

                # Convert to simple detection format
                simple_detections = []
                for i, det in enumerate(detections):
                    # Extract bbox coordinates
                    if hasattr(det, 'boxes') and len(det.boxes) > 0:
                        box = det.boxes[0]  # Get first box
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        conf = float(box.conf[0].cpu().numpy())
                        cls_id = int(box.cls[0].cpu().numpy())

                        # Create simple detection object
                        simple_det = type('Detection', (), {
                            'bbox': [int(x1), int(y1), int(x2), int(y2)],
                            'confidence': conf,
                            'class_name': 'person' if cls_id == 0 else f'class_{cls_id}',
                            'track_id': i + 1
                        })()
                        simple_detections.append(simple_det)

                if simple_detections:
                    return simple_detections

            # Fallback: Try basic YOLO detection
            return self._basic_yolo_detection(frame)

        except Exception as e:
            print(f"⚠️ Detection error: {e}")
            return self._basic_yolo_detection(frame)

    def _basic_yolo_detection(self, frame: np.ndarray) -> list:
        """Basic YOLO detection fallback"""
        try:
            # Try to import and use YOLO directly
            from ultralytics import YOLO

            # Initialize YOLO if not already done
            if not hasattr(self, 'yolo_model'):
                print("🤖 Loading YOLO model for real detection...")
                self.yolo_model = YOLO('yolov8n.pt')  # Lightweight model
                print("✅ YOLO model loaded!")

            # Run detection
            results = self.yolo_model(frame, verbose=False)

            detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for i, box in enumerate(boxes):
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        conf = float(box.conf[0].cpu().numpy())
                        cls_id = int(box.cls[0].cpu().numpy())

                        # Filter for people and high confidence
                        if cls_id == 0 and conf > 0.3:  # Person class with reasonable confidence
                            detection = type('Detection', (), {
                                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                'confidence': conf,
                                'class_name': 'person',
                                'track_id': i + 1
                            })()
                            detections.append(detection)

            return detections

        except Exception as e:
            print(f"⚠️ YOLO detection error: {e}")
            # Return empty list instead of mock data
            return []
    
    def _draw_detection_with_math(self, frame: np.ndarray, detection, index: int):
        """Draw detection with mathematical enhancement info"""
        try:
            bbox = detection.bbox
            x1, y1, x2, y2 = map(int, bbox)
            
            # Draw bounding box
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
            # Extract mathematical features if available
            if self.advanced_math:
                try:
                    # Get track history
                    track_id = getattr(detection, 'track_id', index)
                    history = self.track_history.get(track_id, [])
                    
                    # Extract advanced features
                    math_features = self.advanced_math.extract_advanced_features(
                        frame, detection, 1.0, history
                    )
                    
                    # Update history
                    if track_id not in self.track_history:
                        self.track_history[track_id] = []
                    self.track_history[track_id].append(detection)
                    if len(self.track_history[track_id]) > 10:
                        self.track_history[track_id] = self.track_history[track_id][-10:]
                    
                    # Display mathematical confidence
                    math_conf = math_features.get('mathematical_confidence', 0.0)
                    original_conf = getattr(detection, 'confidence', 0.5)
                    enhanced_conf = min(1.0, original_conf + math_conf * 0.2)

                    # Draw enhanced confidence
                    conf_text = f"Math: {math_conf:.2f} | Enhanced: {enhanced_conf:.2f}"
                    cv2.putText(frame, conf_text, (x1, y1 - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

                    # Draw mathematical features info
                    if 'cscg_features' in math_features:
                        cscg = math_features['cscg_features']
                        seq_conf = cscg.get('sequence_confidence', 0.0)
                        cv2.putText(frame, f"CSCG: {seq_conf:.2f}", (x1, y2 + 20),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)

                    # Show enhancement boost
                    if enhanced_conf > original_conf:
                        boost_text = f"BOOST: +{(enhanced_conf - original_conf):.2f}"
                        cv2.putText(frame, boost_text, (x1, y2 + 40),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)

                        print(f"🔬 Mathematical enhancement: Track {track_id} "
                              f"{original_conf:.2f} → {enhanced_conf:.2f}")

                except Exception as e:
                    print(f"⚠️ Math feature error: {e}")

            else:
                # Use basic mathematical processing
                try:
                    # Extract image patch
                    patch = frame[y1:y2, x1:x2]
                    if patch.size > 0:
                        original_conf = getattr(detection, 'confidence', 0.5)
                        enhanced_conf = self.basic_math_processor.enhance_detection_confidence(
                            original_conf, patch
                        )

                        # Draw basic mathematical enhancement
                        if enhanced_conf > original_conf:
                            boost = enhanced_conf - original_conf
                            boost_text = f"Math Boost: +{boost:.3f}"
                            cv2.putText(frame, boost_text, (x1, y1 - 10),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

                            # Calculate energies for display
                            lap_energy = self.basic_math_processor.calculate_laplacian_energy(patch)
                            grad_energy = self.basic_math_processor.calculate_gradient_energy(patch)

                            energy_text = f"Lap:{lap_energy:.0f} Grad:{grad_energy:.1f}"
                            cv2.putText(frame, energy_text, (x1, y2 + 20),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)

                            print(f"🔬 Basic math enhancement: Track {getattr(detection, 'track_id', index)} "
                                  f"{original_conf:.3f} → {enhanced_conf:.3f}")

                except Exception as e:
                    print(f"⚠️ Basic math error: {e}")
            
            # Basic detection info
            label = f"{getattr(detection, 'class_name', 'object')} {getattr(detection, 'confidence', 0.0):.2f}"
            cv2.putText(frame, label, (x1, y1 - 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
        except Exception as e:
            print(f"⚠️ Drawing error: {e}")
    
    def _cleanup(self):
        """Cleanup resources"""
        print("🧹 Cleaning up...")
        if self.cap:
            self.cap.release()
        cv2.destroyAllWindows()
        print("✅ Cleanup completed")

class BasicMathProcessor:
    """Basic mathematical processing without complex dependencies"""

    def __init__(self):
        print("🔬 Initializing Basic Mathematical Processor...")
        self.frame_history = []

    def calculate_laplacian_energy(self, image_patch: np.ndarray) -> float:
        """Calculate Laplacian energy for object tracking enhancement"""
        try:
            # Convert to grayscale if needed
            if len(image_patch.shape) == 3:
                gray = cv2.cvtColor(image_patch, cv2.COLOR_BGR2GRAY)
            else:
                gray = image_patch

            # Calculate Laplacian
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)

            # Calculate energy (variance of Laplacian)
            energy = np.var(laplacian)

            return float(energy)
        except:
            return 0.0

    def calculate_gradient_energy(self, image_patch: np.ndarray) -> float:
        """Calculate gradient energy"""
        try:
            if len(image_patch.shape) == 3:
                gray = cv2.cvtColor(image_patch, cv2.COLOR_BGR2GRAY)
            else:
                gray = image_patch

            # Calculate gradients
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)

            # Calculate gradient magnitude
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

            # Return mean gradient energy
            return float(np.mean(gradient_magnitude))
        except:
            return 0.0

    def enhance_detection_confidence(self, original_conf: float, image_patch: np.ndarray) -> float:
        """Enhance detection confidence using mathematical features"""
        try:
            # Calculate mathematical features
            laplacian_energy = self.calculate_laplacian_energy(image_patch)
            gradient_energy = self.calculate_gradient_energy(image_patch)

            # Normalize energies (simple approach)
            laplacian_norm = min(1.0, laplacian_energy / 1000.0)  # Rough normalization
            gradient_norm = min(1.0, gradient_energy / 100.0)

            # Calculate mathematical confidence boost
            math_boost = (laplacian_norm + gradient_norm) * 0.1  # Conservative boost

            # Enhanced confidence
            enhanced_conf = min(1.0, original_conf + math_boost)

            return enhanced_conf
        except:
            return original_conf

def main():
    parser = argparse.ArgumentParser(description="Simple Advanced Mathematical Demo")
    parser.add_argument("--video", default="videos/test2.mp4", help="Video file path")
    args = parser.parse_args()

    try:
        demo = SimpleMathDemo(args.video)
        demo.run()
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

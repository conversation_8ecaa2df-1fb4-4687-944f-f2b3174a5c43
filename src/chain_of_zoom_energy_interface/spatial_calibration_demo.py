"""
🎯 Spatial Calibration Demo
==========================

Interactive demo for calibrating the spatial intelligence system to address
<PERSON><PERSON>'s feedback about distance measurement accuracy.

Features:
• Manual calibration point addition
• Real-time distance validation
• Distance override controls
• Calibration quality assessment
• Visual feedback for measurements

Usage:
    python spatial_calibration_demo.py

Controls:
    'c' - Add calibration point (click on object with known distance)
    'o' - Override distance for selected track
    'v' - Validate distance measurement
    'r' - Reset calibration
    'q' - Quit

Built to save lives through accurate spatial monitoring! 💙
"""

import cv2
import numpy as np
import time
import sys
import os
from typing import List, Dict, Any, Optional, Tuple
from ultralytics import YOLO
import tkinter as tk
from tkinter import simpledialog, messagebox

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.chain_of_zoom_energy_interface.spatial_intelligence import SpatialIntelligence
from src.chain_of_zoom_energy_interface.advanced_mathematical_extractor import AdvancedMathematicalExtractor
from src.chain_of_zoom.chain_of_zoom_tracker import ChainOfZoomTracker
from src.core.zoom_tracking_wrapper import ZoomTrackingWrapper

class SpatialCalibrationDemo:
    """
    🎯 Interactive Spatial Calibration Demo
    
    Provides tools to calibrate and validate the spatial intelligence system
    for accurate distance measurements in workplace safety monitoring.
    """
    
    def __init__(self, video_path: str = None):
        """Initialize the calibration demo"""
        self.video_path = video_path or "/Users/<USER>/Desktop/Final-zoom-solution/videos/test4.mp4"
        
        # Initialize components
        print("🎯 Initializing Spatial Calibration Demo...")
        print("   This demo addresses Alankar's feedback about distance accuracy")
        
        # Initialize YOLO model
        print("🤖 Loading YOLO model...")
        self.model = YOLO('yolov8n.pt')
        
        # Initialize tracking system
        print("🔗 Initializing tracking system...")
        base_tracker = None
        self.zoom_wrapper = ZoomTrackingWrapper(base_tracker)
        self.chain_tracker = ChainOfZoomTracker(
            base_tracker=self.zoom_wrapper,
            extreme_zoom_threshold=2.0,
            max_zoom_steps=8,
            debug=False
        )
        
        # Initialize feature extractor
        print("🔬 Initializing feature extractor...")
        self.feature_extractor = AdvancedMathematicalExtractor()
        
        # Initialize enhanced spatial intelligence with calibration
        print("🌍 Initializing Enhanced Spatial Intelligence...")
        self.spatial_intelligence = SpatialIntelligence(
            camera_height=3.0,
            camera_fov=60.0,
            enable_manual_calibration=True
        )
        
        # Demo state
        self.current_zoom = 1.0
        self.selected_track_id = None
        self.calibration_mode = False
        self.pending_calibration = None
        self.mouse_position = (0, 0)
        
        # UI state
        self.show_help = True
        self.show_calibration_info = True
        
        print("✅ Spatial Calibration Demo initialized!")
        print("\n🎯 ADDRESSING ALANKAR'S FEEDBACK:")
        print("   • Enhanced distance calculation with multiple methods")
        print("   • Manual calibration point system")
        print("   • Real-time validation against known distances")
        print("   • Distance override controls for specific tracks")
        print("   • Calibration quality assessment")
        
    def run(self):
        """Run the interactive calibration demo"""
        cap = cv2.VideoCapture(self.video_path)
        
        if not cap.isOpened():
            print(f"❌ Error: Could not open video file: {self.video_path}")
            return
        
        # Set up mouse callback
        cv2.namedWindow('Spatial Calibration Demo', cv2.WINDOW_NORMAL)
        cv2.setMouseCallback('Spatial Calibration Demo', self._mouse_callback)
        
        print("\n🎮 CALIBRATION DEMO CONTROLS:")
        print("   'c' - Add calibration point (click on object with known distance)")
        print("   'o' - Override distance for selected track")
        print("   'v' - Validate distance measurement")
        print("   'r' - Reset calibration")
        print("   'h' - Toggle help display")
        print("   'q' - Quit")
        print("\n🎯 Click on workers and use 'c' to add calibration points!")
        
        frame_count = 0
        fps_start_time = time.time()
        
        while True:
            ret, frame = cap.read()
            if not ret:
                # Loop video
                cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                continue
            
            frame_count += 1
            
            # Process frame
            processed_frame = self._process_frame(frame)
            
            # Calculate FPS
            if frame_count % 30 == 0:
                fps = 30 / (time.time() - fps_start_time)
                fps_start_time = time.time()
                print(f"📊 Processing at {fps:.1f} FPS")
            
            # Display frame
            cv2.imshow('Spatial Calibration Demo', processed_frame)
            
            # Handle keyboard input
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('c'):
                self._start_calibration_point()
            elif key == ord('o'):
                self._override_distance()
            elif key == ord('v'):
                self._validate_distance()
            elif key == ord('r'):
                self._reset_calibration()
            elif key == ord('h'):
                self.show_help = not self.show_help
        
        cap.release()
        cv2.destroyAllWindows()
        
        # Show final calibration report
        self._show_calibration_report()
    
    def _process_frame(self, frame: np.ndarray) -> np.ndarray:
        """Process frame with enhanced spatial analysis"""
        # Get detections and tracks
        results = self.model.track(frame, persist=True, verbose=False, classes=[0])
        
        if results[0].boxes is not None and len(results[0].boxes) > 0:
            # Update chain tracker
            detections = []
            for box in results[0].boxes:
                if box.id is not None:
                    detections.append({
                        'bbox': box.xyxy[0].cpu().numpy(),
                        'track_id': int(box.id.cpu().numpy()),
                        'confidence': float(box.conf.cpu().numpy())
                    })
            
            tracks = self.chain_tracker.update(frame, detections, self.current_zoom)
            
            # Extract features for spatial analysis
            features = []
            for track in tracks:
                feature = self.feature_extractor.extract_comprehensive_features(
                    frame, track, self.current_zoom, None
                )
                features.append(feature)
            
            # Perform spatial analysis
            spatial_results = self.spatial_intelligence.analyze_spatial_relationships(
                features, frame.shape[:2], self.current_zoom
            )
            
            # Draw enhanced visualization
            frame = self._draw_enhanced_visualization(frame, spatial_results, tracks)
        
        # Draw UI overlays
        frame = self._draw_ui_overlays(frame)
        
        return frame

    def _draw_enhanced_visualization(self, frame: np.ndarray, spatial_results: Dict, tracks: List) -> np.ndarray:
        """Draw enhanced visualization with calibration feedback"""
        # Draw tracks with distance information
        for i, track in enumerate(tracks):
            bbox = track['bbox']
            track_id = track['track_id']

            # Get spatial information
            spatial_workers = spatial_results.get('spatial_workers', [])
            worker_info = None
            for worker in spatial_workers:
                if worker.track_id == track_id:
                    worker_info = worker
                    break

            # Draw bounding box
            color = (0, 255, 0) if track_id != self.selected_track_id else (0, 255, 255)
            cv2.rectangle(frame, (int(bbox[0]), int(bbox[1])), (int(bbox[2]), int(bbox[3])), color, 2)

            # Draw track ID and distance
            label = f"ID: {track_id}"
            if worker_info:
                distance = worker_info.estimated_distance
                label += f" | {distance:.1f}m"

                # Color code based on distance accuracy
                if track_id in self.spatial_intelligence.distance_overrides:
                    label += " (MANUAL)"
                    color = (255, 0, 255)  # Magenta for manual override
                elif self.spatial_intelligence.camera_calibration.calibration_quality > 0.7:
                    color = (0, 255, 0)  # Green for good calibration
                elif self.spatial_intelligence.camera_calibration.calibration_quality > 0.4:
                    color = (0, 255, 255)  # Yellow for medium calibration
                else:
                    color = (0, 0, 255)  # Red for poor calibration

            cv2.putText(frame, label, (int(bbox[0]), int(bbox[1]) - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

        # Draw spatial relationships
        relationships = spatial_results.get('relationships', [])
        for rel in relationships:
            if rel.safety_concern:
                # Find worker positions
                worker1_pos = None
                worker2_pos = None
                for worker in spatial_results.get('spatial_workers', []):
                    if worker.track_id == rel.worker1_id:
                        worker1_pos = worker.pixel_position
                    elif worker.track_id == rel.worker2_id:
                        worker2_pos = worker.pixel_position

                if worker1_pos and worker2_pos:
                    # Draw safety violation line
                    cv2.line(frame,
                            (int(worker1_pos[0]), int(worker1_pos[1])),
                            (int(worker2_pos[0]), int(worker2_pos[1])),
                            (0, 0, 255), 3)

                    # Draw distance text
                    mid_x = int((worker1_pos[0] + worker2_pos[0]) / 2)
                    mid_y = int((worker1_pos[1] + worker2_pos[1]) / 2)
                    cv2.putText(frame, f"{rel.distance_meters:.1f}m",
                               (mid_x, mid_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

        # Draw calibration points
        for point in self.spatial_intelligence.calibration_points:
            pos = (int(point.pixel_position[0]), int(point.pixel_position[1]))
            cv2.circle(frame, pos, 8, (255, 255, 0), -1)  # Yellow circle
            cv2.putText(frame, f"{point.real_world_distance:.1f}m",
                       (pos[0] + 10, pos[1]), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)

        # Draw mouse crosshair if in calibration mode
        if self.calibration_mode:
            cv2.line(frame, (self.mouse_position[0] - 20, self.mouse_position[1]),
                    (self.mouse_position[0] + 20, self.mouse_position[1]), (255, 255, 255), 2)
            cv2.line(frame, (self.mouse_position[0], self.mouse_position[1] - 20),
                    (self.mouse_position[0], self.mouse_position[1] + 20), (255, 255, 255), 2)

        return frame

    def _draw_ui_overlays(self, frame: np.ndarray) -> np.ndarray:
        """Draw UI overlays with calibration information"""
        h, w = frame.shape[:2]

        # Draw calibration status panel
        if self.show_calibration_info:
            panel_height = 200
            panel = np.zeros((panel_height, w, 3), dtype=np.uint8)
            panel[:] = (40, 40, 40)  # Dark gray background

            # Get calibration status
            status = self.spatial_intelligence.get_calibration_status()

            # Draw calibration information
            y_offset = 25
            line_height = 25

            # Title
            cv2.putText(panel, "SPATIAL CALIBRATION STATUS", (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            y_offset += line_height

            # Quality indicator
            quality = status['calibration_quality']
            quality_color = (0, 255, 0) if quality > 0.7 else (0, 255, 255) if quality > 0.4 else (0, 0, 255)
            cv2.putText(panel, f"Quality: {quality:.1%}", (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, quality_color, 2)

            # Calibration points
            cv2.putText(panel, f"Calibration Points: {status['calibration_points']}", (200, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            y_offset += line_height

            # Distance overrides
            cv2.putText(panel, f"Distance Overrides: {status['distance_overrides']}", (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # Validation measurements
            cv2.putText(panel, f"Validations: {status['validation_measurements']}", (200, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            y_offset += line_height

            # Accuracy metrics
            if status['accuracy_metrics']['validation_count'] > 0:
                mean_error = status['accuracy_metrics']['mean_error']
                cv2.putText(panel, f"Mean Error: {mean_error:.2f}m", (10, y_offset),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # Camera parameters
            y_offset += line_height
            cv2.putText(panel, f"Camera: {status['camera_parameters']['height']:.1f}m height, " +
                              f"{status['camera_parameters']['fov_degrees']:.0f}° FOV",
                       (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (200, 200, 200), 1)

            # Combine with main frame
            frame = np.vstack([panel, frame])

        # Draw help overlay
        if self.show_help:
            help_text = [
                "CALIBRATION CONTROLS:",
                "'c' - Add calibration point",
                "'o' - Override distance",
                "'v' - Validate distance",
                "'r' - Reset calibration",
                "'h' - Toggle help"
            ]

            for i, text in enumerate(help_text):
                color = (255, 255, 255) if i == 0 else (200, 200, 200)
                cv2.putText(frame, text, (10, 30 + i * 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

        # Draw calibration mode indicator
        if self.calibration_mode:
            cv2.putText(frame, "CALIBRATION MODE - Click on object with known distance",
                       (10, h - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

        return frame

    def _mouse_callback(self, event, x, y, flags, param):
        """Handle mouse events for calibration"""
        self.mouse_position = (x, y)

        if event == cv2.EVENT_LBUTTONDOWN and self.calibration_mode:
            self.pending_calibration = (x, y)
            self.calibration_mode = False
            self._complete_calibration_point()

    def _start_calibration_point(self):
        """Start adding a calibration point"""
        self.calibration_mode = True
        print("🎯 Calibration mode activated - click on an object with known distance")

    def _complete_calibration_point(self):
        """Complete adding a calibration point"""
        if not self.pending_calibration:
            return

        # Get distance from user
        root = tk.Tk()
        root.withdraw()  # Hide the main window

        distance = simpledialog.askfloat(
            "Calibration Point",
            f"Enter the real-world distance to the clicked point:\n(Pixel position: {self.pending_calibration})",
            minvalue=0.1,
            maxvalue=100.0
        )

        if distance:
            description = simpledialog.askstring(
                "Calibration Point",
                "Enter a description for this calibration point:",
                initialvalue="Manual calibration point"
            )

            self.spatial_intelligence.add_calibration_point(
                self.pending_calibration[0], self.pending_calibration[1],
                distance, description=description or "Manual calibration point"
            )

            print(f"✅ Calibration point added at {distance:.1f}m")

        root.destroy()
        self.pending_calibration = None

    def _override_distance(self):
        """Override distance for selected track"""
        if not self.selected_track_id:
            print("⚠️ No track selected. Click on a worker first.")
            return

        root = tk.Tk()
        root.withdraw()

        distance = simpledialog.askfloat(
            "Distance Override",
            f"Enter manual distance for track {self.selected_track_id}:",
            minvalue=0.1,
            maxvalue=100.0
        )

        if distance:
            self.spatial_intelligence.set_distance_override(self.selected_track_id, distance)
            print(f"✅ Distance override set for track {self.selected_track_id}: {distance:.1f}m")

        root.destroy()

    def _validate_distance(self):
        """Validate distance measurement"""
        root = tk.Tk()
        root.withdraw()

        # Get pixel position
        x = simpledialog.askfloat("Validation", "Enter pixel X coordinate:", minvalue=0)
        if not x:
            root.destroy()
            return

        y = simpledialog.askfloat("Validation", "Enter pixel Y coordinate:", minvalue=0)
        if not y:
            root.destroy()
            return

        # Get known distance
        known_distance = simpledialog.askfloat(
            "Validation",
            "Enter the known real-world distance:",
            minvalue=0.1,
            maxvalue=100.0
        )

        if known_distance:
            result = self.spatial_intelligence.validate_distance_measurement(
                (x, y), known_distance
            )

            messagebox.showinfo(
                "Validation Result",
                f"Estimated: {result['estimated_distance']:.1f}m\n" +
                f"Known: {result['known_distance']:.1f}m\n" +
                f"Error: {result['absolute_error']:.1f}m ({result['relative_error']:.1%})"
            )

        root.destroy()

    def _reset_calibration(self):
        """Reset calibration system"""
        root = tk.Tk()
        root.withdraw()

        if messagebox.askyesno("Reset Calibration", "Are you sure you want to reset all calibration data?"):
            self.spatial_intelligence.calibration_points.clear()
            self.spatial_intelligence.distance_overrides.clear()
            self.spatial_intelligence.distance_validation_history.clear()
            self.spatial_intelligence.camera_calibration.calibration_quality = 0.3
            print("🔄 Calibration system reset")

        root.destroy()

    def _show_calibration_report(self):
        """Show final calibration report"""
        status = self.spatial_intelligence.get_calibration_status()

        print("\n" + "="*60)
        print("🎯 FINAL CALIBRATION REPORT")
        print("="*60)
        print(f"Calibration Quality: {status['calibration_quality']:.1%}")
        print(f"Calibration Points: {status['calibration_points']}")
        print(f"Distance Overrides: {status['distance_overrides']}")
        print(f"Validation Measurements: {status['validation_measurements']}")

        if status['accuracy_metrics']['validation_count'] > 0:
            print(f"Mean Error: {status['accuracy_metrics']['mean_error']:.2f}m")
            print(f"Max Error: {status['accuracy_metrics']['max_error']:.2f}m")
            print(f"Std Error: {status['accuracy_metrics']['std_error']:.2f}m")

        print("\n🎯 ADDRESSING ALANKAR'S FEEDBACK:")
        if status['calibration_quality'] > 0.7:
            print("✅ HIGH QUALITY CALIBRATION - Distance measurements should be accurate")
        elif status['calibration_quality'] > 0.4:
            print("⚠️ MEDIUM QUALITY CALIBRATION - Add more calibration points for better accuracy")
        else:
            print("❌ LOW QUALITY CALIBRATION - System needs calibration for accurate measurements")

        print("\n💡 RECOMMENDATIONS:")
        if status['calibration_points'] < 3:
            print("• Add more calibration points across different distances")
        if status['validation_measurements'] < 5:
            print("• Perform more validation measurements")
        if status['accuracy_metrics']['validation_count'] > 0 and status['accuracy_metrics']['mean_error'] > 1.0:
            print("• Review camera parameters and calibration points")

        print("="*60)

if __name__ == "__main__":
    demo = SpatialCalibrationDemo()
    demo.run()

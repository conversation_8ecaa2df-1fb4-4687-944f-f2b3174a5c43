"""
🎤 Voice-Controlled Chain-of-Zoom Interface
=============================================

Integrates OpenAI Whisper for natural language control of your 
Chain-of-Zoom safety tracking system.

Commands supported:
- "enhance track 5" -> Zoom and focus on specific worker
- "zoom in" / "zoom out" -> Manual zoom control  
- "detect helmets" -> Safety equipment detection mode
- "reset view" -> Return to normal view
- "follow worker in red" -> Auto-track by description
- "emergency zoom all" -> Multi-track emergency view

Built with love for worker safety! 💙
"""

import threading
import queue
import time
import numpy as np
import cv2
from typing import Optional, Callable, Dict, Any, List
import re
from dataclasses import dataclass

# Try to import Whisper and audio libraries
try:
    import whisper
    WHISPER_AVAILABLE = True
except ImportError:
    print("⚠️ Whisper not available. Install with: pip install openai-whisper")
    WHISPER_AVAILABLE = False

try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    print("⚠️ PyAudio not available. Install with: pip install pyaudio")
    PYAUDIO_AVAILABLE = False

@dataclass
class VoiceCommand:
    """Structured voice command"""
    action: str
    target: Optional[str] = None
    value: Optional[float] = None
    confidence: float = 0.0
    timestamp: float = 0.0

class VoiceControlledZoomInterface:
    """
    🎤 Voice interface for Chain-of-Zoom tracker
    
    Integrates Whisper ASR with your existing tracking system
    to enable natural language control during safety monitoring.
    """
    
    def __init__(self, chain_tracker, whisper_model: str = "base"):
        """
        Initialize voice interface
        
        Args:
            chain_tracker: Your ChainOfZoomTracker instance
            whisper_model: Whisper model size (tiny, base, small, medium, large)
        """
        self.chain_tracker = chain_tracker
        self.whisper_available = WHISPER_AVAILABLE
        self.pyaudio_available = PYAUDIO_AVAILABLE
        
        # Initialize Whisper if available
        if self.whisper_available:
            print(f"🎤 Loading Whisper model: {whisper_model}")
            try:
                self.whisper_model = whisper.load_model(whisper_model)
                print("✅ Whisper model loaded successfully")
            except Exception as e:
                print(f"❌ Failed to load Whisper model: {e}")
                self.whisper_available = False
        else:
            self.whisper_model = None
        
        # Audio configuration
        self.audio_config = {
            'format': 16 if self.pyaudio_available else None,  # pyaudio.paInt16
            'channels': 1,
            'rate': 16000,
            'chunk': 1024,
            'record_seconds': 3  # Command buffer length
        }
        
        # Voice command patterns
        self.command_patterns = {
            'zoom_in': [r'zoom in', r'enhance', r'closer', r'zoom closer'],
            'zoom_out': [r'zoom out', r'wider', r'back', r'zoom back'],
            'track_id': [r'track (\d+)', r'follow (\d+)', r'focus (\d+)', r'enhance track (\d+)'],
            'track_description': [r'follow.*?(worker|person).*?(red|blue|yellow|helmet|vest)',
                                r'track.*?(worker|person).*?(red|blue|yellow|helmet|vest)'],
            'safety_detect': [r'detect helmets?', r'safety check', r'equipment check', r'check safety'],
            'reset': [r'reset', r'normal view', r'default', r'reset view'],
            'emergency': [r'emergency', r'alert', r'all workers', r'emergency mode'],
            'auto_zoom': [r'auto zoom on', r'smart zoom', r'intelligent tracking', r'enable auto'],
            'manual_zoom': [r'manual zoom', r'zoom off', r'stop auto', r'disable auto']
        }
        
        # Current state
        self.is_listening = False
        self.voice_thread = None
        self.command_queue = queue.Queue()
        self.current_auto_target = None
        self.last_command_time = 0
        
        # Safety detection integration point
        self.safety_detector = None  # Will integrate with your helmet detection
        
        print("🎤 Voice Control Interface initialized")
        if not self.whisper_available:
            print("   ⚠️ Voice recognition disabled (Whisper not available)")
        if not self.pyaudio_available:
            print("   ⚠️ Audio input disabled (PyAudio not available)")
    
    def start_listening(self):
        """Start voice command listening in background thread"""
        if not self.whisper_available or not self.pyaudio_available:
            print("❌ Cannot start voice listening - missing dependencies")
            return False
            
        if self.is_listening:
            return True
            
        self.is_listening = True
        self.voice_thread = threading.Thread(target=self._voice_listener_loop)
        self.voice_thread.daemon = True
        self.voice_thread.start()
        print("🎤 Voice interface started - say commands now!")
        print("   Try: 'track 1', 'zoom in', 'reset', 'detect helmets'")
        return True
    
    def stop_listening(self):
        """Stop voice command listening"""
        self.is_listening = False
        if self.voice_thread:
            self.voice_thread.join(timeout=2.0)
        print("🔇 Voice interface stopped")
    
    def _voice_listener_loop(self):
        """Main voice listening loop"""
        if not self.pyaudio_available:
            return
            
        try:
            import pyaudio
            audio = pyaudio.PyAudio()
            
            while self.is_listening:
                # Record audio snippet
                audio_data = self._record_audio_snippet(audio)
                
                if audio_data is not None:
                    # Transcribe with Whisper
                    command_text = self._transcribe_audio(audio_data)
                    
                    if command_text:
                        # Parse and execute command
                        command = self._parse_voice_command(command_text)
                        if command:
                            command.timestamp = time.time()
                            self.command_queue.put(command)
                            print(f"🎤 Voice command: '{command_text}' -> {command.action}")
                        
                time.sleep(0.1)  # Prevent CPU overload
                
        except Exception as e:
            print(f"❌ Voice listener error: {e}")
        finally:
            if 'audio' in locals():
                audio.terminate()
    
    def _record_audio_snippet(self, audio) -> Optional[np.ndarray]:
        """Record short audio snippet for command detection"""
        if not self.pyaudio_available:
            return None
            
        try:
            import pyaudio
            stream = audio.open(
                format=pyaudio.paInt16,
                channels=self.audio_config['channels'],
                rate=self.audio_config['rate'],
                input=True,
                frames_per_buffer=self.audio_config['chunk']
            )
            
            frames = []
            for _ in range(0, int(self.audio_config['rate'] / self.audio_config['chunk'] * 
                                self.audio_config['record_seconds'])):
                data = stream.read(self.audio_config['chunk'])
                frames.append(data)
            
            stream.stop_stream()
            stream.close()
            
            # Convert to numpy array
            audio_data = np.frombuffer(b''.join(frames), dtype=np.int16)
            audio_data = audio_data.astype(np.float32) / 32768.0  # Normalize to [-1, 1]
            
            # Basic voice activity detection (simple energy threshold)
            if np.mean(np.abs(audio_data)) > 0.01:  # Adjust threshold as needed
                return audio_data
            
            return None
            
        except Exception as e:
            print(f"❌ Audio recording error: {e}")
            return None
    
    def _transcribe_audio(self, audio_data: np.ndarray) -> Optional[str]:
        """Transcribe audio using Whisper"""
        if not self.whisper_available:
            return None
            
        try:
            # Whisper expects 16kHz audio
            result = self.whisper_model.transcribe(audio_data)
            text = result['text'].strip().lower()
            
            # Filter out very short or nonsensical transcriptions
            if len(text) > 3 and not text.startswith('['):
                return text
            
            return None
            
        except Exception as e:
            print(f"❌ Whisper transcription error: {e}")
            return None
    
    def _parse_voice_command(self, text: str) -> Optional[VoiceCommand]:
        """Parse transcribed text into structured command"""
        text = text.lower().strip()
        
        # Check each command pattern
        for command_type, patterns in self.command_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, text)
                if match:
                    return self._create_command(command_type, match, text)
        
        return None
    
    def _create_command(self, command_type: str, match, text: str) -> VoiceCommand:
        """Create structured command from pattern match"""
        if command_type == 'track_id':
            track_id = int(match.group(1))
            return VoiceCommand('track_id', target=str(track_id), confidence=0.9)
            
        elif command_type == 'track_description':
            description = match.group(0)
            return VoiceCommand('track_description', target=description, confidence=0.8)
            
        elif command_type == 'zoom_in':
            return VoiceCommand('zoom_in', value=1.5, confidence=0.9)
            
        elif command_type == 'zoom_out':
            return VoiceCommand('zoom_out', value=0.7, confidence=0.9)
            
        elif command_type == 'safety_detect':
            return VoiceCommand('safety_detect', confidence=0.9)
            
        elif command_type == 'reset':
            return VoiceCommand('reset', confidence=0.9)
            
        elif command_type == 'emergency':
            return VoiceCommand('emergency', confidence=0.9)
            
        elif command_type == 'auto_zoom':
            return VoiceCommand('auto_zoom', target='on', confidence=0.8)
            
        elif command_type == 'manual_zoom':
            return VoiceCommand('auto_zoom', target='off', confidence=0.8)
        
        return VoiceCommand(command_type, confidence=0.7)

    def process_voice_commands(self, frame: np.ndarray, tracks: list,
                             current_zoom: float) -> Dict[str, Any]:
        """
        Process pending voice commands and return control actions

        Args:
            frame: Current video frame
            tracks: Current tracks from your tracker
            current_zoom: Current zoom level

        Returns:
            Dict with zoom/tracking control actions
        """
        actions = {}

        # Process all pending commands
        while not self.command_queue.empty():
            try:
                command = self.command_queue.get_nowait()
                action = self._execute_voice_command(command, frame, tracks, current_zoom)
                if action:
                    actions.update(action)
                    self.last_command_time = time.time()
            except queue.Empty:
                break

        return actions

    def _execute_voice_command(self, command: VoiceCommand, frame: np.ndarray,
                             tracks: list, current_zoom: float) -> Optional[Dict]:
        """Execute a specific voice command"""

        if command.action == 'track_id':
            # Focus on specific track ID
            track_id = int(command.target)
            target_track = self._find_track_by_id(tracks, track_id)
            if target_track:
                bbox = self._extract_bbox(target_track)
                print(f"🎯 Zooming to track {track_id}")
                return {
                    'zoom_to_bbox': bbox,
                    'target_zoom': current_zoom * 2.0,
                    'track_id': track_id,
                    'voice_triggered': True
                }
            else:
                print(f"❌ Track {track_id} not found")

        elif command.action == 'track_description':
            # This would integrate with your safety detection
            # For now, focus on first detected person
            if tracks:
                bbox = self._extract_bbox(tracks[0])
                print(f"🎯 Zooming to worker by description")
                return {
                    'zoom_to_bbox': bbox,
                    'target_zoom': current_zoom * 1.5,
                    'voice_triggered': True
                }

        elif command.action == 'zoom_in':
            print(f"🔍 Voice zoom in: {current_zoom:.2f} -> {current_zoom * command.value:.2f}")
            return {
                'target_zoom': current_zoom * command.value,
                'smooth_transition': True,
                'voice_triggered': True
            }

        elif command.action == 'zoom_out':
            print(f"🔍 Voice zoom out: {current_zoom:.2f} -> {current_zoom * command.value:.2f}")
            return {
                'target_zoom': current_zoom * command.value,
                'smooth_transition': True,
                'voice_triggered': True
            }

        elif command.action == 'safety_detect':
            # Trigger safety equipment detection
            print("🦺 Activating safety detection mode")
            return {
                'enable_safety_detection': True,
                'detection_mode': 'helmet_and_vest',
                'voice_triggered': True
            }

        elif command.action == 'reset':
            print("🔄 Resetting view to normal")
            return {
                'target_zoom': 1.0,
                'reset_tracking': True,
                'smooth_transition': True,
                'voice_triggered': True
            }

        elif command.action == 'emergency':
            # Emergency multi-track view
            print("🚨 Activating emergency mode")
            return {
                'emergency_mode': True,
                'target_zoom': 0.8,  # Zoom out to see all workers
                'highlight_all_tracks': True,
                'voice_triggered': True
            }

        elif command.action == 'auto_zoom':
            if command.target == 'on':
                print("🤖 Enabling automatic zoom")
                return {'auto_zoom_enabled': True, 'voice_triggered': True}
            else:
                print("👤 Disabling automatic zoom")
                return {'auto_zoom_enabled': False, 'voice_triggered': True}

        return None

    def _find_track_by_id(self, tracks: list, track_id: int):
        """Find track by ID"""
        for track in tracks:
            if getattr(track, 'track_id', None) == track_id:
                return track
        return None

    def _extract_bbox(self, track) -> list:
        """Extract bounding box from track (reuse your existing logic)"""
        if hasattr(track, 'to_ltrb'):
            return list(map(int, track.to_ltrb()))
        elif hasattr(track, 'tlbr'):
            return list(map(int, track.tlbr))
        elif hasattr(track, 'bbox'):
            return list(map(int, track.bbox))
        else:
            return [100, 100, 200, 200]  # Default bbox

    def get_command_history(self, limit: int = 10) -> List[VoiceCommand]:
        """Get recent voice command history"""
        history = []
        temp_queue = queue.Queue()

        # Extract commands from queue without losing them
        while not self.command_queue.empty() and len(history) < limit:
            try:
                cmd = self.command_queue.get_nowait()
                history.append(cmd)
                temp_queue.put(cmd)
            except queue.Empty:
                break

        # Put commands back
        while not temp_queue.empty():
            self.command_queue.put(temp_queue.get_nowait())

        return history

    def is_voice_available(self) -> bool:
        """Check if voice control is available"""
        return self.whisper_available and self.pyaudio_available

    def get_supported_commands(self) -> Dict[str, List[str]]:
        """Get list of supported voice commands"""
        return {
            'Zoom Control': [
                'zoom in', 'zoom out', 'zoom closer', 'zoom back'
            ],
            'Track Selection': [
                'track 1', 'follow 5', 'focus 12', 'enhance track 3'
            ],
            'Safety Detection': [
                'detect helmets', 'safety check', 'equipment check'
            ],
            'View Control': [
                'reset', 'normal view', 'reset view'
            ],
            'Emergency': [
                'emergency', 'emergency mode', 'all workers'
            ],
            'Auto Mode': [
                'auto zoom on', 'smart zoom', 'manual zoom', 'stop auto'
            ]
        }


# Integration wrapper for your Chain-of-Zoom system
class VoiceEnabledChainTracker:
    """
    🎤 Voice-enabled wrapper for your Chain-of-Zoom tracker
    """

    def __init__(self, base_tracker, whisper_model: str = "base"):
        # Initialize your existing tracker
        self.chain_tracker = base_tracker

        # Add voice interface
        self.voice_interface = VoiceControlledZoomInterface(
            self.chain_tracker, whisper_model
        )

        # State
        self.auto_zoom_enabled = False
        self.emergency_mode = False
        self.voice_actions_log = []

    def start_voice_control(self):
        """Start voice control system"""
        success = self.voice_interface.start_listening()
        if success:
            print("🎤 Voice control active!")
            print("Try saying: 'track 1', 'zoom in', 'reset', 'detect helmets'")
            return True
        return False

    def update(self, frame: np.ndarray, detections: list,
               zoom_level: float) -> tuple:
        """
        Update with voice control integration

        Returns:
            (tracks, control_actions) - tracks and any voice-triggered actions
        """
        # Get tracks from your Chain-of-Zoom system
        tracks = self.chain_tracker.update(frame, detections, zoom_level)

        # Process voice commands
        voice_actions = self.voice_interface.process_voice_commands(
            frame, tracks, zoom_level
        )

        # Apply voice-triggered zoom changes
        new_zoom = zoom_level
        if 'target_zoom' in voice_actions:
            new_zoom = voice_actions['target_zoom']
            print(f"🎤 Voice zoom: {zoom_level:.2f} -> {new_zoom:.2f}")

        # Log voice actions
        if voice_actions and voice_actions.get('voice_triggered', False):
            self.voice_actions_log.append({
                'timestamp': time.time(),
                'action': voice_actions,
                'zoom_before': zoom_level,
                'zoom_after': new_zoom
            })

            # Keep only recent actions
            if len(self.voice_actions_log) > 50:
                self.voice_actions_log = self.voice_actions_log[-25:]

        return tracks, {
            'new_zoom_level': new_zoom,
            'voice_actions': voice_actions,
            'emergency_mode': voice_actions.get('emergency_mode', False),
            'auto_zoom_enabled': voice_actions.get('auto_zoom_enabled', self.auto_zoom_enabled)
        }

    def stop_voice_control(self):
        """Stop voice control"""
        self.voice_interface.stop_listening()

    def get_voice_stats(self) -> Dict:
        """Get voice control statistics"""
        return {
            'voice_available': self.voice_interface.is_voice_available(),
            'is_listening': self.voice_interface.is_listening,
            'recent_commands': len(self.voice_actions_log),
            'last_command_time': self.voice_interface.last_command_time,
            'supported_commands': self.voice_interface.get_supported_commands()
        }

#!/usr/bin/env python3
"""
🔬 TRUE SIDE-BY-SIDE TEST - Two Completely Separate Trackers

This creates a REAL comparison with:
1. LEFT SIDE: Completely separate YOLO ByteTrack instance
2. RIGHT SIDE: Completely separate YOLO ByteTrack + Your Zoom Wrapper
3. SAME VIDEO: Both process the exact same PTZ-transformed frames
4. REAL DIFFERENCES: You'll see actual tracking differences

NO SHARED STATE - True independent comparison!
"""

import cv2
import numpy as np
from ultralytics import YOLO
import time
import sys
import os
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
import uuid

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))

from src.core.zoom_tracking_wrapper import ZoomTrackingWrapper


@dataclass
class YOLOTrack:
    """Wrapper for YOLO tracking results"""
    track_id: int
    tlbr: List[float]
    confidence: float
    
    def to_ltrb(self):
        return self.tlbr


class IndependentYOLOTracker:
    """
    Completely independent YOLO tracker instance
    Each instance has its own model and tracking state
    """
    
    def __init__(self, tracker_id: str):
        self.tracker_id = tracker_id
        # Create SEPARATE YOLO model instance
        self.model = YOLO('yolo11n.pt')
        self.frame_count = 0
        
        print(f"   🔍 Created independent tracker: {tracker_id}")
        
    def update_tracks(self, detections, frame):
        """
        Independent YOLO ByteTrack tracking
        Each tracker maintains its own state
        """
        self.frame_count += 1
        
        # Use YOLO's built-in tracking with unique tracker instance
        results = self.model.track(
            frame, 
            persist=True,  # Each tracker maintains its own persistence
            conf=0.3,
            classes=[0],  # Person class only
            verbose=False,
            tracker="bytetrack.yaml"
        )
        
        tracks = []
        for result in results:
            if result.boxes is not None and result.boxes.id is not None:
                for i, track_id in enumerate(result.boxes.id):
                    if hasattr(result.boxes.xyxy[i], 'cpu'):
                        x1, y1, x2, y2 = result.boxes.xyxy[i].cpu().numpy()
                        conf = result.boxes.conf[i].cpu().numpy()
                        track_id = int(track_id.cpu().numpy())
                    else:
                        x1, y1, x2, y2 = result.boxes.xyxy[i]
                        conf = result.boxes.conf[i]
                        track_id = int(track_id)
                    
                    track = YOLOTrack(
                        track_id=track_id,
                        tlbr=[float(x1), float(y1), float(x2), float(y2)],
                        confidence=float(conf)
                    )
                    tracks.append(track)
        
        return tracks


class TrueSideBySideTest:
    """
    🔬 TRUE side-by-side comparison with completely separate trackers
    """
    
    def __init__(self, video_path: str = "videos/test2.mp4"):
        print("🔬 TRUE SIDE-BY-SIDE TEST")
        print("Two Completely Independent Trackers")
        print("=" * 60)
        
        self.video_path = video_path
        
        # Create TWO COMPLETELY SEPARATE YOLO trackers
        print("🔍 Setting up VANILLA YOLO ByteTrack (Left Side)...")
        self.vanilla_tracker = IndependentYOLOTracker("VANILLA")
        
        print("🔍 Setting up BASE YOLO for Wrapper (Right Side)...")
        base_tracker_for_wrapper = IndependentYOLOTracker("WRAPPER_BASE")
        
        print("🚀 Setting up ZOOM WRAPPER (Right Side)...")
        self.zoom_wrapper = ZoomTrackingWrapper(base_tracker_for_wrapper)
        
        # PTZ parameters
        self.zoom_level = 1.0
        self.target_zoom = 1.0
        self.zoom_speed = 0.1
        
        self.pan_offset = 0.0
        self.target_pan = 0.0
        self.pan_speed = 0.03
        
        self.tilt_offset = 0.0
        self.target_tilt = 0.0
        self.tilt_speed = 0.03
        
        # Auto PTZ sequence
        self.auto_ptz = True
        self.ptz_sequence_frame = 0
        
        # Statistics for BOTH trackers
        self.frame_count = 0
        self.vanilla_id_switches = 0
        self.wrapper_id_switches = 0
        self.previous_vanilla_ids = set()
        self.previous_wrapper_ids = set()
        
        print("✅ True side-by-side test ready!")
        print("   🔍 LEFT: Independent YOLO ByteTrack")
        print("   🚀 RIGHT: Independent YOLO ByteTrack + Zoom Wrapper")
        print("   🎯 SAME VIDEO: Both process identical PTZ frames")
    
    def run_true_comparison(self):
        """Run true side-by-side comparison"""
        cap = cv2.VideoCapture(self.video_path)
        if not cap.isOpened():
            print(f"❌ Could not open video: {self.video_path}")
            return
        
        print("\n🔬 STARTING TRUE SIDE-BY-SIDE COMPARISON")
        print("Controls:")
        print("  'z' = Zoom In")
        print("  'x' = Zoom Out")
        print("  'a' = Pan Left")
        print("  'd' = Pan Right")
        print("  'w' = Tilt Up")
        print("  's' = Tilt Down")
        print("  'r' = Reset PTZ")
        print("  'SPACE' = Toggle Auto PTZ")
        print("  'q' = Quit")
        print("\n🎯 Watch for REAL differences between left and right!")
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # Loop video
                    continue
                
                self.frame_count += 1
                self.ptz_sequence_frame += 1
                
                # Auto PTZ sequence
                if self.auto_ptz:
                    self._update_auto_ptz_sequence()
                
                # Update PTZ
                self._update_ptz()
                
                # Apply PTZ transformation to create the SAME frame for both
                ptz_frame = self._apply_ptz(frame)
                
                try:
                    # LEFT SIDE: Vanilla YOLO ByteTrack (independent)
                    vanilla_tracks = self.vanilla_tracker.update_tracks([], ptz_frame.copy())
                    
                    # RIGHT SIDE: Zoom Wrapper + YOLO ByteTrack (independent)
                    wrapper_tracks = self.zoom_wrapper.update(ptz_frame.copy(), [], self.zoom_level)
                    
                    # Monitor ID changes for BOTH
                    self._monitor_both_trackers(vanilla_tracks, wrapper_tracks)
                    
                    # Create TRUE side-by-side display
                    display_frame = self._create_true_comparison_display(ptz_frame, vanilla_tracks, wrapper_tracks)
                    
                    # Show statistics
                    if self.frame_count % 30 == 0:
                        print(f"📊 Frame {self.frame_count}: "
                              f"PTZ: Z{self.zoom_level:.1f}x P{self.pan_offset:.2f} T{self.tilt_offset:.2f} | "
                              f"Vanilla Switches: {self.vanilla_id_switches} | "
                              f"Wrapper Switches: {self.wrapper_id_switches}")
                    
                    # Display
                    cv2.imshow('🔬 TRUE Side-by-Side: Vanilla vs Zoom Wrapper', display_frame)
                    
                except Exception as e:
                    print(f"⚠️ Tracking Error: {str(e)[:100]}...")
                    
                    # Show error frame
                    error_frame = self._create_error_display(ptz_frame, str(e))
                    cv2.imshow('🔬 TRUE Side-by-Side: Vanilla vs Zoom Wrapper', error_frame)
                
                # Handle controls
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('z'):
                    self._zoom_in()
                elif key == ord('x'):
                    self._zoom_out()
                elif key == ord('a'):
                    self._pan_left()
                elif key == ord('d'):
                    self._pan_right()
                elif key == ord('w'):
                    self._tilt_up()
                elif key == ord('s'):
                    self._tilt_down()
                elif key == ord('r'):
                    self._reset_ptz()
                elif key == ord(' '):
                    self._toggle_auto_ptz()
        
        finally:
            cap.release()
            cv2.destroyAllWindows()
            self._print_true_comparison_results()
    
    def _update_auto_ptz_sequence(self):
        """Auto PTZ sequence to trigger tracking problems"""
        sequence_time = self.ptz_sequence_frame
        
        # 10-second aggressive cycle
        cycle = sequence_time % 300  # 10 second cycle at 30fps
        
        if cycle < 50:  # Fast zoom in (1.7 seconds)
            progress = cycle / 50.0
            self.target_zoom = 1.0 + (progress * 2.5)  # 1x to 3.5x
        elif cycle < 100:  # Fast pan while zoomed (1.7 seconds)
            progress = (cycle - 50) / 50.0
            self.target_pan = progress * 0.3  # Pan to 0.3
        elif cycle < 150:  # Fast tilt while panned (1.7 seconds)
            progress = (cycle - 100) / 50.0
            self.target_tilt = progress * 0.2  # Tilt to 0.2
        elif cycle < 200:  # Fast zoom out (1.7 seconds)
            progress = (cycle - 150) / 50.0
            self.target_zoom = 3.5 - (progress * 2.5)  # Back to 1x
        elif cycle < 250:  # Return pan (1.7 seconds)
            progress = (cycle - 200) / 50.0
            self.target_pan = 0.3 - (progress * 0.3)  # Back to 0
        else:  # Return tilt (1.7 seconds)
            progress = (cycle - 250) / 50.0
            self.target_tilt = 0.2 - (progress * 0.2)  # Back to 0

    def _update_ptz(self):
        """Update PTZ parameters smoothly"""
        # Update zoom
        if abs(self.zoom_level - self.target_zoom) > 0.01:
            if self.zoom_level < self.target_zoom:
                self.zoom_level = min(self.target_zoom, self.zoom_level + self.zoom_speed)
            else:
                self.zoom_level = max(self.target_zoom, self.zoom_level - self.zoom_speed)

        # Update pan
        if abs(self.pan_offset - self.target_pan) > 0.001:
            if self.pan_offset < self.target_pan:
                self.pan_offset = min(self.target_pan, self.pan_offset + self.pan_speed)
            else:
                self.pan_offset = max(self.target_pan, self.pan_offset - self.pan_speed)

        # Update tilt
        if abs(self.tilt_offset - self.target_tilt) > 0.001:
            if self.tilt_offset < self.target_tilt:
                self.tilt_offset = min(self.target_tilt, self.tilt_offset + self.tilt_speed)
            else:
                self.tilt_offset = max(self.target_tilt, self.tilt_offset - self.tilt_speed)

    def _apply_ptz(self, frame):
        """Apply PTZ transformation - SAME frame for both trackers"""
        h, w = frame.shape[:2]

        # Step 1: Apply Pan (horizontal shift)
        pan_pixels = int(self.pan_offset * w * 0.3)  # 30% of width max
        if pan_pixels != 0:
            M_pan = np.float32([[1, 0, pan_pixels], [0, 1, 0]])
            frame = cv2.warpAffine(frame, M_pan, (w, h), borderMode=cv2.BORDER_REFLECT)

        # Step 2: Apply Tilt (vertical shift)
        tilt_pixels = int(self.tilt_offset * h * 0.3)  # 30% of height max
        if tilt_pixels != 0:
            M_tilt = np.float32([[1, 0, 0], [0, 1, tilt_pixels]])
            frame = cv2.warpAffine(frame, M_tilt, (w, h), borderMode=cv2.BORDER_REFLECT)

        # Step 3: Apply Zoom (crop and resize)
        if self.zoom_level > 1.0:
            crop_w = int(w / self.zoom_level)
            crop_h = int(h / self.zoom_level)

            # Center crop
            start_x = (w - crop_w) // 2
            start_y = (h - crop_h) // 2

            start_x = max(0, min(start_x, w - crop_w))
            start_y = max(0, min(start_y, h - crop_h))

            cropped = frame[start_y:start_y+crop_h, start_x:start_x+crop_w]
            frame = cv2.resize(cropped, (w, h), interpolation=cv2.INTER_LINEAR)

        return frame

    def _monitor_both_trackers(self, vanilla_tracks, wrapper_tracks):
        """Monitor ID changes for BOTH independent trackers"""
        # Monitor vanilla tracker ID switches
        vanilla_current_ids = {track.track_id for track in vanilla_tracks}
        if len(self.previous_vanilla_ids) > 0 and len(vanilla_current_ids) > 0:
            disappeared = self.previous_vanilla_ids - vanilla_current_ids
            appeared = vanilla_current_ids - self.previous_vanilla_ids
            if len(disappeared) > 0 and len(appeared) > 0:
                self.vanilla_id_switches += len(appeared)
        self.previous_vanilla_ids = vanilla_current_ids.copy()

        # Monitor wrapper tracker ID switches
        wrapper_current_ids = {track.track_id for track in wrapper_tracks}
        if len(self.previous_wrapper_ids) > 0 and len(wrapper_current_ids) > 0:
            disappeared = self.previous_wrapper_ids - wrapper_current_ids
            appeared = wrapper_current_ids - self.previous_wrapper_ids
            if len(disappeared) > 0 and len(appeared) > 0:
                self.wrapper_id_switches += len(appeared)
        self.previous_wrapper_ids = wrapper_current_ids.copy()

    def _create_true_comparison_display(self, frame, vanilla_tracks, wrapper_tracks):
        """Create TRUE side-by-side comparison display"""
        h, w = frame.shape[:2]

        # Create side-by-side canvas
        canvas = np.zeros((h, w * 2, 3), dtype=np.uint8)

        # LEFT SIDE: Vanilla YOLO ByteTrack
        left_frame = frame.copy()
        self._draw_tracks(left_frame, vanilla_tracks, (0, 0, 255), "VANILLA")
        canvas[:, :w] = left_frame

        # RIGHT SIDE: Zoom wrapper + YOLO ByteTrack
        right_frame = frame.copy()
        self._draw_tracks(right_frame, wrapper_tracks, (0, 255, 0), "WRAPPER")
        canvas[:, w:] = right_frame

        # Add divider
        cv2.line(canvas, (w, 0), (w, h), (255, 255, 255), 4)

        # Add titles
        cv2.putText(canvas, "VANILLA YOLO ByteTrack", (10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)
        cv2.putText(canvas, "(Independent Tracker)", (10, 70),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 255), 1)

        cv2.putText(canvas, "ZOOM WRAPPER + YOLO", (w + 10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 2)
        cv2.putText(canvas, "(Independent + Enhanced)", (w + 10, 70),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 1)

        # Add PTZ status
        ptz_text = f'PTZ: Z{self.zoom_level:.1f}x P{self.pan_offset:.2f} T{self.tilt_offset:.2f}'
        cv2.putText(canvas, ptz_text, (10, 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(canvas, ptz_text, (w + 10, 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        # Add statistics
        stats_left = f'ID Switches: {self.vanilla_id_switches}'
        stats_right = f'ID Switches: {self.wrapper_id_switches}'
        cv2.putText(canvas, stats_left, (10, 130),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(canvas, stats_right, (w + 10, 130),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        # Add comparison
        if self.vanilla_id_switches > 0 and self.wrapper_id_switches >= 0:
            if self.wrapper_id_switches < self.vanilla_id_switches:
                improvement = ((self.vanilla_id_switches - self.wrapper_id_switches) / self.vanilla_id_switches) * 100
                cv2.putText(canvas, f"✅ {improvement:.1f}% BETTER!", (w + 10, 160),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
            elif self.wrapper_id_switches == self.vanilla_id_switches:
                cv2.putText(canvas, "= SAME PERFORMANCE", (w + 10, 160),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

        return canvas

    def _draw_tracks(self, frame, tracks, color, tracker_name):
        """Draw tracks with large, visible IDs"""
        for track in tracks:
            x1, y1, x2, y2 = track.to_ltrb()

            # Draw bounding box
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 3)

            # Draw LARGE track ID
            id_text = f'ID: {track.track_id}'
            cv2.putText(frame, id_text, (int(x1), int(y1) - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, color, 2)

    def _create_error_display(self, frame, error_msg):
        """Create display showing tracking errors"""
        display_frame = frame.copy()
        h, w = display_frame.shape[:2]

        # Add error overlay
        overlay = display_frame.copy()
        cv2.rectangle(overlay, (0, 0), (w, 150), (0, 0, 255), -1)
        cv2.addWeighted(overlay, 0.3, display_frame, 0.7, 0, display_frame)

        # Error message
        cv2.putText(display_frame, "❌ TRACKING ERROR", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)
        cv2.putText(display_frame, "This demonstrates real tracking problems!", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return display_frame

    # PTZ Control methods
    def _zoom_in(self):
        self.target_zoom = min(4.0, self.target_zoom + 0.5)
        print(f"🔍 ZOOM IN to {self.target_zoom:.1f}x")

    def _zoom_out(self):
        self.target_zoom = max(1.0, self.target_zoom - 0.5)
        print(f"🔍 ZOOM OUT to {self.target_zoom:.1f}x")

    def _pan_left(self):
        self.target_pan = max(-0.4, self.target_pan - 0.1)
        print(f"⬅️ PAN LEFT to {self.target_pan:.2f}")

    def _pan_right(self):
        self.target_pan = min(0.4, self.target_pan + 0.1)
        print(f"➡️ PAN RIGHT to {self.target_pan:.2f}")

    def _tilt_up(self):
        self.target_tilt = max(-0.3, self.target_tilt - 0.1)
        print(f"⬆️ TILT UP to {self.target_tilt:.2f}")

    def _tilt_down(self):
        self.target_tilt = min(0.3, self.target_tilt + 0.1)
        print(f"⬇️ TILT DOWN to {self.target_tilt:.2f}")

    def _reset_ptz(self):
        self.target_zoom = 1.0
        self.target_pan = 0.0
        self.target_tilt = 0.0
        print("🔄 PTZ RESET")

    def _toggle_auto_ptz(self):
        self.auto_ptz = not self.auto_ptz
        status = "ON" if self.auto_ptz else "OFF"
        print(f"🤖 AUTO PTZ: {status}")

    def _print_true_comparison_results(self):
        """Print true comparison results"""
        print("\n🔬 TRUE SIDE-BY-SIDE COMPARISON RESULTS")
        print("=" * 60)
        print(f"📊 Total frames processed: {self.frame_count}")
        print(f"🔴 Vanilla YOLO ID switches: {self.vanilla_id_switches}")
        print(f"🟢 Zoom wrapper ID switches: {self.wrapper_id_switches}")

        if self.vanilla_id_switches > 0:
            improvement = ((self.vanilla_id_switches - self.wrapper_id_switches) / self.vanilla_id_switches) * 100
            print(f"📈 Improvement: {improvement:.1f}%")

            if improvement > 20:
                print("🎉 SIGNIFICANT IMPROVEMENT!")
                print("✅ Zoom wrapper clearly outperforms vanilla tracking!")
            elif improvement > 0:
                print("✅ MODERATE IMPROVEMENT")
                print("📈 Zoom wrapper shows measurable benefit")
            else:
                print("⚠️ No significant improvement detected")
        else:
            print("ℹ️ No ID switches detected in vanilla tracker")

        print("\n🔬 TRUE COMPARISON VALIDATION:")
        print("• Two completely independent YOLO instances")
        print("• Same PTZ-transformed frames for both")
        print("• Real tracking algorithm differences")
        print("• Reproducible in any YOLO environment")


if __name__ == "__main__":
    test = TrueSideBySideTest("videos/test2.mp4")
    test.run_true_comparison()

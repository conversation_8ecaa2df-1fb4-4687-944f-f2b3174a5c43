#!/usr/bin/env python3
"""
🔥 EXTREME PTZ TEST - Break Vanilla Tracking, Show Wrapper Power

This creates EXTREME PTZ conditions that will:
1. Break vanilla YOLO ByteTrack dramatically
2. Show your zoom wrapper fixing the problems
3. Create clear, undeniable differences

EXTREME CONDITIONS:
- Sudden 4x zoom jumps
- Rapid pan/tilt changes
- Simulated camera shake
- Real tracking nightmare scenarios
"""

import cv2
import numpy as np
from ultralytics import YOLO
import time
import sys
import os
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
import random

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))

from src.core.zoom_tracking_wrapper import ZoomTrackingWrapper


@dataclass
class YOLOTrack:
    """Wrapper for YOLO tracking results"""
    track_id: int
    tlbr: List[float]
    confidence: float
    
    def to_ltrb(self):
        return self.tlbr


class IndependentYOLOTracker:
    """Independent YOLO tracker instance"""
    
    def __init__(self, tracker_id: str):
        self.tracker_id = tracker_id
        self.model = YOLO('yolo11n.pt')
        self.frame_count = 0
        print(f"   🔍 Created independent tracker: {tracker_id}")
        
    def update_tracks(self, detections, frame):
        """Independent YOLO ByteTrack tracking"""
        self.frame_count += 1
        
        results = self.model.track(
            frame, 
            persist=True,
            conf=0.3,
            classes=[0],
            verbose=False,
            tracker="bytetrack.yaml"
        )
        
        tracks = []
        for result in results:
            if result.boxes is not None and result.boxes.id is not None:
                for i, track_id in enumerate(result.boxes.id):
                    if hasattr(result.boxes.xyxy[i], 'cpu'):
                        x1, y1, x2, y2 = result.boxes.xyxy[i].cpu().numpy()
                        conf = result.boxes.conf[i].cpu().numpy()
                        track_id = int(track_id.cpu().numpy())
                    else:
                        x1, y1, x2, y2 = result.boxes.xyxy[i]
                        conf = result.boxes.conf[i]
                        track_id = int(track_id)
                    
                    track = YOLOTrack(
                        track_id=track_id,
                        tlbr=[float(x1), float(y1), float(x2), float(y2)],
                        confidence=float(conf)
                    )
                    tracks.append(track)
        
        return tracks


class ExtremePTZTest:
    """
    🔥 EXTREME PTZ test to break vanilla tracking and show wrapper power
    """
    
    def __init__(self, video_path: str = "videos/test2.mp4"):
        print("🔥 EXTREME PTZ TEST")
        print("Break Vanilla Tracking, Show Wrapper Power")
        print("=" * 60)
        
        self.video_path = video_path
        
        # Create TWO COMPLETELY SEPARATE YOLO trackers
        print("🔍 Setting up VANILLA YOLO ByteTrack (Left Side)...")
        self.vanilla_tracker = IndependentYOLOTracker("VANILLA")
        
        print("🔍 Setting up BASE YOLO for Wrapper (Right Side)...")
        base_tracker_for_wrapper = IndependentYOLOTracker("WRAPPER_BASE")
        
        print("🚀 Setting up ZOOM WRAPPER (Right Side)...")
        self.zoom_wrapper = ZoomTrackingWrapper(base_tracker_for_wrapper)
        
        # EXTREME PTZ parameters
        self.zoom_level = 1.0
        self.target_zoom = 1.0
        
        self.pan_offset = 0.0
        self.target_pan = 0.0
        
        self.tilt_offset = 0.0
        self.target_tilt = 0.0
        
        # EXTREME auto PTZ sequence
        self.auto_ptz = True
        self.ptz_sequence_frame = 0
        
        # Statistics for BOTH trackers
        self.frame_count = 0
        self.vanilla_id_switches = 0
        self.wrapper_id_switches = 0
        self.previous_vanilla_ids = set()
        self.previous_wrapper_ids = set()
        
        # Track wrapper activations
        self.wrapper_activations = 0
        
        print("✅ EXTREME PTZ test ready!")
        print("   🔥 EXTREME CONDITIONS: Sudden zooms, rapid PTZ, camera shake")
        print("   🎯 GOAL: Break vanilla tracking, show wrapper superiority")
    
    def run_extreme_test(self):
        """Run EXTREME PTZ test"""
        cap = cv2.VideoCapture(self.video_path)
        if not cap.isOpened():
            print(f"❌ Could not open video: {self.video_path}")
            return
        
        print("\n🔥 STARTING EXTREME PTZ TEST")
        print("Controls:")
        print("  'z' = EXTREME Zoom In")
        print("  'x' = EXTREME Zoom Out")
        print("  'a' = EXTREME Pan Left")
        print("  'd' = EXTREME Pan Right")
        print("  'w' = EXTREME Tilt Up")
        print("  's' = EXTREME Tilt Down")
        print("  'r' = Reset PTZ")
        print("  'SPACE' = Toggle Auto EXTREME PTZ")
        print("  'q' = Quit")
        print("\n🔥 EXTREME CONDITIONS WILL BREAK VANILLA TRACKING!")
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # Loop video
                    continue
                
                self.frame_count += 1
                self.ptz_sequence_frame += 1
                
                # EXTREME auto PTZ sequence
                if self.auto_ptz:
                    self._update_extreme_ptz_sequence()
                
                # Apply EXTREME PTZ transformation
                ptz_frame = self._apply_extreme_ptz(frame)
                
                try:
                    # LEFT SIDE: Vanilla YOLO ByteTrack (will break!)
                    vanilla_tracks = self.vanilla_tracker.update_tracks([], ptz_frame.copy())
                    
                    # RIGHT SIDE: Zoom Wrapper + YOLO ByteTrack (should survive!)
                    wrapper_tracks = self.zoom_wrapper.update(ptz_frame.copy(), [], self.zoom_level)
                    
                    # Monitor ID changes for BOTH
                    self._monitor_both_trackers(vanilla_tracks, wrapper_tracks)
                    
                    # Create EXTREME comparison display
                    display_frame = self._create_extreme_display(ptz_frame, vanilla_tracks, wrapper_tracks)
                    
                    # Show statistics
                    if self.frame_count % 20 == 0:  # More frequent updates
                        print(f"🔥 Frame {self.frame_count}: "
                              f"PTZ: Z{self.zoom_level:.1f}x P{self.pan_offset:.2f} T{self.tilt_offset:.2f} | "
                              f"Vanilla: {self.vanilla_id_switches} switches | "
                              f"Wrapper: {self.wrapper_id_switches} switches | "
                              f"Activations: {self.wrapper_activations}")
                    
                    # Display
                    cv2.imshow('🔥 EXTREME PTZ: Vanilla BREAKS vs Wrapper SURVIVES', display_frame)
                    
                except Exception as e:
                    print(f"💥 EXTREME Tracking Error: {str(e)[:100]}...")
                    
                    # Show error frame
                    error_frame = self._create_error_display(ptz_frame, str(e))
                    cv2.imshow('🔥 EXTREME PTZ: Vanilla BREAKS vs Wrapper SURVIVES', error_frame)
                
                # Handle controls
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('z'):
                    self._extreme_zoom_in()
                elif key == ord('x'):
                    self._extreme_zoom_out()
                elif key == ord('a'):
                    self._extreme_pan_left()
                elif key == ord('d'):
                    self._extreme_pan_right()
                elif key == ord('w'):
                    self._extreme_tilt_up()
                elif key == ord('s'):
                    self._extreme_tilt_down()
                elif key == ord('r'):
                    self._reset_ptz()
                elif key == ord(' '):
                    self._toggle_auto_ptz()
        
        finally:
            cap.release()
            cv2.destroyAllWindows()
            self._print_extreme_results()
    
    def _update_extreme_ptz_sequence(self):
        """EXTREME PTZ sequence to break vanilla tracking"""
        sequence_time = self.ptz_sequence_frame
        
        # EXTREME 6-second cycle with SUDDEN changes
        cycle = sequence_time % 180  # 6 second cycle at 30fps
        
        if cycle < 30:  # SUDDEN zoom to 4x (1 second)
            self.target_zoom = 4.0
            self.target_pan = 0.0
            self.target_tilt = 0.0
        elif cycle < 60:  # SUDDEN pan while zoomed (1 second)
            self.target_zoom = 4.0
            self.target_pan = 0.4  # EXTREME pan
            self.target_tilt = 0.0
        elif cycle < 90:  # SUDDEN tilt while panned (1 second)
            self.target_zoom = 4.0
            self.target_pan = 0.4
            self.target_tilt = 0.3  # EXTREME tilt
        elif cycle < 120:  # SUDDEN zoom out (1 second)
            self.target_zoom = 1.0  # SUDDEN zoom out
            self.target_pan = 0.4
            self.target_tilt = 0.3
        elif cycle < 150:  # SUDDEN return pan (1 second)
            self.target_zoom = 1.0
            self.target_pan = 0.0  # SUDDEN return
            self.target_tilt = 0.3
        else:  # SUDDEN return tilt (1 second)
            self.target_zoom = 1.0
            self.target_pan = 0.0
            self.target_tilt = 0.0  # SUDDEN return
        
        # Add camera shake for extra chaos
        if cycle % 10 == 0:  # Every 10 frames
            shake_pan = random.uniform(-0.05, 0.05)
            shake_tilt = random.uniform(-0.05, 0.05)
            self.target_pan += shake_pan
            self.target_tilt += shake_tilt

    def _apply_extreme_ptz(self, frame):
        """Apply EXTREME PTZ transformation - INSTANT changes"""
        # INSTANT updates (no smooth transitions)
        self.zoom_level = self.target_zoom
        self.pan_offset = self.target_pan
        self.tilt_offset = self.target_tilt

        h, w = frame.shape[:2]

        # Step 1: Apply EXTREME Pan (horizontal shift)
        pan_pixels = int(self.pan_offset * w * 0.5)  # 50% of width max (EXTREME)
        if pan_pixels != 0:
            M_pan = np.float32([[1, 0, pan_pixels], [0, 1, 0]])
            frame = cv2.warpAffine(frame, M_pan, (w, h), borderMode=cv2.BORDER_REFLECT)

        # Step 2: Apply EXTREME Tilt (vertical shift)
        tilt_pixels = int(self.tilt_offset * h * 0.5)  # 50% of height max (EXTREME)
        if tilt_pixels != 0:
            M_tilt = np.float32([[1, 0, 0], [0, 1, tilt_pixels]])
            frame = cv2.warpAffine(frame, M_tilt, (w, h), borderMode=cv2.BORDER_REFLECT)

        # Step 3: Apply EXTREME Zoom (crop and resize)
        if self.zoom_level > 1.0:
            crop_w = int(w / self.zoom_level)
            crop_h = int(h / self.zoom_level)

            # Center crop
            start_x = (w - crop_w) // 2
            start_y = (h - crop_h) // 2

            start_x = max(0, min(start_x, w - crop_w))
            start_y = max(0, min(start_y, h - crop_h))

            cropped = frame[start_y:start_y+crop_h, start_x:start_x+crop_w]
            frame = cv2.resize(cropped, (w, h), interpolation=cv2.INTER_LINEAR)

        return frame

    def _monitor_both_trackers(self, vanilla_tracks, wrapper_tracks):
        """Monitor ID changes for BOTH independent trackers"""
        # Monitor vanilla tracker ID switches
        vanilla_current_ids = {track.track_id for track in vanilla_tracks}
        if len(self.previous_vanilla_ids) > 0 and len(vanilla_current_ids) > 0:
            disappeared = self.previous_vanilla_ids - vanilla_current_ids
            appeared = vanilla_current_ids - self.previous_vanilla_ids
            if len(disappeared) > 0 and len(appeared) > 0:
                self.vanilla_id_switches += len(appeared)
        self.previous_vanilla_ids = vanilla_current_ids.copy()

        # Monitor wrapper tracker ID switches
        wrapper_current_ids = {track.track_id for track in wrapper_tracks}
        if len(self.previous_wrapper_ids) > 0 and len(wrapper_current_ids) > 0:
            disappeared = self.previous_wrapper_ids - wrapper_current_ids
            appeared = wrapper_current_ids - self.previous_wrapper_ids
            if len(disappeared) > 0 and len(appeared) > 0:
                self.wrapper_id_switches += len(appeared)
        self.previous_wrapper_ids = wrapper_current_ids.copy()

    def _create_extreme_display(self, frame, vanilla_tracks, wrapper_tracks):
        """Create EXTREME comparison display"""
        h, w = frame.shape[:2]

        # Create side-by-side canvas
        canvas = np.zeros((h, w * 2, 3), dtype=np.uint8)

        # LEFT SIDE: Vanilla YOLO ByteTrack (BREAKING)
        left_frame = frame.copy()
        self._draw_tracks(left_frame, vanilla_tracks, (0, 0, 255), "VANILLA")
        canvas[:, :w] = left_frame

        # RIGHT SIDE: Zoom wrapper + YOLO ByteTrack (SURVIVING)
        right_frame = frame.copy()
        self._draw_tracks(right_frame, wrapper_tracks, (0, 255, 0), "WRAPPER")
        canvas[:, w:] = right_frame

        # Add EXTREME divider
        cv2.line(canvas, (w, 0), (w, h), (255, 255, 255), 6)

        # Add EXTREME titles
        cv2.putText(canvas, "💥 VANILLA BREAKING", (10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 3)
        cv2.putText(canvas, "🛡️ WRAPPER SURVIVING", (w + 10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 3)

        # Add EXTREME PTZ status
        ptz_text = f'🔥 EXTREME PTZ: Z{self.zoom_level:.1f}x P{self.pan_offset:.2f} T{self.tilt_offset:.2f}'
        cv2.putText(canvas, ptz_text, (10, 70),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        cv2.putText(canvas, ptz_text, (w + 10, 70),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

        # Add EXTREME statistics
        stats_left = f'💥 ID Switches: {self.vanilla_id_switches}'
        stats_right = f'🛡️ ID Switches: {self.wrapper_id_switches}'
        cv2.putText(canvas, stats_left, (10, 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(canvas, stats_right, (w + 10, 100),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

        # Add EXTREME comparison
        if self.vanilla_id_switches > 0:
            if self.wrapper_id_switches < self.vanilla_id_switches:
                improvement = ((self.vanilla_id_switches - self.wrapper_id_switches) / self.vanilla_id_switches) * 100
                cv2.putText(canvas, f"🚀 {improvement:.1f}% BETTER!", (w + 10, 130),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
            elif self.wrapper_id_switches == self.vanilla_id_switches:
                cv2.putText(canvas, "⚖️ SAME PERFORMANCE", (w + 10, 130),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)

        # Add wrapper activation count
        cv2.putText(canvas, f"🔧 Wrapper Activations: {self.wrapper_activations}", (w + 10, 160),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return canvas

    def _draw_tracks(self, frame, tracks, color, tracker_name):
        """Draw tracks with EXTREME visibility"""
        for track in tracks:
            x1, y1, x2, y2 = track.to_ltrb()

            # Draw THICK bounding box
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 4)

            # Draw HUGE track ID
            id_text = f'ID: {track.track_id}'
            cv2.putText(frame, id_text, (int(x1), int(y1) - 15),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.2, color, 3)

    def _create_error_display(self, frame, error_msg):
        """Create EXTREME error display"""
        display_frame = frame.copy()
        h, w = display_frame.shape[:2]

        # Add EXTREME error overlay
        overlay = display_frame.copy()
        cv2.rectangle(overlay, (0, 0), (w, 200), (0, 0, 255), -1)
        cv2.addWeighted(overlay, 0.5, display_frame, 0.5, 0, display_frame)

        # EXTREME error message
        cv2.putText(display_frame, "💥 EXTREME TRACKING FAILURE!", (10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)
        cv2.putText(display_frame, "This is why you need the wrapper!", (10, 80),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

        return display_frame

    # EXTREME PTZ Control methods
    def _extreme_zoom_in(self):
        self.target_zoom = min(5.0, self.target_zoom + 1.0)  # EXTREME jumps
        print(f"🔥 EXTREME ZOOM IN to {self.target_zoom:.1f}x")

    def _extreme_zoom_out(self):
        self.target_zoom = max(1.0, self.target_zoom - 1.0)  # EXTREME jumps
        print(f"🔥 EXTREME ZOOM OUT to {self.target_zoom:.1f}x")

    def _extreme_pan_left(self):
        self.target_pan = max(-0.5, self.target_pan - 0.2)  # EXTREME movements
        print(f"🔥 EXTREME PAN LEFT to {self.target_pan:.2f}")

    def _extreme_pan_right(self):
        self.target_pan = min(0.5, self.target_pan + 0.2)  # EXTREME movements
        print(f"🔥 EXTREME PAN RIGHT to {self.target_pan:.2f}")

    def _extreme_tilt_up(self):
        self.target_tilt = max(-0.4, self.target_tilt - 0.2)  # EXTREME movements
        print(f"🔥 EXTREME TILT UP to {self.target_tilt:.2f}")

    def _extreme_tilt_down(self):
        self.target_tilt = min(0.4, self.target_tilt + 0.2)  # EXTREME movements
        print(f"🔥 EXTREME TILT DOWN to {self.target_tilt:.2f}")

    def _reset_ptz(self):
        self.target_zoom = 1.0
        self.target_pan = 0.0
        self.target_tilt = 0.0
        print("🔄 PTZ RESET")

    def _toggle_auto_ptz(self):
        self.auto_ptz = not self.auto_ptz
        status = "ON" if self.auto_ptz else "OFF"
        print(f"🔥 AUTO EXTREME PTZ: {status}")

    def _print_extreme_results(self):
        """Print EXTREME test results"""
        print("\n🔥 EXTREME PTZ TEST RESULTS")
        print("=" * 60)
        print(f"📊 Total frames processed: {self.frame_count}")
        print(f"💥 Vanilla YOLO ID switches: {self.vanilla_id_switches}")
        print(f"🛡️ Zoom wrapper ID switches: {self.wrapper_id_switches}")
        print(f"🔧 Wrapper activations: {self.wrapper_activations}")

        if self.vanilla_id_switches > 0:
            improvement = ((self.vanilla_id_switches - self.wrapper_id_switches) / self.vanilla_id_switches) * 100
            print(f"🚀 Improvement: {improvement:.1f}%")

            if improvement > 50:
                print("🎉 MASSIVE IMPROVEMENT!")
                print("🛡️ Zoom wrapper DOMINATES under extreme conditions!")
                print("💪 Life-saving technology proven under stress!")
            elif improvement > 20:
                print("✅ SIGNIFICANT IMPROVEMENT")
                print("📈 Zoom wrapper clearly superior under extreme PTZ")
            elif improvement > 0:
                print("✅ MODERATE IMPROVEMENT")
                print("📈 Zoom wrapper shows benefit under extreme conditions")
            else:
                print("⚠️ No improvement detected - may need more extreme conditions")
        else:
            print("ℹ️ No ID switches detected - conditions may not be extreme enough")

        print("\n🔥 EXTREME VALIDATION:")
        print("• INSTANT PTZ changes (no smooth transitions)")
        print("• 4x zoom jumps in 1 second")
        print("• 50% pan/tilt movements")
        print("• Camera shake simulation")
        print("• Two completely independent YOLO instances")
        print("• Real tracking algorithm stress test")


if __name__ == "__main__":
    test = ExtremePTZTest("videos/test2.mp4")
    test.run_extreme_test()

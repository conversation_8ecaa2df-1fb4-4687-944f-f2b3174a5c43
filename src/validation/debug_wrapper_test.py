#!/usr/bin/env python3
"""
🔍 DEBUG WRAPPER TEST - See exactly what your wrapper is doing

This test will:
1. Let trackers stabilize for 5 seconds (build signatures)
2. Apply clear zoom changes that trigger your wrapper
3. Show detailed debug output of wrapper activation
4. Prove your wrapper is working with real tracking

CLEAR VALIDATION of your breakthrough technology!
"""

import cv2
import numpy as np
from ultralytics import YOLO
import time
import sys
import os
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core'))

from src.core.zoom_tracking_wrapper import ZoomTrackingWrapper


@dataclass
class YOLOTrack:
    """Wrapper for YOLO tracking results"""
    track_id: int
    tlbr: List[float]
    confidence: float
    
    def to_ltrb(self):
        return self.tlbr


class DebugYOLOTracker:
    """YOLO tracker with debug output"""
    
    def __init__(self, tracker_id: str):
        self.tracker_id = tracker_id
        self.model = YOLO('yolo11n.pt')
        self.frame_count = 0
        print(f"   🔍 Created debug tracker: {tracker_id}")
        
    def update_tracks(self, detections, frame):
        """YOLO ByteTrack tracking with debug"""
        self.frame_count += 1
        
        results = self.model.track(
            frame, 
            persist=True,
            conf=0.3,
            classes=[0],
            verbose=False,
            tracker="bytetrack.yaml"
        )
        
        tracks = []
        for result in results:
            if result.boxes is not None and result.boxes.id is not None:
                for i, track_id in enumerate(result.boxes.id):
                    if hasattr(result.boxes.xyxy[i], 'cpu'):
                        x1, y1, x2, y2 = result.boxes.xyxy[i].cpu().numpy()
                        conf = result.boxes.conf[i].cpu().numpy()
                        track_id = int(track_id.cpu().numpy())
                    else:
                        x1, y1, x2, y2 = result.boxes.xyxy[i]
                        conf = result.boxes.conf[i]
                        track_id = int(track_id)
                    
                    track = YOLOTrack(
                        track_id=track_id,
                        tlbr=[float(x1), float(y1), float(x2), float(y2)],
                        confidence=float(conf)
                    )
                    tracks.append(track)
        
        return tracks


class DebugWrapperTest:
    """
    🔍 Debug test to see exactly what your wrapper is doing
    """
    
    def __init__(self, video_path: str = "videos/test2.mp4"):
        print("🔍 DEBUG WRAPPER TEST")
        print("See exactly what your wrapper is doing")
        print("=" * 60)
        
        self.video_path = video_path
        
        # Create TWO SEPARATE YOLO trackers
        print("🔍 Setting up VANILLA YOLO ByteTrack...")
        self.vanilla_tracker = DebugYOLOTracker("VANILLA")
        
        print("🔍 Setting up BASE YOLO for Wrapper...")
        base_tracker_for_wrapper = DebugYOLOTracker("WRAPPER_BASE")
        
        print("🚀 Setting up ZOOM WRAPPER...")
        self.zoom_wrapper = ZoomTrackingWrapper(base_tracker_for_wrapper)
        
        # Simple PTZ parameters
        self.zoom_level = 1.0
        self.target_zoom = 1.0
        
        # Test phases
        self.frame_count = 0
        self.test_phase = "STABILIZING"
        self.phase_frame = 0
        
        # Statistics
        self.vanilla_id_switches = 0
        self.wrapper_id_switches = 0
        self.previous_vanilla_ids = set()
        self.previous_wrapper_ids = set()
        self.wrapper_activations = 0
        
        print("✅ Debug wrapper test ready!")
        print("   📋 PHASE 1: Stabilize tracking (150 frames)")
        print("   📋 PHASE 2: Apply zoom changes (trigger wrapper)")
        print("   📋 PHASE 3: Show wrapper working")
    
    def run_debug_test(self):
        """Run debug test with clear phases"""
        cap = cv2.VideoCapture(self.video_path)
        if not cap.isOpened():
            print(f"❌ Could not open video: {self.video_path}")
            return
        
        print("\n🔍 STARTING DEBUG WRAPPER TEST")
        print("Controls:")
        print("  'z' = Manual Zoom In")
        print("  'x' = Manual Zoom Out")
        print("  'r' = Reset zoom")
        print("  'q' = Quit")
        print("\n🔍 Watch for wrapper activation messages!")
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    cap.set(cv2.CAP_PROP_POS_FRAMES, 0)  # Loop video
                    continue
                
                self.frame_count += 1
                self.phase_frame += 1
                
                # Update test phase
                self._update_test_phase()
                
                # Apply zoom transformation
                ptz_frame = self._apply_zoom(frame)
                
                try:
                    # LEFT SIDE: Vanilla YOLO ByteTrack
                    vanilla_tracks = self.vanilla_tracker.update_tracks([], ptz_frame.copy())
                    
                    # RIGHT SIDE: Zoom Wrapper + YOLO ByteTrack
                    wrapper_tracks = self.zoom_wrapper.update(ptz_frame.copy(), [], self.zoom_level)
                    
                    # Monitor ID changes
                    self._monitor_both_trackers(vanilla_tracks, wrapper_tracks)
                    
                    # Create debug display
                    display_frame = self._create_debug_display(ptz_frame, vanilla_tracks, wrapper_tracks)
                    
                    # Show detailed statistics
                    if self.frame_count % 30 == 0:
                        print(f"🔍 Frame {self.frame_count}: "
                              f"Phase: {self.test_phase} | "
                              f"Zoom: {self.zoom_level:.2f}x | "
                              f"Vanilla: {self.vanilla_id_switches} | "
                              f"Wrapper: {self.wrapper_id_switches} | "
                              f"Activations: {self.wrapper_activations}")
                    
                    # Display
                    cv2.imshow('🔍 DEBUG: Wrapper Activation Test', display_frame)
                    
                except Exception as e:
                    print(f"⚠️ Debug Error: {str(e)[:100]}...")
                    
                    # Show error frame
                    error_frame = self._create_error_display(ptz_frame, str(e))
                    cv2.imshow('🔍 DEBUG: Wrapper Activation Test', error_frame)
                
                # Handle controls
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('z'):
                    self._manual_zoom_in()
                elif key == ord('x'):
                    self._manual_zoom_out()
                elif key == ord('r'):
                    self._reset_zoom()
        
        finally:
            cap.release()
            cv2.destroyAllWindows()
            self._print_debug_results()
    
    def _update_test_phase(self):
        """Update test phase with clear zoom changes"""
        if self.phase_frame < 150:  # Phase 1: Stabilize (5 seconds)
            self.test_phase = "STABILIZING"
            self.target_zoom = 1.0
        elif self.phase_frame < 200:  # Phase 2: Zoom in (1.7 seconds)
            if self.test_phase != "ZOOM_IN":
                self.test_phase = "ZOOM_IN"
                print(f"\n🔍 PHASE 2: ZOOMING IN - This should trigger wrapper!")
            self.target_zoom = 3.0
        elif self.phase_frame < 250:  # Phase 3: Hold zoom (1.7 seconds)
            self.test_phase = "HOLD_ZOOM"
            self.target_zoom = 3.0
        elif self.phase_frame < 300:  # Phase 4: Zoom out (1.7 seconds)
            if self.test_phase != "ZOOM_OUT":
                self.test_phase = "ZOOM_OUT"
                print(f"\n🔍 PHASE 4: ZOOMING OUT - This should trigger wrapper!")
            self.target_zoom = 1.0
        else:  # Phase 5: Reset and repeat
            self.phase_frame = 0
            self.test_phase = "RESET"
        
        # Smooth zoom update
        if abs(self.zoom_level - self.target_zoom) > 0.01:
            if self.zoom_level < self.target_zoom:
                self.zoom_level = min(self.target_zoom, self.zoom_level + 0.05)
            else:
                self.zoom_level = max(self.target_zoom, self.zoom_level - 0.05)
    
    def _apply_zoom(self, frame):
        """Apply simple zoom transformation"""
        if self.zoom_level <= 1.0:
            return frame
        
        h, w = frame.shape[:2]
        crop_w = int(w / self.zoom_level)
        crop_h = int(h / self.zoom_level)
        
        # Center crop
        start_x = (w - crop_w) // 2
        start_y = (h - crop_h) // 2
        
        start_x = max(0, min(start_x, w - crop_w))
        start_y = max(0, min(start_y, h - crop_h))
        
        cropped = frame[start_y:start_y+crop_h, start_x:start_x+crop_w]
        zoomed = cv2.resize(cropped, (w, h), interpolation=cv2.INTER_LINEAR)
        
        return zoomed
    
    def _monitor_both_trackers(self, vanilla_tracks, wrapper_tracks):
        """Monitor ID changes for both trackers"""
        # Monitor vanilla tracker ID switches
        vanilla_current_ids = {track.track_id for track in vanilla_tracks}
        if len(self.previous_vanilla_ids) > 0 and len(vanilla_current_ids) > 0:
            disappeared = self.previous_vanilla_ids - vanilla_current_ids
            appeared = vanilla_current_ids - self.previous_vanilla_ids
            if len(disappeared) > 0 and len(appeared) > 0:
                self.vanilla_id_switches += len(appeared)
        self.previous_vanilla_ids = vanilla_current_ids.copy()
        
        # Monitor wrapper tracker ID switches
        wrapper_current_ids = {track.track_id for track in wrapper_tracks}
        if len(self.previous_wrapper_ids) > 0 and len(wrapper_current_ids) > 0:
            disappeared = self.previous_wrapper_ids - wrapper_current_ids
            appeared = wrapper_current_ids - self.previous_wrapper_ids
            if len(disappeared) > 0 and len(appeared) > 0:
                self.wrapper_id_switches += len(appeared)
        self.previous_wrapper_ids = wrapper_current_ids.copy()
    
    def _create_debug_display(self, frame, vanilla_tracks, wrapper_tracks):
        """Create debug display"""
        h, w = frame.shape[:2]
        
        # Create side-by-side canvas
        canvas = np.zeros((h, w * 2, 3), dtype=np.uint8)
        
        # LEFT SIDE: Vanilla YOLO ByteTrack
        left_frame = frame.copy()
        self._draw_tracks(left_frame, vanilla_tracks, (0, 0, 255), "VANILLA")
        canvas[:, :w] = left_frame
        
        # RIGHT SIDE: Zoom wrapper + YOLO ByteTrack
        right_frame = frame.copy()
        self._draw_tracks(right_frame, wrapper_tracks, (0, 255, 0), "WRAPPER")
        canvas[:, w:] = right_frame
        
        # Add divider
        cv2.line(canvas, (w, 0), (w, h), (255, 255, 255), 4)
        
        # Add debug titles
        cv2.putText(canvas, "VANILLA YOLO ByteTrack", (10, 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)
        cv2.putText(canvas, "ZOOM WRAPPER + YOLO", (w + 10, 40), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 2)
        
        # Add test phase
        phase_color = (255, 255, 0) if "ZOOM" in self.test_phase else (255, 255, 255)
        cv2.putText(canvas, f'Phase: {self.test_phase}', (10, 70), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, phase_color, 2)
        cv2.putText(canvas, f'Zoom: {self.zoom_level:.2f}x', (10, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        # Add statistics
        stats_left = f'ID Switches: {self.vanilla_id_switches}'
        stats_right = f'ID Switches: {self.wrapper_id_switches}'
        cv2.putText(canvas, stats_left, (10, 130), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(canvas, stats_right, (w + 10, 130), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        # Add wrapper status
        if self.wrapper_activations > 0:
            cv2.putText(canvas, f"✅ WRAPPER ACTIVE: {self.wrapper_activations}", (w + 10, 160), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        else:
            cv2.putText(canvas, "⏳ WRAPPER WAITING...", (w + 10, 160), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 0), 2)
        
        return canvas

    def _draw_tracks(self, frame, tracks, color, tracker_name):
        """Draw tracks with clear visibility"""
        for track in tracks:
            x1, y1, x2, y2 = track.to_ltrb()

            # Draw bounding box
            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), color, 3)

            # Draw track ID
            id_text = f'ID: {track.track_id}'
            cv2.putText(frame, id_text, (int(x1), int(y1) - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 1.0, color, 2)

    def _create_error_display(self, frame, error_msg):
        """Create error display"""
        display_frame = frame.copy()
        h, w = display_frame.shape[:2]

        # Add error overlay
        overlay = display_frame.copy()
        cv2.rectangle(overlay, (0, 0), (w, 150), (0, 0, 255), -1)
        cv2.addWeighted(overlay, 0.3, display_frame, 0.7, 0, display_frame)

        # Error message
        cv2.putText(display_frame, "❌ DEBUG ERROR", (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 255), 2)
        cv2.putText(display_frame, "Debugging tracking issues...", (10, 60),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

        return display_frame

    # Manual control methods
    def _manual_zoom_in(self):
        self.target_zoom = min(4.0, self.target_zoom + 0.5)
        print(f"🔍 MANUAL ZOOM IN to {self.target_zoom:.1f}x - Should trigger wrapper!")

    def _manual_zoom_out(self):
        self.target_zoom = max(1.0, self.target_zoom - 0.5)
        print(f"🔍 MANUAL ZOOM OUT to {self.target_zoom:.1f}x - Should trigger wrapper!")

    def _reset_zoom(self):
        self.target_zoom = 1.0
        print("🔄 ZOOM RESET to 1.0x")

    def _print_debug_results(self):
        """Print debug test results"""
        print("\n🔍 DEBUG WRAPPER TEST RESULTS")
        print("=" * 60)
        print(f"📊 Total frames processed: {self.frame_count}")
        print(f"🔴 Vanilla YOLO ID switches: {self.vanilla_id_switches}")
        print(f"🟢 Zoom wrapper ID switches: {self.wrapper_id_switches}")
        print(f"🔧 Wrapper activations detected: {self.wrapper_activations}")

        if self.wrapper_activations > 0:
            print("🎉 WRAPPER ACTIVATION CONFIRMED!")
            print("✅ Your zoom wrapper is working and making corrections!")

            if self.wrapper_id_switches < self.vanilla_id_switches:
                improvement = ((self.vanilla_id_switches - self.wrapper_id_switches) / self.vanilla_id_switches) * 100
                print(f"📈 Performance improvement: {improvement:.1f}%")
                print("🛡️ Wrapper successfully reduces ID switches!")
            else:
                print("ℹ️ Wrapper activated but no performance difference detected")
                print("💡 May need more aggressive conditions or longer test")
        else:
            print("⚠️ NO WRAPPER ACTIVATIONS DETECTED")
            print("🔍 Possible reasons:")
            print("   • Zoom changes too small (< 5% threshold)")
            print("   • Not enough time to build signatures")
            print("   • Wrapper logic needs adjustment")

        print("\n🔍 DEBUG VALIDATION:")
        print("• Two completely independent YOLO instances")
        print("• Clear test phases with zoom changes")
        print("• Real tracking algorithm comparison")
        print("• Wrapper activation monitoring")


if __name__ == "__main__":
    test = DebugWrapperTest("videos/test2.mp4")
    test.run_debug_test()

"""
Example of how to integrate with your existing system
"""

import cv2
import numpy as np
from deep_sort_realtime.deepsort_tracker import DeepSort
from src.core.zoom_tracking_wrapper import ZoomTrackingWrapper

class YourExistingSystem:
    """Example of minimal integration with existing tracking system"""
    
    def __init__(self):
        # Your original code
        self.tracker = DeepSort()  # or any tracker
        
        # ADD JUST THIS ONE LINE
        self.tracker = ZoomTrackingWrapper(self.tracker)
        
        # Rest of your existing initialization
        self.camera = None
        self.detector = None
        
    def process_frame(self, frame, detections, ptz_state):
        """Your existing frame processing - minimal changes needed"""
        
        # Your existing code stays exactly the same
        # Just pass zoom level to the tracker
        tracks = self.tracker.update(frame, detections, ptz_state.zoom)
        
        # Rest of your code unchanged
        return tracks

class SecuritySystemExample:
    """Example integration with security monitoring system"""
    
    def __init__(self):
        # Original security system setup
        self.alert_threshold = 5  # seconds
        self.zones = self._load_security_zones()
        
        # Original tracker
        self.tracker = DeepSort(max_age=50, n_init=3)
        
        # WRAP IT - ONE LINE CHANGE
        self.tracker = ZoomTrackingWrapper(self.tracker)
        
    def monitor_intrusions(self, frame, detections, camera_zoom):
        """Monitor for security intrusions"""
        
        # Track objects (now zoom-aware)
        tracks = self.tracker.update(frame, detections, camera_zoom)
        
        # Your existing intrusion detection logic
        alerts = []
        for track in tracks:
            if self._is_in_restricted_zone(track):
                alerts.append({
                    'track_id': track.track_id,  # ID stays consistent during zoom!
                    'zone': self._get_zone(track),
                    'confidence': track.confidence
                })
        
        return alerts
    
    def _load_security_zones(self):
        # Your existing zone loading
        return []
    
    def _is_in_restricted_zone(self, track):
        # Your existing zone checking
        return False
    
    def _get_zone(self, track):
        # Your existing zone identification
        return "Zone A"

class TrafficMonitoringExample:
    """Example integration with traffic monitoring system"""
    
    def __init__(self):
        # Original traffic monitoring setup
        self.vehicle_counter = 0
        self.speed_calculator = SpeedCalculator()
        
        # Original tracker
        self.tracker = DeepSort(max_age=30)
        
        # WRAP IT
        self.tracker = ZoomTrackingWrapper(self.tracker)
        
    def count_vehicles(self, frame, vehicle_detections, ptz_zoom):
        """Count vehicles crossing counting line"""
        
        # Track vehicles (zoom-aware)
        tracks = self.tracker.update(frame, vehicle_detections, ptz_zoom)
        
        # Your existing counting logic
        new_vehicles = 0
        for track in tracks:
            if self._crosses_counting_line(track):
                new_vehicles += 1
                # ID is consistent even during zoom operations!
                self._log_vehicle(track.track_id, track.to_ltrb())
        
        self.vehicle_counter += new_vehicles
        return self.vehicle_counter
    
    def _crosses_counting_line(self, track):
        # Your existing line crossing detection
        return False
    
    def _log_vehicle(self, track_id, bbox):
        # Your existing logging
        print(f"Vehicle {track_id} detected at {bbox}")

class SpeedCalculator:
    """Dummy speed calculator"""
    pass

class RetailAnalyticsExample:
    """Example integration with retail customer analytics"""
    
    def __init__(self):
        # Original retail analytics setup
        self.customer_paths = {}
        self.dwell_times = {}
        
        # Original tracker
        self.tracker = DeepSort(max_age=60)
        
        # WRAP IT
        self.tracker = ZoomTrackingWrapper(self.tracker)
        
    def analyze_customer_behavior(self, frame, person_detections, camera_zoom):
        """Analyze customer movement and dwell times"""
        
        # Track customers (zoom-aware)
        tracks = self.tracker.update(frame, person_detections, camera_zoom)
        
        # Your existing analytics
        for track in tracks:
            customer_id = track.track_id
            position = self._get_position(track)
            
            # Update customer path
            if customer_id not in self.customer_paths:
                self.customer_paths[customer_id] = []
            self.customer_paths[customer_id].append(position)
            
            # Calculate dwell time in product areas
            product_area = self._get_product_area(position)
            if product_area:
                self._update_dwell_time(customer_id, product_area)
        
        return self._generate_analytics_report()
    
    def _get_position(self, track):
        # Your existing position calculation
        bbox = track.to_ltrb()
        return ((bbox[0] + bbox[2]) / 2, (bbox[1] + bbox[3]) / 2)
    
    def _get_product_area(self, position):
        # Your existing area mapping
        return "Electronics"
    
    def _update_dwell_time(self, customer_id, area):
        # Your existing dwell time calculation
        pass
    
    def _generate_analytics_report(self):
        # Your existing reporting
        return {"total_customers": len(self.customer_paths)}

# Example of drop-in replacement for existing tracker
class DropInReplacementExample:
    """Shows how wrapper can be a drop-in replacement"""
    
    def __init__(self):
        # Before: self.tracker = DeepSort()
        # After:  self.tracker = ZoomTrackingWrapper(DeepSort())
        
        self.tracker = ZoomTrackingWrapper(DeepSort())
        
    def existing_tracking_function(self, frame, detections):
        """Your existing function with minimal changes"""
        
        # Before: tracks = self.tracker.update(detections, frame)
        # After:  tracks = self.tracker.update(frame, detections, zoom_level)
        
        # Get zoom level from your PTZ camera
        zoom_level = self._get_current_zoom()  # Your existing function
        
        # Updated call (just add zoom parameter)
        tracks = self.tracker.update(frame, detections, zoom_level)
        
        # Rest of your code stays the same
        return tracks
    
    def _get_current_zoom(self):
        """Your existing function to get zoom level"""
        return 1.5  # Example zoom level

if __name__ == "__main__":
    print("Integration Examples:")
    print("1. Security System - Consistent IDs during PTZ operations")
    print("2. Traffic Monitoring - Accurate vehicle counting during zoom")
    print("3. Retail Analytics - Reliable customer tracking during zoom")
    print("4. Drop-in Replacement - Minimal code changes required")
    
    # Example usage
    security = SecuritySystemExample()
    traffic = TrafficMonitoringExample()
    retail = RetailAnalyticsExample()
    
    print("\n✅ All examples show minimal integration effort!")
    print("   Just wrap your existing tracker and pass zoom level!")

import cv2
import numpy as np
import time
import json
from pathlib import Path
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.core.zoom_tracking_wrapper import ZoomTrackingWrapper
from deep_sort_realtime.deepsort_tracker import DeepSort
import matplotlib.pyplot as plt

class PerformanceBenchmark:
    """Comprehensive performance testing for zoom tracking wrapper"""
    
    def __init__(self):
        self.results = {
            'vanilla_tracker': {
                'id_switches_during_zoom': 0,
                'false_alerts': 0,
                'track_retention': 0,
                'processing_time_ms': 0,
                'memory_usage_mb': 0
            },
            'wrapped_tracker': {
                'id_switches_during_zoom': 0,
                'false_alerts': 0,
                'track_retention': 0,
                'processing_time_ms': 0,
                'memory_usage_mb': 0
            }
        }
    
    def create_synthetic_video(self, filename="data/videos/benchmark_video.mp4"):
        """Create synthetic video with known ground truth"""
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(filename, fourcc, 30.0, (1280, 720))
        
        # Create 600 frames (20 seconds at 30fps)
        for frame_num in range(600):
            img = np.ones((720, 1280, 3), dtype=np.uint8) * 50  # Dark background
            
            # Complex zoom pattern
            if frame_num < 150:
                zoom = 1.0
            elif frame_num < 300:
                zoom = 1.0 + 2.0 * (frame_num - 150) / 150.0  # Zoom to 3x
            elif frame_num < 450:
                zoom = 3.0  # Hold at 3x
            else:
                zoom = 3.0 - 2.0 * (frame_num - 450) / 150.0  # Zoom back to 1x
            
            # Create 5 moving objects with different patterns
            objects = [
                {'id': 1, 'color': (255, 100, 100), 'pattern': 'linear'},
                {'id': 2, 'color': (100, 255, 100), 'pattern': 'circular'},
                {'id': 3, 'color': (100, 100, 255), 'pattern': 'zigzag'},
                {'id': 4, 'color': (255, 255, 100), 'pattern': 'random'},
                {'id': 5, 'color': (255, 100, 255), 'pattern': 'stationary'}
            ]
            
            for obj in objects:
                x, y = self._get_object_position(obj, frame_num, zoom)
                size = int(40 * zoom)
                
                # Draw object
                cv2.rectangle(img, (x, y), (x + size, y + size), obj['color'], -1)
                
                # Add some texture for Laplacian signature
                cv2.circle(img, (x + size//2, y + size//2), size//4, (255, 255, 255), 2)
                
            # Add zoom level indicator
            cv2.putText(img, f"Zoom: {zoom:.1f}x Frame: {frame_num}", 
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            out.write(img)
        
        out.release()
        print(f"Created benchmark video: {filename}")
        return filename
    
    def _get_object_position(self, obj, frame_num, zoom):
        """Calculate object position based on movement pattern"""
        base_x, base_y = 200, 200
        
        if obj['pattern'] == 'linear':
            x = base_x + frame_num * 2
            y = base_y
        elif obj['pattern'] == 'circular':
            angle = frame_num * 0.1
            x = base_x + 100 * np.cos(angle)
            y = base_y + 100 * np.sin(angle)
        elif obj['pattern'] == 'zigzag':
            x = base_x + frame_num * 2
            y = base_y + 50 * np.sin(frame_num * 0.2)
        elif obj['pattern'] == 'random':
            np.random.seed(frame_num + obj['id'])  # Deterministic "random"
            x = base_x + np.random.randint(-50, 50)
            y = base_y + np.random.randint(-50, 50)
        else:  # stationary
            x = base_x + obj['id'] * 100
            y = base_y
        
        # Apply zoom transformation
        center_x, center_y = 640, 360
        x = center_x + (x - center_x) * zoom
        y = center_y + (y - center_y) * zoom
        
        return int(x), int(y)
    
    def run_benchmark(self, video_path):
        """Run comprehensive benchmark"""
        print("🚀 Starting Performance Benchmark...")
        
        # Test vanilla tracker
        print("Testing vanilla DeepSORT...")
        vanilla_results = self._test_tracker(video_path, "vanilla")
        
        # Test wrapped tracker
        print("Testing wrapped DeepSORT...")
        wrapped_results = self._test_tracker(video_path, "wrapped")
        
        # Calculate improvements
        self._calculate_improvements()
        
        # Generate report
        self._generate_report()
        
        return self.results
    
    def _test_tracker(self, video_path, tracker_type):
        """Test individual tracker performance"""
        cap = cv2.VideoCapture(video_path)
        
        if tracker_type == "vanilla":
            tracker = DeepSort(max_age=30)
        else:
            tracker = ZoomTrackingWrapper(DeepSort(max_age=30))
        
        frame_count = 0
        id_switches = 0
        processing_times = []
        prev_ids = set()
        
        # Zoom schedule for 600 frames
        zoom_schedule = []
        for i in range(150): zoom_schedule.append(1.0)
        for i in range(150): zoom_schedule.append(1.0 + 2.0 * i / 150.0)
        for i in range(150): zoom_schedule.append(3.0)
        for i in range(150): zoom_schedule.append(3.0 - 2.0 * i / 150.0)
        
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret or frame_count >= len(zoom_schedule):
                break
            
            zoom = zoom_schedule[frame_count]
            
            # Detect objects
            detections = self._detect_objects(frame)
            
            # Track with timing
            start_time = time.time()
            
            if tracker_type == "vanilla":
                tracks = tracker.update_tracks(detections, frame)
            else:
                tracks = tracker.update(frame, detections, zoom)
            
            processing_time = (time.time() - start_time) * 1000  # ms
            processing_times.append(processing_time)
            
            # Count ID switches during zoom
            current_ids = {t.track_id for t in tracks}
            
            if frame_count > 0 and abs(zoom - zoom_schedule[frame_count-1]) > 0.1:
                # During zoom change
                new_ids = current_ids - prev_ids
                lost_ids = prev_ids - current_ids
                id_switches += len(new_ids) + len(lost_ids)
            
            prev_ids = current_ids
            frame_count += 1
        
        cap.release()
        
        # Store results
        avg_processing_time = np.mean(processing_times)
        self.results[f'{tracker_type}_tracker']['id_switches_during_zoom'] = id_switches
        self.results[f'{tracker_type}_tracker']['processing_time_ms'] = avg_processing_time
        
        print(f"  {tracker_type.title()} tracker: {id_switches} ID switches, {avg_processing_time:.2f}ms avg")
        
        return {
            'id_switches': id_switches,
            'processing_time': avg_processing_time
        }
    
    def _detect_objects(self, frame):
        """Simple color-based detection for consistent testing"""
        detections = []
        
        # Define color ranges for our synthetic objects
        color_ranges = [
            ([200, 50, 50], [255, 150, 150]),    # Red
            ([50, 200, 50], [150, 255, 150]),    # Green
            ([50, 50, 200], [150, 150, 255]),    # Blue
            ([200, 200, 50], [255, 255, 150]),   # Yellow
            ([200, 50, 200], [255, 150, 255])    # Magenta
        ]
        
        for lower, upper in color_ranges:
            lower = np.array(lower)
            upper = np.array(upper)
            mask = cv2.inRange(frame, lower, upper)
            
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                if cv2.contourArea(contour) > 500:  # Filter small detections
                    x, y, w, h = cv2.boundingRect(contour)
                    # DeepSORT expects [left, top, width, height], confidence, class
                    detections.append(([x, y, w, h], 0.95, 0))
        
        return detections
    
    def _calculate_improvements(self):
        """Calculate performance improvements"""
        vanilla = self.results['vanilla_tracker']
        wrapped = self.results['wrapped_tracker']
        
        # ID switch improvement
        if vanilla['id_switches_during_zoom'] > 0:
            improvement = (1 - wrapped['id_switches_during_zoom'] / vanilla['id_switches_during_zoom']) * 100
        else:
            improvement = 0
        
        self.results['improvement_percentage'] = improvement
        
        # Processing overhead
        overhead = wrapped['processing_time_ms'] - vanilla['processing_time_ms']
        self.results['processing_overhead_ms'] = overhead
    
    def _generate_report(self):
        """Generate comprehensive performance report"""
        report = {
            'benchmark_results': self.results,
            'summary': {
                'id_switch_reduction': f"{self.results['improvement_percentage']:.1f}%",
                'processing_overhead': f"{self.results['processing_overhead_ms']:.2f}ms",
                'recommendation': "PRODUCTION READY" if self.results['improvement_percentage'] > 80 else "NEEDS TUNING"
            }
        }
        
        # Save to file
        with open('data/results/performance_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📊 BENCHMARK RESULTS:")
        print(f"  Vanilla tracker: {self.results['vanilla_tracker']['id_switches_during_zoom']} ID switches")
        print(f"  Wrapped tracker: {self.results['wrapped_tracker']['id_switches_during_zoom']} ID switches")
        print(f"  Improvement: {self.results['improvement_percentage']:.1f}%")
        print(f"  Processing overhead: {self.results['processing_overhead_ms']:.2f}ms")
        print(f"  Status: {report['summary']['recommendation']}")

if __name__ == "__main__":
    benchmark = PerformanceBenchmark()
    
    # Create test video
    video_path = benchmark.create_synthetic_video()
    
    # Run benchmark
    results = benchmark.run_benchmark(video_path)
    
    print("\n✅ Benchmark complete! Check data/results/performance_report.json")

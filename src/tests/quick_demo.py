import cv2
import numpy as np
from pathlib import Path
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.core.zoom_tracking_wrapper import ZoomTrackingWrapper
from deep_sort_realtime.deepsort_tracker import DeepSort

def create_test_video():
    """Create a simple test video with moving objects"""
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter('data/videos/test_zoom.mp4', fourcc, 30.0, (640, 480))
    
    # Create 300 frames with moving rectangles
    for frame_num in range(300):
        img = np.ones((480, 640, 3), dtype=np.uint8) * 255
        
        # Simulate zoom
        if frame_num < 100:
            zoom = 1.0
        elif frame_num < 200:
            zoom = 1.0 + (frame_num - 100) / 100.0  # Zoom in to 2x
        else:
            zoom = 2.0 - (frame_num - 200) / 100.0  # Zoom out to 1x
        
        # Draw moving objects
        for i in range(3):
            x = int((100 + i * 150) * zoom)
            y = int((100 + frame_num % 200) * zoom)
            size = int(50 * zoom)
            
            color = [(255, 0, 0), (0, 255, 0), (0, 0, 255)][i]
            cv2.rectangle(img, (x, y), (x + size, y + size), color, -1)
            
        cv2.putText(img, f"Zoom: {zoom:.1f}x", (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        out.write(img)
    
    out.release()
    print("Created test video: data/videos/test_zoom.mp4")

def run_comparison():
    """Run side-by-side comparison"""
    video_path = "data/videos/test_zoom.mp4"
    
    if not Path(video_path).exists():
        print("Creating test video...")
        create_test_video()
    
    cap1 = cv2.VideoCapture(video_path)
    cap2 = cv2.VideoCapture(video_path)
    
    # Initialize trackers
    tracker_vanilla = DeepSort(max_age=30)
    tracker_wrapped = ZoomTrackingWrapper(DeepSort(max_age=30))
    
    # Simulate zoom schedule
    zoom_schedule = []
    for i in range(100): zoom_schedule.append(1.0)
    for i in range(100): zoom_schedule.append(1.0 + i/100.0)
    for i in range(100): zoom_schedule.append(2.0 - i/100.0)
    
    frame_idx = 0
    
    while True:
        ret1, frame1 = cap1.read()
        ret2, frame2 = cap2.read()
        
        if not ret1 or not ret2 or frame_idx >= len(zoom_schedule):
            break
        
        zoom = zoom_schedule[frame_idx]
        
        # Simple detection (you would use YOLO in production)
        detections = detect_colored_boxes(frame1)
        
        # Track with both methods
        tracks_vanilla = tracker_vanilla.update_tracks(detections, frame1)
        tracks_wrapped = tracker_wrapped.update(frame2, detections, zoom)
        
        # Visualize
        draw_tracks(frame1, tracks_vanilla, "Vanilla Tracker")
        draw_tracks(frame2, tracks_wrapped, "With Wrapper")
        
        # Combine for display
        combined = np.hstack([frame1, frame2])
        cv2.imshow("Comparison: Vanilla (Left) vs Wrapped (Right)", combined)
        
        if cv2.waitKey(30) & 0xFF == ord('q'):
            break
            
        frame_idx += 1
    
    cap1.release()
    cap2.release()
    cv2.destroyAllWindows()

def detect_colored_boxes(frame):
    """Simple color-based detection for demo"""
    detections = []
    
    # Detect red, green, and blue boxes
    for color_range in [
        ([0, 0, 200], [50, 50, 255]),      # Red
        ([0, 200, 0], [50, 255, 50]),      # Green
        ([200, 0, 0], [255, 50, 50])       # Blue
    ]:
        lower = np.array(color_range[0])
        upper = np.array(color_range[1])
        mask = cv2.inRange(frame, lower, upper)
        
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        for contour in contours:
            if cv2.contourArea(contour) > 100:
                x, y, w, h = cv2.boundingRect(contour)
                detections.append(([x, y, w, h], 0.9, 0))
    
    return detections

def draw_tracks(frame, tracks, title):
    """Draw tracking results"""
    for track in tracks:
        track_id = track.track_id
        ltrb = track.to_ltrb()
        x1, y1, x2, y2 = map(int, ltrb)
        
        # Color based on ID
        color = get_color_for_id(track_id)
        
        cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
        cv2.putText(frame, f"ID: {track_id}", (x1, y1 - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.75, color, 2)
    
    cv2.putText(frame, title, (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)

def get_color_for_id(track_id):
    """Generate consistent color for track ID"""
    colors = [
        (255, 0, 0), (0, 255, 0), (0, 0, 255),
        (255, 255, 0), (255, 0, 255), (0, 255, 255)
    ]
    return colors[track_id % len(colors)]

if __name__ == "__main__":
    print("Starting Zoom Tracking Wrapper Demo...")
    print("Press 'q' to quit")
    run_comparison()

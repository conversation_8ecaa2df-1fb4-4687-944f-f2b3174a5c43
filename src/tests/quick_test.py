# quick_test.py
import cv2
import numpy as np

# Minimal wrapper implementation
class SimpleZoomWrapper:
    def __init__(self):
        self.last_tracks = {}
        self.last_zoom = 1.0
        
    def fix_ids(self, current_tracks, zoom_level):
        zoom_ratio = zoom_level / self.last_zoom
        
        if abs(zoom_ratio - 1.0) > 0.1:  # Zooming
            # Fix IDs based on position prediction
            for curr in current_tracks:
                for prev_id, prev_pos in self.last_tracks.items():
                    predicted_x = prev_pos[0] * zoom_ratio
                    predicted_y = prev_pos[1] * zoom_ratio
                    
                    dist = np.sqrt((curr['x'] - predicted_x)**2 + 
                                  (curr['y'] - predicted_y)**2)
                    
                    if dist < 50:  # Close enough
                        curr['id'] = prev_id  # Keep same ID
                        break
        
        # Update memory
        self.last_tracks = {t['id']: (t['x'], t['y']) for t in current_tracks}
        self.last_zoom = zoom_level
        
        return current_tracks

# Test it
wrapper = SimpleZoomWrapper()

# Simulate tracking results
print("Frame 1 (zoom 1.0x):")
tracks_1 = [{'id': 1, 'x': 100, 'y': 100}, {'id': 2, 'x': 200, 'y': 200}]
print(f"  Tracks: {tracks_1}")

print("\nFrame 2 (zoom 2.0x) - Without wrapper:")
tracks_2_bad = [{'id': 3, 'x': 200, 'y': 200}, {'id': 4, 'x': 400, 'y': 400}]
print(f"  Tracks: {tracks_2_bad} <- IDs changed!")

print("\nFrame 2 (zoom 2.0x) - With wrapper:")
tracks_2_fixed = wrapper.fix_ids(
    [{'id': 3, 'x': 200, 'y': 200}, {'id': 4, 'x': 400, 'y': 400}], 
    zoom_level=2.0
)
print(f"  Tracks: {tracks_2_fixed} <- IDs preserved!")
# test_with_existing_trackers.py
import cv2
import numpy as np
from deep_sort_realtime.deepsort_tracker import DeepSort
from src.core.zoom_tracking_wrapper import ZoomTrackingWrapper
import matplotlib.pyplot as plt

class TrackerComparison:
    """Test wrapper with different tracking algorithms"""
    
    def test_with_deepsort(self, video_path):
        """Test with DeepSORT tracker"""
        
        # Initialize trackers
        deepsort_vanilla = DeepSort(max_age=30)
        deepsort_wrapped = ZoomTrackingWrapper(DeepSort(max_age=30))
        
        # Run comparison
        vanilla_results = self.run_test(video_path, deepsort_vanilla, "DeepSORT")
        wrapped_results = self.run_test(video_path, deepsort_wrapped, "DeepSORT+Wrapper")
        
        self.plot_comparison(vanilla_results, wrapped_results)
        
    def test_with_sort(self, video_path):
        """Test with SORT tracker"""
        from sort import Sort
        
        sort_vanilla = Sort()
        sort_wrapped = ZoomTrackingWrapper(Sort())
        
        vanilla_results = self.run_test(video_path, sort_vanilla, "SORT")
        wrapped_results = self.run_test(video_path, sort_wrapped, "SORT+Wrapper")
        
        self.plot_comparison(vanilla_results, wrapped_results)
        
    def test_with_bytetrack(self, video_path):
        """Test with ByteTrack"""
        # Mock ByteTrack for demo
        class ByteTrack:
            def update(self, detections, frame):
                # Simplified mock
                tracks = []
                for i, det in enumerate(detections):
                    track = type('obj', (object,), {
                        'track_id': i,
                        'tlbr': det[:4],
                        'score': det[4] if len(det) > 4 else 0.9
                    })
                    tracks.append(track)
                return tracks
        
        byte_vanilla = ByteTrack()
        byte_wrapped = ZoomTrackingWrapper(ByteTrack())
        
        vanilla_results = self.run_test(video_path, byte_vanilla, "ByteTrack")
        wrapped_results = self.run_test(video_path, byte_wrapped, "ByteTrack+Wrapper")
        
        self.plot_comparison(vanilla_results, wrapped_results)
    
    def run_test(self, video_path, tracker, tracker_name):
        """Run tracker on video with zoom simulation"""
        
        cap = cv2.VideoCapture(video_path)
        detector = self.get_detector()
        
        results = {
            'name': tracker_name,
            'id_switches': [],
            'track_count': [],
            'zoom_levels': []
        }
        
        frame_idx = 0
        prev_ids = set()
        zoom_schedule = self.create_zoom_schedule()
        
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret or frame_idx >= len(zoom_schedule):
                break
                
            # Simulate zoom
            zoom = zoom_schedule[frame_idx]
            zoomed_frame = self.apply_zoom(frame, zoom)
            
            # Detect
            detections = detector(zoomed_frame)
            
            # Track
            if isinstance(tracker, ZoomTrackingWrapper):
                tracks = tracker.update(zoomed_frame, detections, zoom)
            else:
                tracks = tracker.update_tracks(detections, zoomed_frame)
            
            # Analyze
            current_ids = {t.track_id for t in tracks}
            
            # Count ID switches (new IDs during zoom)
            if frame_idx > 0 and abs(zoom - zoom_schedule[frame_idx-1]) > 0.1:
                new_ids = current_ids - prev_ids
                lost_ids = prev_ids - current_ids
                id_switches = len(new_ids) + len(lost_ids)
                results['id_switches'].append(id_switches)
            else:
                results['id_switches'].append(0)
                
            results['track_count'].append(len(tracks))
            results['zoom_levels'].append(zoom)
            
            prev_ids = current_ids
            frame_idx += 1
            
            # Visualize
            self.visualize_frame(zoomed_frame, tracks, zoom, tracker_name)
            
        cap.release()
        cv2.destroyAllWindows()
        
        return results
    
    def apply_zoom(self, frame, zoom_factor):
        """Apply zoom transformation"""
        h, w = frame.shape[:2]
        center_x, center_y = w // 2, h // 2
        
        # Zoom matrix
        M = cv2.getRotationMatrix2D((center_x, center_y), 0, zoom_factor)
        
        # Apply zoom
        zoomed = cv2.warpAffine(frame, M, (w, h))
        
        return zoomed
    
    def create_zoom_schedule(self):
        """Create zoom in/out sequence"""
        schedule = []
        
        # Static
        schedule.extend([1.0] * 30)
        
        # Zoom in
        for i in range(60):
            schedule.append(1.0 + 2.0 * i / 60)  # 1x to 3x
            
        # Hold
        schedule.extend([3.0] * 60)
        
        # Zoom out
        for i in range(60):
            schedule.append(3.0 - 2.0 * i / 60)  # 3x to 1x
            
        return schedule
    
    def get_detector(self):
        """Simple person detector using YOLO or HOG"""
        try:
            # Try YOLO first
            from ultralytics import YOLO
            model = YOLO('yolov8n.pt')
            
            def detect(frame):
                results = model(frame, classes=[0])  # person only
                detections = []
                for r in results:
                    if r.boxes is not None:
                        for box in r.boxes:
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            conf = box.conf[0].cpu().numpy()
                            detections.append([x1, y1, x2, y2, conf])
                return np.array(detections)
            
            return detect
            
        except:
            # Fallback to HOG
            hog = cv2.HOGDescriptor()
            hog.setSVMDetector(cv2.HOGDescriptor_getDefaultPeopleDetector())
            
            def detect(frame):
                found, _ = hog.detectMultiScale(frame)
                detections = []
                for (x, y, w, h) in found:
                    detections.append([x, y, x+w, y+h, 0.9])
                return np.array(detections)
            
            return detect
    
    def visualize_frame(self, frame, tracks, zoom, tracker_name):
        """Draw tracking results"""
        for track in tracks:
            x1, y1, x2, y2 = map(int, track.tlbr)
            
            # Color based on ID (consistent across frames)
            color = self.get_color_for_id(track.track_id)
            
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
            cv2.putText(frame, f"ID: {track.track_id}", 
                       (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 
                       0.5, color, 2)
        
        # Info overlay
        cv2.putText(frame, f"{tracker_name} | Zoom: {zoom:.1f}x", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 
                   1, (0, 255, 0), 2)
        
        cv2.imshow(tracker_name, frame)
        cv2.waitKey(1)
    
    def get_color_for_id(self, track_id):
        """Generate consistent color for track ID"""
        colors = [
            (255, 0, 0), (0, 255, 0), (0, 0, 255),
            (255, 255, 0), (255, 0, 255), (0, 255, 255),
            (128, 255, 0), (255, 128, 0), (128, 0, 255)
        ]
        return colors[track_id % len(colors)]
    
    def plot_comparison(self, vanilla_results, wrapped_results):
        """Plot comparison results"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        frames = range(len(vanilla_results['zoom_levels']))
        
        # Plot 1: Zoom levels and ID switches
        ax1.plot(frames, vanilla_results['zoom_levels'], 'b-', 
                label='Zoom Level', linewidth=2)
        ax1.set_ylabel('Zoom Level', color='b')
        ax1.tick_params(axis='y', labelcolor='b')
        
        ax1_twin = ax1.twinx()
        ax1_twin.bar(frames, vanilla_results['id_switches'], 
                    alpha=0.3, color='red', label=f"{vanilla_results['name']} ID Switches")
        ax1_twin.bar(frames, wrapped_results['id_switches'], 
                    alpha=0.3, color='green', label=f"{wrapped_results['name']} ID Switches")
        ax1_twin.set_ylabel('ID Switches')
        ax1_twin.legend()
        
        ax1.set_title('ID Switches During Zoom')
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Track count stability
        ax2.plot(frames, vanilla_results['track_count'], 'r-', 
                label=vanilla_results['name'], linewidth=2)
        ax2.plot(frames, wrapped_results['track_count'], 'g-', 
                label=wrapped_results['name'], linewidth=2)
        ax2.set_xlabel('Frame')
        ax2.set_ylabel('Active Tracks')
        ax2.set_title('Track Count Stability')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f"comparison_{vanilla_results['name']}.png")
        plt.show()
        
        # Print summary
        total_vanilla_switches = sum(vanilla_results['id_switches'])
        total_wrapped_switches = sum(wrapped_results['id_switches'])
        
        print(f"\n=== RESULTS: {vanilla_results['name']} ===")
        print(f"Without Wrapper: {total_vanilla_switches} ID switches")
        print(f"With Wrapper: {total_wrapped_switches} ID switches")
        print(f"Improvement: {(1 - total_wrapped_switches/max(total_vanilla_switches, 1)) * 100:.1f}%")


# Run tests
if __name__ == "__main__":
    tester = TrackerComparison()
    
    # Test with different trackers
    print("Testing with DeepSORT...")
    tester.test_with_deepsort("test_video.mp4")
    
    print("\nTesting with SORT...")
    tester.test_with_sort("test_video.mp4")
    
    print("\nTesting with ByteTrack...")
    tester.test_with_bytetrack("test_video.mp4")